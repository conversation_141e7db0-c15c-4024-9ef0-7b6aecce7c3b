{
  // Place your settings in this file to overwrite default and user settings.
  "workbench.iconTheme": "material-icon-theme",
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "prettier.configPath": ".prettierrc.js",
  "scss.validate": true,
  "javascript.validate.enable": false,
  "editor.formatOnSave": true,
  "editor.linkedEditing": true,
  "files.insertFinalNewline": true,
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "typescript.tsdk": "node_modules/typescript/lib",
  // Increase memory limit for tsserver so that typescript hover hints don't stop working after a while
  "typescript.tsserver.maxTsServerMemory": 4290,
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.oxc": "explicit"
  },
  "typescript.preferences.autoImportFileExcludePatterns": [
    "**/node_modules/@types/node",
    "@mobily/ts-belt/dist/types",
    "cypress/types/sinon",
    "prop-types",
    "dist/apps/**"
  ],
  "gitlens.advanced.blame.customArguments": [
    "--ignore-revs-file",
    ".git-blame-ignore-revs"
  ],
  "search.exclude": {
    "apps/fleet-web/src/_third-party-libs-forks": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.hg/store/**": true,
    "dist/apps/**": true,
    "**/_third-party-libs-forks/**": true,
    ".nx/cache/**": true,
    ".cache/**": true
  },
  "chat.mcp.enabled": true,
  "chat.mcp.discovery.enabled": true,
  "oxc.enable": true,
  "geminicodeassist.updateChannel": "Insiders",
  "typescript.experimental.useTsgo": false
}
