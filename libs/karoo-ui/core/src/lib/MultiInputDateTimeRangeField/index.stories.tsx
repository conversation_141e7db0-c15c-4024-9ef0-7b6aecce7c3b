import { useState } from 'react'
import type { Meta } from '@storybook/react'
import type { DateRange } from '@mui/x-date-pickers-pro'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { MultiInputDateTimeRangeField } from './index'

export default {
  component: MultiInputDateTimeRangeField,
} as Meta<typeof MultiInputDateTimeRangeField>

export const Default = () => {
  const [value, setValue] = useState<DateRange<DateTime>>([null, null])

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <MultiInputDateTimeRangeField
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
