import {
  MultiInputDateTimeRangeField as MuiMultiInputDateTimeRangeField,
  unstable_useMultiInputDateTimeRangeField as useMultiInputDateTimeRangeField,
} from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField'

import { forwardRefTyped } from '../forwardRefTyped'

const MultiInputDateTimeRangeField = forwardRefTyped(
  function MultiInputDateTimeRangeField(
    { slotProps, ...props },
    ref: React.Ref<HTMLInputElement>,
  ) {
    return (
      <MuiMultiInputDateTimeRangeField
        ref={ref}
        slotProps={{
          ...slotProps,
          textField: { size: 'small', ...slotProps?.textField },
        }}
        {...props}
      />
    )
  },
) as typeof MuiMultiInputDateTimeRangeField

export { useMultiInputDateTimeRangeField, MultiInputDateTimeRangeField }
export type {
  MultiInputDateTimeRangeFieldProps,
  UseMultiInputDateTimeRangeFieldComponentProps,
  UseMultiInputDateTimeRangeFieldProps,
} from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField'
