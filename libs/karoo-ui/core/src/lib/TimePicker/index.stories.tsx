import { useState } from 'react'
import type { Meta } from '@storybook/react'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { Typography } from '../Typography'
import { TimePicker } from './index'

export default {
  component: TimePicker,
} as Meta<typeof TimePicker>

const initialDate = new Date()
initialDate.setHours(17, 0, 45, 0)

export const Default = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))

  const adapterLocale = 'pt-PT'

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={adapterLocale}
    >
      <div>
        <TimePicker
          value={value}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
        <Typography> Using locale {adapterLocale}</Typography>
      </div>
    </LocalizationProvider>
  )
}

export const WithHoursMinutesAndSecondsInPT = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={'pt-PT'}
    >
      <div>
        <TimePicker
          value={value}
          views={['hours', 'minutes', 'seconds']}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
      </div>
    </LocalizationProvider>
  )
}

export const WithHoursMinutesAndSecondsInEnUS = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={'en-US'}
    >
      <div>
        <TimePicker
          value={value}
          views={['hours', 'minutes', 'seconds']}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
      </div>
    </LocalizationProvider>
  )
}
