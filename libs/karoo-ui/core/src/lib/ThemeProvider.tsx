/* eslint-disable @typescript-eslint/consistent-type-definitions */
import type {} from '@mui/x-date-pickers-pro/themeAugmentation'
import type {} from '@mui/x-data-grid/themeAugmentation'
import type {} from '../typings/themeAugmentation'

import { useMemo } from 'react'
import { formLabelClasses } from '@mui/material/FormLabel'
import { outlinedInputClasses } from '@mui/material/OutlinedInput'
import {
  alpha,
  createTheme,
  ThemeProvider as MuiThemeProvider,
  useTheme,
  type ComponentsPropsList,
  type Theme as MuiTheme,
  type Palette,
  type TransitionsOptions,
} from '@mui/material/styles'

import { KarooExtendedLocalesProvider } from './KarooExtendedLocalesContext'
import { locale_enUS, type KarooUiLocale } from './locale'

export type KarooUiCustomizableTheme = {
  palette: Pick<Palette, 'primary' | 'secondary'>
}

export type KarooUiInternalTheme = MuiTheme

/**
 * @description
 * These colors were defined by our designers for our default theme
 */
export const defaultCustomizableTheme: KarooUiCustomizableTheme = {
  palette: {
    primary: {
      main: '#F47735',
      dark: '#BB4800',
      light: '#FFA863',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#333333',
      dark: '#0C0C0C',
      light: '#5C5C5C',
      contrastText: '#FFFFFF',
    },
  },
}

type ThemeProviderProps = {
  theme?: KarooUiCustomizableTheme | null
  transitions?: Pick<TransitionsOptions, 'create'>
  locale?: KarooUiLocale
  children?: React.ReactNode
}

function ThemeProvider({
  theme: themeWithoutDefault,
  locale: localeWithoutDefault,
  transitions,
  children,
}: ThemeProviderProps) {
  const theme = themeWithoutDefault ?? defaultCustomizableTheme
  const locale = localeWithoutDefault ?? locale_enUS

  const muiTheme = useMemo((): KarooUiInternalTheme => {
    // Mixed Locales configuration based on https://mui.com/components/data-grid/localization/#locale-text

    const htmlFontSize = 14
    const createTypography = () => {
      return {
        /** Needs to be kept in sync with html { font-size: ... }.
         * Very important so that mui keeps the original font-sizes for typography variants like caption, body1, etc
         */
        htmlFontSize,
        fontSize: htmlFontSize,
      }
    }

    const computedTheme = createTheme(
      {
        cssVariables: true,
        transitions,
        typography: createTypography(),
        palette: {
          ...theme.palette,
          // Properties that are not meant to be customizable for white labels
          success: {
            main: '#4CAF50',
            dark: '#388E3C',
            light: '#81C784',
            contrastText: '#FFFFFF',
          },
          error: {
            main: '#F44336',
            dark: '#D32F2F',
            light: '#E57373',
            contrastText: '#FFFFFF',
          },
          warning: {
            main: '#FF9800',
            dark: '#F57C00',
            light: '#FFB74D',
            contrastText: '#FFFFFF',
          },
          info: {
            main: '#2196F3',
            dark: '#1976D2',
            light: '#64B5F6',
            contrastText: '#FFFFFF',
          },
        },
        components: {
          ...locale.karoo.components,
          MuiAutocomplete: {
            styleOverrides: {
              paper: {
                fontSize: '1rem',
              },
            },
          },
          MuiTypography: {
            defaultProps: {
              variant: 'body2',
            },
          },
          MuiDialogContentText: {
            defaultProps: {
              variant: 'body2',
            },
          },
          MuiListItemText: {
            defaultProps: {
              primaryTypographyProps: {
                variant: 'body2',
              },
            },
          },
          MuiMenuItem: {
            styleOverrides: {
              root: {
                fontSize: '1rem',
              },
            },
          },
          MuiCheckbox: {
            defaultProps: {
              size: 'small',
            },
          },
          MuiRadio: {
            defaultProps: {
              size: 'small',
            },
          },
          MuiSwitch: {
            defaultProps: {
              size: 'small',
            },
          },
          MuiChip: {
            defaultProps: {
              size: 'small',
            },
          },
          MuiDataGrid: {
            styleOverrides: {
              root: {
                // Mui uses under 1rem font size for the data grid, which is too small when we use base font size of 14px
                fontSize: htmlFontSize,
              },
            },
          },
          MuiDialogContent: {
            styleOverrides: {
              root: {
                overflow: 'visible',
              },
            },
          },
          MuiIconButton: {
            styleOverrides: {
              root: ({
                ownerState,
              }: {
                ownerState: ComponentsPropsList['MuiIconButton']
              }) => ({
                ...(ownerState.disableRipple && {
                  padding: 0,
                }),
              }),
            },
          },
          MuiToggleButtonGroup: {
            styleOverrides: {
              root: {
                '& .MuiToggleButton-root': {
                  borderColor: '#BDBDBD',

                  '&.Mui-selected, &.Mui-selected:hover': {
                    color: theme.palette.primary.contrastText,
                    backgroundColor: theme.palette.primary.main,
                  },
                },
              },
            },
          },
          MuiOutlinedInput: {
            styleOverrides: {
              root: (() => {
                // Declared here so that the objects references are stable across renders
                const readonlyFocusedStyles = {
                  '&.Mui-focused': {
                    [`& .${outlinedInputClasses.notchedOutline}`]: {
                      border: `1px solid rgba(0, 0, 0, 0.23)`,
                    },
                  },
                }
                return ({ ownerState }) => {
                  if (ownerState.readOnly) {
                    return readonlyFocusedStyles
                  }
                  return undefined
                }
              })(),
            },
          },
          MuiInputLabel: {
            styleOverrides: {
              root: {
                fontSize: '1rem',
                '&[data-shrink="true"]': {
                  fontSize: '1.08rem',
                },
              },
            },
          },
          MuiInputBase: {
            styleOverrides: {
              root: (() => {
                // Declared here so that the objects references are stable across renders
                const baseStyles = { fontSize: '1rem' }
                const readonlyStyles = {
                  ...baseStyles,
                  [`&:not(.Mui-focused)`]: {
                    '&:hover': {
                      [`& .${outlinedInputClasses.notchedOutline}`]: {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                    },
                  },
                }
                return ({ ownerState }) => {
                  if (ownerState.readOnly) {
                    return readonlyStyles
                  }
                  return baseStyles
                }
              })(),
            },
          },
          MuiFormLabel: {
            styleOverrides: {
              root: {
                [`&.${formLabelClasses.focused}`]: {
                  color: 'rgba(0, 0, 0, 0.6)',
                },
              },
              asterisk: ({ ownerState, theme }) => {
                if (ownerState.disabled && !ownerState.error) {
                  // Only use grey color when the field is disabled and not in error state.
                  // If it's in error state, we want to use the error color (to maintain mui consistency)
                  return { color: theme.palette.text.disabled }
                }
                return { color: '#db3131' }
              },
            },
          },
        },
      },
      locale.dataGrid,
      locale.datePickers,
      locale.base,
    )

    const alphaValues = {
      selected: computedTheme.palette.action.selectedOpacity,
      hover: computedTheme.palette.action.hoverOpacity,
      focus: computedTheme.palette.action.focusOpacity,
    }

    return {
      ...computedTheme,
      palette: {
        ...computedTheme.palette,
        states: {
          primary: {
            selected: alpha(theme.palette.primary.main, alphaValues.selected),
            hover: alpha(theme.palette.primary.main, alphaValues.hover),
            focus: alpha(theme.palette.primary.main, alphaValues.focus),
          },
          secondary: {
            selected: alpha(theme.palette.secondary.main, alphaValues.selected),
            hover: alpha(theme.palette.secondary.main, alphaValues.hover),
            focus: alpha(theme.palette.secondary.main, alphaValues.focus),
          },
        },
      },
    }
  }, [locale, theme.palette, transitions])

  return (
    <MuiThemeProvider theme={muiTheme}>
      <KarooExtendedLocalesProvider value={locale.karoo.extended}>
        {children}
      </KarooExtendedLocalesProvider>
    </MuiThemeProvider>
  )
}

export { ThemeProvider, type ThemeProviderProps, useTheme }
