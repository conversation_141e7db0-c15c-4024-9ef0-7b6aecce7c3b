import { useEffect, useMemo, useRef, useState } from 'react'
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded'
import KeyboardArrowRightRoundedIcon from '@mui/icons-material/KeyboardArrowRightRounded'
import {
  DataGridPremium as DataGridMui,
  gridClasses,
  useGridApiRef,
  type DataGridPremiumProps as DataGridMuiProps,
  type GridEventListener,
  type GridRowHeightReturnValue,
  type GridRowIdGetter,
  type GridRowParams,
  type GridValidRowModel,
} from '@mui/x-data-grid-premium'
import * as R from 'remeda'
import type { Except } from 'type-fest'

import { styled } from '../styled'
import type { CallbackBranded } from '../useCallbackBranded'
import type { MemoBranded } from '../useMemoBranded'
import type { GridFilterModel } from './overrides'
import type { ColumnHelperDef } from './public-utils'
import type { GridRowHeightParams } from './types-overrides-public'

type RemoveArrayFirstElement<A extends Array<any>> = A extends [any, ...infer Rest]
  ? Rest
  : []

export type DataGridBaseProps<R extends GridValidRowModel = any> = Except<
  DataGridMuiProps<R>,
  | 'columns'
  | 'onFilterModelChange'
  | 'getTreeDataPath'
  | 'getRowId'
  | 'getRowHeight'
  | 'groupingColDef'
  | 'onRowClick'
> & {
  columns: ReadonlyArray<
    | DataGridMuiProps<R>['columns'][number]
    | ColumnHelperDef<Except<DataGridMuiProps<R>['columns'][number], 'valueGetter'>>
  >
  onFilterModelChange?: (
    // Use our GridFilterModel improved type instead of the one from mui-x
    model: GridFilterModel,
    ...rest: RemoveArrayFirstElement<
      Parameters<NonNullable<DataGridMuiProps<R>['onFilterModelChange']>>
    >
  ) => void

  onRowClick?: (
    // As of "@mui/x-data-grid": "7.28.3", params type is GridRowParams<any>. We want better type safety
    params: GridRowParams<R>,
    event: Parameters<GridEventListener<'rowClick'>>[1],
    details: Parameters<GridEventListener<'rowClick'>>[2],
  ) => void

  /**
   * Determines the path of a row in the tree data.
   * For instance, a row with the path ["A", "B"] is the child of the row with the path ["A"].
   * Note that all paths must contain at least one element.
   * @template R
   * @param {R} row The row from which we want the path.
   * @returns {string[]} The path to the row.
   *
   * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
   *
   * __Reason:__ `getTreeDataPath` should keep the same reference between two renders. If it changes, the Data Grid will consider that the data has changed and will recompute the tree resulting in collapsing all the rows.
   */
  getTreeDataPath?: CallbackBranded<
    Exclude<DataGridMuiProps<R>['getTreeDataPath'], undefined>
  >

  /**
   * Return the id of a given [[GridRowModel]].
   * Ensure the reference of this prop is stable to avoid performance implications.
   * It could be done by either defining the prop outside of the component or by memoizing it.
   *
   * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
   */
  getRowId?: CallbackBranded<GridRowIdGetter<R>>

  /**
   * Function that sets the row height per row.
   * @returns {GridRowHeightReturnValue} The row height value. If `null` or `undefined` then the default row height is applied. If "auto" then the row height is calculated based on the content.
   *
   * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
   */
  getRowHeight?: CallbackBranded<
    (params: GridRowHeightParams<R>) => GridRowHeightReturnValue
  >

  /**
   * The grouping column used by the tree data.
   *
   * __IMPORTANT:__ Needs to be used with `useMemoBranded`
   */
  groupingColDef?: MemoBranded<NonNullable<DataGridMuiProps<R>['groupingColDef']>>
}

export function DataGridBase<R extends GridValidRowModel>({
  slots,
  slotProps,
  columns: columnsProp_,
  sx: sxProp,
  apiRef: apiRefProp,
  onFilterModelChange,
  getRowHeight,
  groupingColDef,
  onRowClick,
  ...restProps
}: DataGridBaseProps<R>) {
  const columnsProp = useMemo(
    () =>
      columnsProp_.map((column) => ({
        ...column,
        /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
        // Set default __groupable__ and __aggregable__ to false. These features are not yet stable to be used by default for __every__ column. It specifically causes issues with the `singleSelect` columns.
        // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
        groupable: column.groupable ?? false,
        aggregable: column.aggregable ?? false,
        display: column.display ?? 'flex',
      })),
    [columnsProp_],
  )

  const [stableColumns, setStableColumns] = useState<typeof columnsProp_>(columnsProp)

  const _gridApiRef = useGridApiRef()
  const apiRef = apiRefProp ?? _gridApiRef

  const prevColumnsProp = usePrevious(columnsProp)
  const columnHasBeenRemoved = useMemo((): boolean => {
    if (prevColumnsProp === undefined) {
      return false
    }
    return prevColumnsProp.some((stableColumn) => {
      const propColumnExistsInProp = columnsProp.some(
        (column) => stableColumn.field === column.field,
      )
      return !propColumnExistsInProp
    })
  }, [columnsProp, prevColumnsProp])

  useEffect(() => {
    if (columnHasBeenRemoved) {
      // If a column has been removed, we reset the stable columns to the prop columns.
      // apiRef.current.updateColumns is not enough because it doesn't remove columns from the grid, only upserts them.
      setStableColumns(columnsProp)
      return
    }

    // We check for the existence of the `updateColumns` method to avoid a crash. DataGrid lazy loads some methods like `updateColumns` which are not available immediately.
    if (apiRef.current && apiRef.current.updateColumns) {
      // Fixes https://gitlab.cartrack.com/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/679
      apiRef.current.updateColumns(columnsProp)
    }
  }, [columnsProp, apiRef, columnHasBeenRemoved])

  const filterPanelSlotPropsSx = slotProps?.filterPanel?.sx ?? []
  const sx = sxProp ?? []

  return (
    <StyledDataGridMui<R>
      {...restProps}
      onRowClick={onRowClick}
      getRowHeight={getRowHeight as DataGridMuiProps<R>['getRowHeight']}
      groupingColDef={groupingColDef as DataGridMuiProps<R>['groupingColDef']}
      apiRef={apiRef}
      /* From mui docs https://mui.com/x/react-data-grid/column-definition/:
           The columns prop should keep the same reference between two renders. The columns are designed to be definitions, to never change once the component is mounted.
           Otherwise, you take the risk of losing elements like column width or order. You can create the array outside the render function or memoize it.
      */
      columns={stableColumns} // Very important to keep a stable columns reference to prevent state reset bugs
      onFilterModelChange={onFilterModelChange as any}
      sx={[
        {
          [`& .${gridClasses.cell}:focus, & .${gridClasses.cell}:focus-within`]: {
            outline: 'none',
          },
          [`& .${gridClasses.columnHeader}:focus, & .${gridClasses.columnHeader}:focus-within`]:
            {
              outline: 'none',
            },
          ...(restProps.disableRowSelectionOnClick && !onRowClick
            ? {
                '& .MuiDataGrid-row:hover': {
                  backgroundColor: 'inherit',
                },
              }
            : {}),
        },
        ...(R.isArray(sx) ? sx : [sx]),
      ]}
      slots={{
        detailPanelExpandIcon: KeyboardArrowRightRoundedIcon,
        detailPanelCollapseIcon: KeyboardArrowDownRoundedIcon,
        ...slots,
      }}
      slotProps={{
        ...slotProps,
        filterPanel: {
          ...slotProps?.filterPanel,
          sx: [
            {
              /* Increase the width of the filter panel to accommodate the new datetime picker input (when using am/pm)
                 This makes sure that there is enough space to show the full date and time.
                 The reason we do this to ALL filter panel input values is to maintain consistency between all input types.
              */
              width: '580px', // (default=510px) + 70px
              minWidth: '320px', // Allow shrinking if window is resized horizontally
              '& .MuiDataGrid-filterFormValueInput': {
                width: '225px', // (default=190px) + 35px
              },
            },
            ...(R.isArray(filterPanelSlotPropsSx)
              ? filterPanelSlotPropsSx
              : [filterPanelSlotPropsSx]),
          ],
        },
      }}
    />
  )
}

const StyledDataGridMui = styled(DataGridMui)({
  border: 'none',
  '& .MuiDataGrid-row.Mui-selected': {
    backgroundColor: 'rgb(91 91 91 / 13.5%)',
  },
  '& .MuiDataGrid-row.Mui-selected:hover': {
    backgroundColor: 'rgb(91 91 91 / 20.5%)',
  },
  '& .actionHeader:last-child .MuiDataGrid-iconSeparator': {
    display: 'none',
  },
  '& .MuiDataGrid-main': {
    // Solves https://cartrack.atlassian.net/browse/FTW-8383
    // Without it, if the grid at some point was resized vertically in way where it's height is near zero, mui would crash.
    minHeight: '130px',
  },
}) as typeof DataGridMui

/**
 * Custom hook to get previous value
 *
 * @returns Returns previous value (from last render)
 * @example
 * function App() {
 *  // State value and setter for our example
 *  const [count, setCount] = useState(0);
 *
 *  // Get the previous value (was passed into hook on last render)
 *  const prevCount = usePrevious(count);
 *
 *  // Display both current and previous count value
 *  return (
 *    <>
 *      <h1>Now: {count}, before: {prevCount}</h1>
 *    </>
 *   );
 * }
 *
 */
function usePrevious<T>(value: T | undefined): T | undefined {
  const ref: {
    current: T | undefined
  } = useRef(undefined)

  // Store current value in ref
  useEffect(() => {
    ref.current = value
  }, [value])

  // Return previous value (happens before update in useEffect above)
  return ref.current
}
