import { useState } from 'react'
import type { Meta } from '@storybook/react'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { DatePicker } from './index'

export default {
  component: DatePicker,
} as Meta<typeof DatePicker>

export const Default = () => {
  const [value, setValue] = useState<DateTime | null>(null)

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <DatePicker
        label="Basic example"
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
