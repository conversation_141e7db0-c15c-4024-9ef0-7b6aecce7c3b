import { useState } from 'react'
import type { Meta } from '@storybook/react'
import type { DateRange } from '@mui/x-date-pickers-pro'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { Typography } from '../Typography'
import { MultiInputTimeRangeField } from './index'

export default {
  component: MultiInputTimeRangeField,
} as Meta<typeof MultiInputTimeRangeField>

const startInitialDate = new Date()
startInitialDate.setHours(17, 0, 0, 0)

const endInitialDate = new Date()
endInitialDate.setHours(19, 4, 0, 0)

export const Default = () => {
  const [value, setValue] = useState<DateRange<DateTime>>([
    DateTime.fromJSDate(startInitialDate),
    DateTime.fromJSDate(endInitialDate),
  ])

  const adapterLocale = 'pt-PT'

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={adapterLocale}
    >
      <div>
        <MultiInputTimeRangeField
          value={value}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />

        <Typography> Using locale {adapterLocale}</Typography>
      </div>
    </LocalizationProvider>
  )
}
