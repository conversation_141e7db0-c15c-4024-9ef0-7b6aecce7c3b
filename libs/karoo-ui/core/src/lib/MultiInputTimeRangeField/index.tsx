import {
  MultiInputTimeRange<PERSON>ield as MuiMultiInputTimeRangeField,
  unstable_useMultiInputTimeRangeField as useMultiInputTimeRangeField,
} from '@mui/x-date-pickers-pro/MultiInputTimeRangeField'

import { forwardRefTyped } from '../forwardRefTyped'

const MultiInputTimeRangeField = forwardRefTyped(function MultiInputTimeRangeField(
  { slotProps, ...props },
  ref: React.Ref<HTMLInputElement>,
) {
  return (
    <MuiMultiInputTimeRangeField
      ref={ref}
      slotProps={{
        ...slotProps,
        textField: { size: 'small', ...slotProps?.textField },
      }}
      {...props}
    />
  )
}) as typeof MuiMultiInputTimeRangeField

export { useMultiInputTimeRangeField, MultiInputTimeRangeField }
export type {
  MultiInputTimeRangeFieldProps,
  UseMultiInputTimeRangeFieldComponentProps,
  UseMultiInputTimeRangeFieldProps,
} from '@mui/x-date-pickers-pro/MultiInputTimeRangeField'
