import {
  DateTimeField as MuiDateTimeField,
  unstable_useDateTimeField as useDateTimeField,
} from '@mui/x-date-pickers/DateTimeField'

import { forwardRefTyped } from '../forwardRefTyped'

const DateTimeField = forwardRefTyped(function DateTimeField(
  { slotProps, ...props },
  ref,
) {
  return (
    <MuiDateTimeField
      ref={ref}
      slotProps={{
        ...slotProps,
        textField: { size: 'small', ...slotProps?.textField },
      }}
      {...props}
    />
  )
}) as typeof MuiDateTimeField

export { useDateTimeField, DateTimeField }
export type {
  DateTimeFieldProps,
  UseDateTimeFieldComponentProps,
  UseDateTimeFieldProps,
} from '@mui/x-date-pickers/DateTimeField'
