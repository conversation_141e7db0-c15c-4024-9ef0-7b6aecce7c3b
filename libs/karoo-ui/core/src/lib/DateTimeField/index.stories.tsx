import { useState } from 'react'
import type { Meta } from '@storybook/react'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { DateTimeField } from './index'

export default {
  component: DateTimeField,
} as Meta<typeof DateTimeField>

export const Default = () => {
  const [value, setValue] = useState<DateTime | null>(null)

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <DateTimeField
        label="Basic example"
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
