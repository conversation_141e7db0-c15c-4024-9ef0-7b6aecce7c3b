import { useState } from 'react'
import type { Meta } from '@storybook/react'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { DateField } from './index'

export default {
  component: DateField,
} as Meta<typeof DateField>

export const Default = () => {
  const [value, setValue] = useState<DateTime | null>(null)

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <DateField
        label="Basic date field"
        value={value}
        onChange={(newValue) => setValue(newValue)}
      />
    </LocalizationProvider>
  )
}
