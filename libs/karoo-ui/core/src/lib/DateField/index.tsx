import { DateField as MuiDateField } from '@mui/x-date-pickers/DateField'

import { forwardRefTyped } from '../forwardRefTyped'

export const DateField = forwardRefTyped(function DateField(
  { size = 'small', ...props },
  ref,
) {
  return (
    <MuiDateField
      size={size}
      ref={ref}
      {...props}
    />
  )
}) as typeof MuiDateField

export { unstable_useDateField as useDateField } from '@mui/x-date-pickers/DateField'
export type {
  UseDateFieldComponentProps,
  UseDateFieldProps,
  DateFieldProps,
} from '@mui/x-date-pickers/DateField'
