import { useState } from 'react'
import type { Meta } from '@storybook/react'
import type { DateRange } from '@mui/x-date-pickers-pro'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { DateTimeRangePicker } from './index'

export default {
  component: DateTimeRangePicker,
} as Meta<typeof DateTimeRangePicker>

export const Default = () => {
  const [value, setValue] = useState<DateRange<DateTime>>(() => [
    DateTime.fromJSDate(new Date('2022-04-07')),
    DateTime.fromJSDate(new Date('2022-04-10')),
  ])

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <DateTimeRangePicker
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
