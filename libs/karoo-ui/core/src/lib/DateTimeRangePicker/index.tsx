// eslint-disable-next-line no-restricted-imports
import type { TextFieldProps, TypographyProps } from '@mui/material'
import { DateTimeRangePicker as MuiDateTimeRangePicker } from '@mui/x-date-pickers-pro/DateTimeRangePicker'
import * as R from 'remeda'

import { forwardRefTyped } from '../forwardRefTyped'

export const DateTimeRangePicker = forwardRefTyped(function DateTimeRangePicker(
  {
    slotProps,
    /** Our users prefer this behaviour and is less confusing */
    disableAutoMonthSwitching = true,
    timeSteps = { hours: 1, minutes: 1, seconds: 1 },
    ...props
  },
  ref,
) {
  const fieldSeparatorProps = slotProps?.fieldSeparator as TypographyProps | undefined
  const fieldSeparatorSx = fieldSeparatorProps?.sx ?? []

  const textFieldProps = slotProps?.textField as TextFieldProps | undefined
  const textFieldSx = textFieldProps?.sx ?? []
  return (
    <MuiDateTimeRangePicker
      ref={ref}
      slotProps={{
        ...slotProps,
        fieldSeparator: {
          ...fieldSeparatorProps,
          sx: [
            { marginLeft: '12px !important', marginRight: '12px !important' },
            ...(R.isArray(fieldSeparatorSx) ? fieldSeparatorSx : [fieldSeparatorSx]),
          ],
        },
        textField: {
          size: 'small',
          ...textFieldProps,
          sx: [
            { ml: '0px !important' },
            ...(R.isArray(textFieldSx) ? textFieldSx : [textFieldSx]),
          ],
        },
      }}
      disableAutoMonthSwitching={disableAutoMonthSwitching}
      timeSteps={timeSteps}
      {...props}
    />
  )
}) as typeof MuiDateTimeRangePicker

export type {
  DateTimeRangePickerProps,
  DateTimeRangePickerSlots,
  DateTimeRangePickerSlotProps,
} from '@mui/x-date-pickers-pro/DateTimeRangePicker'
