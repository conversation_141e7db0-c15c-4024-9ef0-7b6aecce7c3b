import { useState } from 'react'
import type { Meta } from '@storybook/react'
import type { DateRange } from '@mui/x-date-pickers-pro'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { SingleInputDateTimeRangeField } from './index'

export default {
  component: SingleInputDateTimeRangeField,
} as Meta<typeof SingleInputDateTimeRangeField>

export const Default = () => {
  const [value, setValue] = useState<DateRange<DateTime>>([null, null])

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <SingleInputDateTimeRangeField
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
