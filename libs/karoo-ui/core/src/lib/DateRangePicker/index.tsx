// eslint-disable-next-line no-restricted-imports
import type { TextFieldProps, TypographyProps } from '@mui/material'
import type {
  DateRange,
  NonEmptyDateRange,
  PickersShortcutsProps,
  PickerValidDate,
} from '@mui/x-date-pickers-pro'
import { DateRangePicker as MuiDateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker'
import * as R from 'remeda'

import { forwardRefTyped } from '../forwardRefTyped'

export const DateRangePicker = forwardRefTyped(function DateRangePicker(
  {
    slotProps,
    /** Our users prefer this behaviour and is less confusing */
    disableAutoMonthSwitching = true,
    ...props
  },
  ref,
) {
  const fieldSeparatorProps = slotProps?.fieldSeparator as TypographyProps | undefined
  const fieldSeparatorSx = fieldSeparatorProps?.sx ?? []

  const textFieldProps = slotProps?.textField as TextFieldProps | undefined
  const textFieldSx = textFieldProps?.sx ?? []
  return (
    <MuiDateRangePicker
      ref={ref}
      slotProps={{
        ...slotProps,
        fieldSeparator: {
          ...fieldSeparatorProps,
          sx: [
            { marginLeft: '12px !important', marginRight: '12px !important' },
            ...(R.isArray(fieldSeparatorSx) ? fieldSeparatorSx : [fieldSeparatorSx]),
          ],
        },
        textField: {
          size: 'small',
          ...textFieldProps,
          sx: [
            { ml: '0px !important' },
            ...(R.isArray(textFieldSx) ? textFieldSx : [textFieldSx]),
          ],
        },
      }}
      disableAutoMonthSwitching={disableAutoMonthSwitching}
      {...props}
    />
  )
}) as typeof MuiDateRangePicker

export function isNonEmptyDateRange<TDate extends PickerValidDate>(
  dateRange: DateRange<TDate>,
): dateRange is NonEmptyDateRange<TDate> {
  return dateRange[0] != null && dateRange[1] != null
}

export type { DateRange, PickersShortcutsProps, PickerValidDate, NonEmptyDateRange }
export type {
  DateRangePickerProps,
  DateRangePickerSlots,
  DateRangePickerSlotProps,
} from '@mui/x-date-pickers-pro/DateRangePicker'
