import { useState } from 'react'
import type { Meta } from '@storybook/react'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { Typography } from '../Typography'
import { DateTimePicker } from './index'

export default {
  component: DateTimePicker,
} as Meta<typeof DateTimePicker>

const initialDate = new Date(2023, 11, 17)
initialDate.setHours(17, 0, 45, 0)

export const Default = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))
  const adapterLocale = 'pt-PT'

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={adapterLocale}
    >
      <div>
        <DateTimePicker
          label="Basic example"
          value={value}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
        <Typography> Using locale {adapterLocale}</Typography>
      </div>
    </LocalizationProvider>
  )
}

export const WithSecondsInPtPT = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={'pt-PT'}
    >
      <div>
        <DateTimePicker
          label="Basic example"
          value={value}
          views={['year', 'day', 'hours', 'minutes', 'seconds']}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
      </div>
    </LocalizationProvider>
  )
}

export const WithSecondsInEnUS = () => {
  const [value, setValue] = useState<DateTime | null>(DateTime.fromJSDate(initialDate))

  return (
    <LocalizationProvider
      dateAdapter={AdapterLuxon}
      adapterLocale={'en-US'}
    >
      <div>
        <DateTimePicker
          label="Basic example"
          value={value}
          views={['year', 'day', 'hours', 'minutes', 'seconds']}
          onChange={(newValue) => {
            setValue(newValue)
          }}
        />
      </div>
    </LocalizationProvider>
  )
}
