import { TimeField as MuiT<PERSON>Field } from '@mui/x-date-pickers/TimeField'

import { forwardRefTyped } from '../forwardRefTyped'
import { useKarooFormStateContext } from '../KarooFormStateContext'

export const TimeField = forwardRefTyped(function TimeField(
  { slotProps, disabled: disabledProp, readOnly: readOnlyProp, ...props },
  ref,
) {
  const formState = useKarooFormStateContext()
  const disabled = disabledProp ?? formState?.disabled
  const readOnly = readOnlyProp ?? formState?.readOnly
  return (
    <MuiTimeField
      ref={ref}
      disabled={disabled}
      readOnly={readOnly}
      slotProps={{
        ...slotProps,
        textField: { size: 'small', ...slotProps?.textField },
      }}
      {...props}
    />
  )
}) as typeof MuiTimeField

export type {
  TimeFieldProps,
  UseTimeFieldComponentProps,
  UseTimeFieldProps,
} from '@mui/x-date-pickers/TimeField'
export { unstable_useTimeField as useTimeField } from '@mui/x-date-pickers/TimeField'
