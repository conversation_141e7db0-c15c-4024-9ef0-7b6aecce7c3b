import { useState } from 'react'
import type { Meta } from '@storybook/react'
import type { DateRange } from '@mui/x-date-pickers-pro'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import type { DateTime } from 'luxon'

import { LocalizationProvider } from '../LocalizationProvider'
import { SingleInputTimeRangeField } from './index'

export default {
  component: SingleInputTimeRangeField,
} as Meta<typeof SingleInputTimeRangeField>

export const Default = () => {
  const [value, setValue] = useState<DateRange<DateTime>>([null, null])

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <SingleInputTimeRangeField
        value={value}
        onChange={(newValue) => {
          setValue(newValue)
        }}
      />
    </LocalizationProvider>
  )
}
