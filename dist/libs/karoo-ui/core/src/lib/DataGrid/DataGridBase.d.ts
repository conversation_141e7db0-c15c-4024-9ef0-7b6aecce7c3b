import { type DataGridPremiumProps as DataGridMuiProps, type GridEventListener, type GridRowHeightReturnValue, type GridRowIdGetter, type GridRowParams, type GridValidRowModel } from '@mui/x-data-grid-premium';
import type { Except } from 'type-fest';
import type { CallbackBranded } from '../useCallbackBranded';
import type { MemoBranded } from '../useMemoBranded';
import type { GridFilterModel } from './overrides';
import type { ColumnHelperDef } from './public-utils';
import type { GridRowHeightParams } from './types-overrides-public';
type RemoveArrayFirstElement<A extends Array<any>> = A extends [any, ...infer Rest] ? Rest : [];
export type DataGridBaseProps<R extends GridValidRowModel = any> = Except<DataGridMuiProps<R>, 'columns' | 'onFilterModelChange' | 'getTreeDataPath' | 'getRowId' | 'getRowHeight' | 'groupingColDef' | 'onRowClick'> & {
    columns: ReadonlyArray<DataGridMuiProps<R>['columns'][number] | ColumnHelperDef<Except<DataGridMuiProps<R>['columns'][number], 'valueGetter'>>>;
    onFilterModelChange?: (model: GridFilterModel, ...rest: RemoveArrayFirstElement<Parameters<NonNullable<DataGridMuiProps<R>['onFilterModelChange']>>>) => void;
    onRowClick?: (params: GridRowParams<R>, event: Parameters<GridEventListener<'rowClick'>>[1], details: Parameters<GridEventListener<'rowClick'>>[2]) => void;
    /**
     * Determines the path of a row in the tree data.
     * For instance, a row with the path ["A", "B"] is the child of the row with the path ["A"].
     * Note that all paths must contain at least one element.
     * @template R
     * @param {R} row The row from which we want the path.
     * @returns {string[]} The path to the row.
     *
     * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
     *
     * __Reason:__ `getTreeDataPath` should keep the same reference between two renders. If it changes, the Data Grid will consider that the data has changed and will recompute the tree resulting in collapsing all the rows.
     */
    getTreeDataPath?: CallbackBranded<Exclude<DataGridMuiProps<R>['getTreeDataPath'], undefined>>;
    /**
     * Return the id of a given [[GridRowModel]].
     * Ensure the reference of this prop is stable to avoid performance implications.
     * It could be done by either defining the prop outside of the component or by memoizing it.
     *
     * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
     */
    getRowId?: CallbackBranded<GridRowIdGetter<R>>;
    /**
     * Function that sets the row height per row.
     * @returns {GridRowHeightReturnValue} The row height value. If `null` or `undefined` then the default row height is applied. If "auto" then the row height is calculated based on the content.
     *
     * __IMPORTANT:__ Needs to be used with `useCallbackBranded`
     */
    getRowHeight?: CallbackBranded<(params: GridRowHeightParams<R>) => GridRowHeightReturnValue>;
    /**
     * The grouping column used by the tree data.
     *
     * __IMPORTANT:__ Needs to be used with `useMemoBranded`
     */
    groupingColDef?: MemoBranded<NonNullable<DataGridMuiProps<R>['groupingColDef']>>;
};
export declare function DataGridBase<R extends GridValidRowModel>({ slots, slotProps, columns: columnsProp_, sx: sxProp, apiRef: apiRefProp, onFilterModelChange, getRowHeight, groupingColDef, onRowClick, ...restProps }: DataGridBaseProps<R>): import("react/jsx-runtime").JSX.Element;
export {};
