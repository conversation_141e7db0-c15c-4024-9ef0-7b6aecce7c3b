import type { GridValidRowModel } from '@mui/x-data-grid';
import type { GridA<PERSON> } from '@mui/x-data-grid-premium';
import type { Except, Tagged } from 'type-fest';
import { type DataGridDateColDef, type DataGridDateTimeColDef } from './date-columns';
import type { GridBaseColDef, GridSingleSelectColDef } from './types-overrides-public';
/**
 * Based on type-fest's IsAny
 * @see https://github.com/sindresorhus/type-fest/blob/main/source/is-any.d.ts
 */
export type IfIsAny<T, Then, Else> = 0 extends 1 & T ? Then : Else;
type BaseColumn<R extends GridValidRowModel, V, F> = GridBaseColDef<R, IfIsAny<V, unknown, V>, IfIsAny<F, unknown, F>>;
/**
 * Super type-safe util to create base columns.
 * Prevents leaking dangerous __any__ types.
 */
export declare const createDataGridBaseColumn: <R extends GridValidRowModel, V, F = V>(column: BaseColumn<R, V, F>) => BaseColumn<R, V, F>;
export type ColumnHelperDef<ColDef> = Tagged<ColDef, 'createColumnHelper'>;
/**
 * Inspired by Tanstack Table createColumnHelper util.
 */
export declare function createDataGridColumnHelper<R extends GridValidRowModel>(): {
    valueGetter: <V, F>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, column: Except<GridBaseColDef<R, V, F>, "valueGetter">) => ColumnHelperDef<{
        type?: import("@mui/x-data-grid").GridColType | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    singleSelect: <V, const ValueOption extends V | {
        value: V;
        label: string;
    }, F = V>(valueGetter: (value: never, row: R, column: GridSingleSelectColDef<unknown, R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, column: Except<GridSingleSelectColDef<ValueOption, R, V, F>, "valueGetter" | "type">) => ColumnHelperDef<{
        valueOptions: readonly ValueOption[] | ((params: import("@mui/x-data-grid").GridValueOptionsParams<R>) => readonly ValueOption[]);
        getOptionLabel?: ((value: ValueOption) => string) | undefined;
        getOptionValue?: ((value: ValueOption) => V) | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        valueGetter: (value: never, row: R, column: GridSingleSelectColDef<unknown, R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
        type: "singleSelect";
    }>;
    string: <V extends string | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: Except<GridBaseColDef<R, V, F>, "valueGetter" | "type">) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "string";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    number: <V extends number | `${number}` | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: Except<GridBaseColDef<R, V, F>, "valueGetter" | "type">) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "number";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    boolean: <V extends boolean | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: Except<GridBaseColDef<R, V, F>, "valueGetter" | "type">) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "boolean";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
};
/**
 *  * Inspired by Tanstack Table createColumnHelper util.
 */
export declare function useDataGridColumnHelper<R extends GridValidRowModel>({ filterMode, }: {
    filterMode: 'server' | 'client';
}): {
    date: <V extends Date | null | undefined, F extends string | number | null>(colDef: DataGridDateColDef<R, V, F>) => {
        groupable: boolean;
        aggregable: boolean;
        display?: "text" | "flex";
        maxWidth?: number;
        minWidth?: number;
        width?: number;
        flex?: number;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | import("@mui/x-data-grid").GridColSpanFn<any, V, F> | undefined;
        disableColumnMenu?: boolean;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[];
        description?: string;
        field: string;
        headerName?: string;
        hideable?: boolean;
        sortable?: boolean;
        resizable: boolean;
        editable?: boolean;
        pinnable?: boolean;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | import("@mui/x-data-grid").GridValueGetter<any, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | import("@mui/x-data-grid").GridValueSetter<any, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | import("@mui/x-data-grid").GridValueParser<any, V, F> | undefined;
        cellClassName?: string | import("@mui/x-data-grid").GridCellClassFn<R, V> | undefined;
        renderEditCell: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | ((params: import("@mui/x-data-grid").GridColumnHeaderParams<any, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment;
        hideSortIcons?: boolean;
        filterable?: boolean;
        filterOperators: readonly import("@mui/x-data-grid").GridFilterOperator<any, any, any>[] | readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | import("@mui/x-data-grid").GetApplyQuickFilterFn<any, V> | undefined;
        disableReorder?: boolean;
        disableExport?: boolean;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        availableAggregationFunctions?: string[];
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<any> | import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridPastedValueParser<any, V, F> | undefined;
        valueGetter: (value: never, row: R, column: import("@mui/x-data-grid").GridColDef<R, any, F>, apiRef: React.MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => V;
        type: import("@mui/x-data-grid").GridColType;
        valueFormatter: (value: V, row: R, _column: import("@mui/x-data-grid").GridColDef<R, V, F>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => F;
        renderCell: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | (({ formattedValue }: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
    };
    dateTime: <V extends Date | null | undefined, F extends string | number | null>(colDef: DataGridDateTimeColDef<R, V, F>) => {
        groupable: boolean;
        aggregable: boolean;
        display?: "text" | "flex";
        maxWidth?: number;
        minWidth?: number;
        width: number;
        flex?: number;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | import("@mui/x-data-grid").GridColSpanFn<any, V, F> | undefined;
        disableColumnMenu?: boolean;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[];
        description?: string;
        field: string;
        headerName?: string;
        hideable?: boolean;
        sortable?: boolean;
        resizable: boolean;
        editable?: boolean;
        pinnable?: boolean;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | import("@mui/x-data-grid").GridValueGetter<any, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | import("@mui/x-data-grid").GridValueSetter<any, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | import("@mui/x-data-grid").GridValueParser<any, V, F> | undefined;
        cellClassName?: string | import("@mui/x-data-grid").GridCellClassFn<R, V> | undefined;
        renderEditCell: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | ((params: import("@mui/x-data-grid").GridColumnHeaderParams<any, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment;
        hideSortIcons?: boolean;
        filterable?: boolean;
        filterOperators: readonly import("@mui/x-data-grid").GridFilterOperator<any, any, any>[] | readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | import("@mui/x-data-grid").GetApplyQuickFilterFn<any, V> | undefined;
        disableReorder?: boolean;
        disableExport?: boolean;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        availableAggregationFunctions?: string[];
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<any> | import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | import("@mui/x-data-grid-premium").GridPastedValueParser<any, V, F> | undefined;
        valueGetter: (value: never, row: R, column: import("@mui/x-data-grid").GridColDef<R, any, F>, apiRef: React.MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => V;
        type: import("@mui/x-data-grid").GridColType;
        valueFormatter: (value: V, row: R, _column: import("@mui/x-data-grid").GridColDef<R, V, F>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => F;
        renderCell: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | (({ formattedValue }: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => import("react/jsx-runtime").JSX.Element);
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
    };
    utils: {
        dateColDefaultFormatter: (dateValue: Date) => string;
        dateTimeColDefaultFormatter: (dateValue: Date) => string;
        getGridDateColumnOperators: ({ showTime }: {
            showTime: boolean;
        }) => readonly [{
            readonly value: "range";
            readonly label: string;
            readonly headerLabel: string;
            readonly getApplyFilterFn: (filterItem_: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined) => boolean) | null;
            readonly InputComponent: typeof import("./date-columns").GridFilterDateRangeInput;
            readonly InputComponentProps: Partial<{
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                dateRangePickerProps?: Partial<import("@mui/x-date-pickers-pro").DateRangePickerProps<import("@mui/x-date-pickers").PickerValidDate>>;
            }>;
        }, {
            readonly value: "is";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "not";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "after";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "onOrAfter";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "before";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "onOrBefore";
            readonly getApplyFilterFn: (filterItem: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined, row: GridValidRowModel, column: import("@mui/x-data-grid").GridColDef<GridValidRowModel, Date | null | undefined, unknown>, apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>) => boolean) | null;
            readonly InputComponent: ({ item, showTime, applyValue, apiRef, filterMode, }: {
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                showTime?: boolean;
                filterMode: "server" | "client";
            }) => import("react/jsx-runtime").JSX.Element;
            readonly InputComponentProps: {
                showTime: boolean;
                filterMode: "server" | "client";
            };
        }, {
            readonly value: "isEmpty";
            readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
            readonly requiresFilterValue: false;
        }, {
            readonly value: "isNotEmpty";
            readonly getApplyFilterFn: () => (value: Date | null | undefined) => boolean;
            readonly requiresFilterValue: false;
        }];
        getGridDateColumnRangeOperator: ({ InputComponentProps, }: {
            InputComponentProps: Partial<import("react").ComponentProps<typeof import("./date-columns").GridFilterDateRangeInput>>;
        }) => {
            readonly value: "range";
            readonly label: string;
            readonly headerLabel: string;
            readonly getApplyFilterFn: (filterItem_: import("@mui/x-data-grid").GridFilterItem) => ((value: Date | null | undefined) => boolean) | null;
            readonly InputComponent: typeof import("./date-columns").GridFilterDateRangeInput;
            readonly InputComponentProps: Partial<{
                item: import("@mui/x-data-grid").GridFilterItem;
                applyValue: (value: import("@mui/x-data-grid").GridFilterItem) => void;
                apiRef: import("react").MutableRefObject<import("@mui/x-data-grid/internals").GridApiCommunity>;
                focusElementRef?: React.Ref<any>;
            } & Pick<import("@mui/material").TextFieldProps, "color" | "size" | "variant" | "error" | "helperText"> & {
                dateRangePickerProps?: Partial<import("@mui/x-date-pickers-pro").DateRangePickerProps<import("@mui/x-date-pickers").PickerValidDate>>;
            }>;
        };
    };
    valueGetter: <V, F>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, column: {
        type?: import("@mui/x-data-grid").GridColType | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
    }) => ColumnHelperDef<{
        type?: import("@mui/x-data-grid").GridColType | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    singleSelect: <V, const ValueOption extends V | {
        value: V;
        label: string;
    }, F = V>(valueGetter: (value: never, row: R, column: GridSingleSelectColDef<unknown, R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, column: {
        valueOptions: readonly ValueOption[] | ((params: import("@mui/x-data-grid").GridValueOptionsParams<R>) => readonly ValueOption[]);
        getOptionLabel?: ((value: ValueOption) => string) | undefined;
        getOptionValue?: ((value: ValueOption) => V) | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
    }) => ColumnHelperDef<{
        valueOptions: readonly ValueOption[] | ((params: import("@mui/x-data-grid").GridValueOptionsParams<R>) => readonly ValueOption[]);
        getOptionLabel?: ((value: ValueOption) => string) | undefined;
        getOptionValue?: ((value: ValueOption) => V) | undefined;
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        valueGetter: (value: never, row: R, column: GridSingleSelectColDef<unknown, R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
        type: "singleSelect";
    }>;
    string: <V extends string | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: {
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
    }) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "string";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    number: <V extends number | `${number}` | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: {
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
    }) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "number";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
    boolean: <V extends boolean | null | undefined, F extends string | null | undefined>(valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V, { renderCell, ...column }: {
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderCell?: ((params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: ((params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode) | undefined;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
    }) => ColumnHelperDef<{
        display?: "text" | "flex" | undefined;
        maxWidth?: number | undefined;
        minWidth?: number | undefined;
        width?: number | undefined;
        flex?: number | undefined;
        align?: import("@mui/x-data-grid").GridAlignment | undefined;
        colSpan?: number | import("@mui/x-data-grid").GridColSpanFn<R, V, F> | undefined;
        disableColumnMenu?: boolean | undefined;
        sortingOrder?: readonly import("@mui/x-data-grid").GridSortDirection[] | undefined;
        description?: string | undefined;
        field: string;
        headerName?: string | undefined;
        hideable?: boolean | undefined;
        sortable?: boolean | undefined;
        resizable?: boolean | undefined;
        editable?: boolean | undefined;
        groupable?: boolean | undefined;
        pinnable?: boolean | undefined;
        sortComparator?: import("@mui/x-data-grid").GridComparatorFn<V> | undefined;
        getSortComparator?: ((sortDirection: import("@mui/x-data-grid").GridSortDirection) => import("@mui/x-data-grid").GridComparatorFn<V> | undefined) | undefined;
        rowSpanValueGetter?: import("@mui/x-data-grid").GridValueGetter<R, V, F, never> | undefined;
        valueSetter?: import("@mui/x-data-grid").GridValueSetter<R, V, F> | undefined;
        valueParser?: import("@mui/x-data-grid").GridValueParser<R, V, F> | undefined;
        cellClassName?: import("@mui/x-data-grid").GridCellClassNamePropType<R, V> | undefined;
        renderEditCell?: ((params: import("@mui/x-data-grid").GridRenderEditCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => React.ReactNode) | undefined;
        preProcessEditCellProps?: ((params: import("@mui/x-data-grid").GridPreProcessEditCellProps) => import("@mui/x-data-grid").GridEditCellProps | Promise<import("@mui/x-data-grid").GridEditCellProps>) | undefined;
        headerClassName?: import("@mui/x-data-grid").GridColumnHeaderClassNamePropType | undefined;
        renderHeader?: ((params: import("@mui/x-data-grid").GridColumnHeaderParams<R, V, F>) => React.ReactNode) | undefined;
        headerAlign?: import("@mui/x-data-grid").GridAlignment | undefined;
        hideSortIcons?: boolean | undefined;
        filterable?: boolean | undefined;
        filterOperators?: readonly import("@mui/x-data-grid").GridFilterOperator<R, V, F>[] | undefined;
        getApplyQuickFilterFn?: import("@mui/x-data-grid").GetApplyQuickFilterFn<R, V> | undefined;
        disableReorder?: boolean | undefined;
        disableExport?: boolean | undefined;
        renderHeaderFilter?: (params: import("@mui/x-data-grid-pro").GridRenderHeaderFilterProps) => React.ReactNode;
        aggregable?: boolean | undefined;
        availableAggregationFunctions?: string[] | undefined;
        groupingValueGetter?: import("@mui/x-data-grid-premium").GridGroupingValueGetter<R> | undefined;
        pastedValueParser?: import("@mui/x-data-grid-premium").GridPastedValueParser<R, V, F> | undefined;
        valueFormatter?: import("@mui/x-data-grid").GridValueFormatter<R, V, F, V> | undefined;
        type: "boolean";
        renderCell: (params: import("@mui/x-data-grid").GridRenderCellParams<R, V, F, import("@mui/x-data-grid").GridTreeNodeWithRender>) => boolean | Iterable<import("react").ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
        valueGetter: (value: never, row: R, column: GridBaseColDef<R, unknown, unknown>, apiRef: React.MutableRefObject<GridApi>) => V;
    }>;
};
export {};
