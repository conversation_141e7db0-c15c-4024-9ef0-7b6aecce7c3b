import type { DateRange, NonEmptyDateRange, PickersShortcutsProps, PickerValidDate } from '@mui/x-date-pickers-pro';
import { DateRangePicker as MuiDateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker';
export declare const DateRangePicker: typeof MuiDateRangePicker;
export declare function isNonEmptyDateRange<TDate extends PickerValidDate>(dateRange: DateRange<TDate>): dateRange is NonEmptyDateRange<TDate>;
export type { DateRange, PickersShortcutsProps, PickerValidDate, NonEmptyDateRange };
export type { DateRangePickerProps, DateRangePickerSlots, DateRangePickerSlotProps, } from '@mui/x-date-pickers-pro/DateRangePicker';
