import { MultiInputDateTimeRangeField as MuiMultiInputDateTimeRangeField, unstable_useMultiInputDateTimeRangeField as useMultiInputDateTimeRangeField } from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField';
declare const MultiInputDateTimeRangeField: typeof MuiMultiInputDateTimeRangeField;
export { useMultiInputDateTimeRangeField, MultiInputDateTimeRangeField };
export type { MultiInputDateTimeRangeFieldProps, UseMultiInputDateTimeRangeFieldComponentProps, UseMultiInputDateTimeRangeFieldProps, } from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField';
