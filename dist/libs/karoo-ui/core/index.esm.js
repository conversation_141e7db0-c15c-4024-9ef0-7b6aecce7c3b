import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import * as React from 'react';
import React__default, { createContext, useContext, useMemo, forwardRef as forwardRef$1, useState, useCallback, useEffect, useRef } from 'react';
import { formLabelClasses } from '@mui/material/FormLabel';
export * from '@mui/material/FormLabel';
export { default as FormLabel } from '@mui/material/FormLabel';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
export * from '@mui/material/OutlinedInput';
export { default as OutlinedInput } from '@mui/material/OutlinedInput';
import { createTheme, alpha, ThemeProvider as ThemeProvider$1, useThemeProps, styled as styled$2 } from '@mui/material/styles';
export { useTheme } from '@mui/material/styles';
import * as baseLocales from '@mui/material/locale';
import * as dataGridLocales from '@mui/x-data-grid-premium/locales';
import * as datePickerLocales from '@mui/x-date-pickers-pro/locales';
export * from '@mui/material/Accordion';
export { default as Accordion } from '@mui/material/Accordion';
export * from '@mui/material/AccordionDetails';
export { default as AccordionDetails } from '@mui/material/AccordionDetails';
export * from '@mui/material/AccordionSummary';
export { default as AccordionSummary } from '@mui/material/AccordionSummary';
export * from '@mui/material/Alert';
export { default as Alert } from '@mui/material/Alert';
export * from '@mui/material/AlertTitle';
export { default as AlertTitle } from '@mui/material/AlertTitle';
export * from '@mui/material/AppBar';
export { default as AppBar } from '@mui/material/AppBar';
import Autocomplete__default from '@mui/material/Autocomplete';
export * from '@mui/material/Autocomplete';
import * as R from 'remeda';
export * from '@mui/material/Avatar';
export { default as Avatar } from '@mui/material/Avatar';
export * from '@mui/material/Backdrop';
export { default as Backdrop } from '@mui/material/Backdrop';
export * from '@mui/material/Badge';
export { default as Badge } from '@mui/material/Badge';
import Box__default from '@mui/material/Box';
export * from '@mui/material/Box';
export { default as Box } from '@mui/material/Box';
export * from '@mui/material/Breadcrumbs';
export { default as Breadcrumbs } from '@mui/material/Breadcrumbs';
import MuiButton__default from '@mui/material/Button';
export * from '@mui/material/Button';
export * from '@mui/material/ButtonGroup';
export { default as ButtonGroup } from '@mui/material/ButtonGroup';
export * from '@mui/material/Card';
export { default as Card } from '@mui/material/Card';
export * from '@mui/material/CardContent';
export { default as CardContent } from '@mui/material/CardContent';
export * from '@mui/material/CardHeader';
export { default as CardHeader } from '@mui/material/CardHeader';
export * from '@mui/material/Checkbox';
export { default as Checkbox } from '@mui/material/Checkbox';
export * from '@mui/material/Chip';
export { default as Chip } from '@mui/material/Chip';
import CircularProgress__default from '@mui/material/CircularProgress';
export * from '@mui/material/CircularProgress';
export { default as CircularProgress } from '@mui/material/CircularProgress';
import Fade__default from '@mui/material/Fade';
export * from '@mui/material/Fade';
export { default as Fade } from '@mui/material/Fade';
import Stack__default from '@mui/material/Stack';
export * from '@mui/material/Stack';
export { default as Stack } from '@mui/material/Stack';
import ClickAwayListener__default from '@mui/material/ClickAwayListener';
export * from '@mui/material/ClickAwayListener';
export { default as ClickAwayListener } from '@mui/material/ClickAwayListener';
export * from '@mui/material/Collapse';
export { default as Collapse } from '@mui/material/Collapse';
export * from '@mui/material/Container';
export { default as Container } from '@mui/material/Container';
export * from '@mui/material/CssBaseline';
export { default as CssBaseline } from '@mui/material/CssBaseline';
import { getGridNumericOperators as getGridNumericOperators$1, getGridStringOperators as getGridStringOperators$2, getGridBooleanOperators as getGridBooleanOperators$2, getGridSingleSelectOperators as getGridSingleSelectOperators$1, GRID_CHECKBOX_SELECTION_COL_DEF as GRID_CHECKBOX_SELECTION_COL_DEF$1, GridToolbarContainer, GridToolbarExportContainer, GridCsvExportMenuItem, GridPrintExportMenuItem, GridExcelExportMenuItem, useGridApiContext as useGridApiContext$1, gridQuickFilterValuesSelector as gridQuickFilterValuesSelector$1, GridToolbarQuickFilter, GridToolbarColumnsButton as GridToolbarColumnsButton$1, GridToolbarFilterButton as GridToolbarFilterButton$1, GridToolbarDensitySelector as GridToolbarDensitySelector$1, GridToolbarExport as GridToolbarExport$1, DataGridPremium, useGridApiRef, gridClasses, GRID_DATE_COL_DEF as GRID_DATE_COL_DEF$1, GRID_DATETIME_COL_DEF as GRID_DATETIME_COL_DEF$1, GRID_TREE_DATA_GROUPING_FIELD } from '@mui/x-data-grid-premium';
export { DATA_GRID_PREMIUM_PROPS_DEFAULT_VALUES, DEFAULT_GRID_COL_TYPE_KEY, DataGridPremium as DataGridMui, GRID_ACTIONS_COLUMN_TYPE, GRID_ACTIONS_COL_DEF, GRID_AGGREGATION_FUNCTIONS, GRID_AGGREGATION_ROOT_FOOTER_ROW_ID, GRID_BOOLEAN_COL_DEF, GRID_CHECKBOX_SELECTION_FIELD, GRID_COLUMN_MENU_SLOTS, GRID_COLUMN_MENU_SLOT_PROPS, GRID_DATETIME_COL_DEF, GRID_DATE_COL_DEF, GRID_DEFAULT_LOCALE_TEXT, GRID_DETAIL_PANEL_TOGGLE_COL_DEF, GRID_DETAIL_PANEL_TOGGLE_FIELD, GRID_EXPERIMENTAL_ENABLED, GRID_NUMERIC_COL_DEF, GRID_REORDER_COL_DEF, GRID_ROOT_GROUP_ID, GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD, GRID_SINGLE_SELECT_COL_DEF, GRID_STRING_COL_DEF, GRID_TREE_DATA_GROUPING_FIELD, GridActionsCell, GridActionsCellItem, GridAddIcon, GridApiContext, GridArrowDownwardIcon, GridArrowUpwardIcon, GridBody, GridBooleanCell, GridCell, GridCellCheckboxForwardRef, GridCellCheckboxRenderer, GridCellEditStartReasons, GridCellEditStopReasons, GridCellModes, GridCheckCircleIcon, GridCheckIcon, GridClearIcon, GridCloseIcon, GridColumnHeaderFilterIconButton, GridColumnHeaderItem, GridColumnHeaderMenu, GridColumnHeaderSeparator, GridColumnHeaderSeparatorSides, GridColumnHeaderSortIcon, GridColumnHeaderTitle, GridColumnIcon, GridColumnMenu, GridColumnMenuColumnsItem, GridColumnMenuContainer, GridColumnMenuFilterItem, GridColumnMenuHideItem, GridColumnMenuPinningItem, GridColumnMenuSortItem, GridColumnsPanel, GridContextProvider, GridCsvExportMenuItem, GridDeleteForeverIcon, GridDeleteIcon, GridDetailPanelToggleCell, GridDragIcon, GridEditBooleanCell, GridEditDateCell, GridEditInputCell, GridEditModes, GridEditSingleSelectCell, GridExcelExportMenuItem, GridExpandMoreIcon, GridFilterAltIcon, GridFilterForm, GridFilterInputDate, GridFilterInputMultipleSingleSelect, GridFilterInputMultipleValue, GridFilterInputSingleSelect, GridFilterInputValue, GridFilterListIcon, GridFilterPanel, GridFooter, GridFooterContainer, GridFooterPlaceholder, GridFunctionsIcon, GridGroupWorkIcon, GridHeader, GridHeaderCheckbox, GridKeyboardArrowRight, GridLoadIcon, GridLoadingOverlay, GridLogicOperator, GridMenu, GridMenuIcon, GridMoreVertIcon, GridNoRowsOverlay, GridOverlay, GridOverlays, GridPagination, GridPanel, GridPanelContent, GridPanelFooter, GridPanelHeader, GridPanelWrapper, GridPinnedColumnPosition, GridPreferencePanelsValue, GridPrintExportMenuItem, GridPushPinLeftIcon, GridPushPinRightIcon, GridRemoveIcon, GridRoot, GridRow, GridRowCount, GridRowEditStartReasons, GridRowEditStopReasons, GridRowModes, GridSaveAltIcon, GridSearchIcon, GridSelectedRowCount, GridSeparatorIcon, GridSignature, GridSkeletonCell, GridTableRowsIcon, GridToolbar, GridToolbarContainer, GridToolbarExportContainer, GridToolbarQuickFilter, GridTreeDataGroupingCell, GridTripleDotsVerticalIcon, GridViewColumnIcon, GridViewHeadlineIcon, GridViewStreamIcon, GridVisibilityOffIcon, GridWorkspacesIcon, checkGridRowIdIsValid, createUseGridApiEventHandler, getAggregationFooterRowIdFromGroupId, getDataGridUtilityClass, getDefaultGridFilterModel, getGridDateOperators, getGridDefaultColumnTypes, getGridNumericQuickFilterFn, getGridStringQuickFilterFn, getGroupRowIdFromPath, getRowGroupingFieldFromGroupingCriteria, gridAggregationLookupSelector, gridAggregationModelSelector, gridAggregationStateSelector, gridClasses, gridColumnDefinitionsSelector, gridColumnFieldsSelector, gridColumnGroupingSelector, gridColumnGroupsHeaderMaxDepthSelector, gridColumnGroupsHeaderStructureSelector, gridColumnGroupsLookupSelector, gridColumnGroupsUnwrappedModelSelector, gridColumnLookupSelector, gridColumnMenuSelector, gridColumnPositionsSelector, gridColumnReorderDragColSelector, gridColumnReorderSelector, gridColumnResizeSelector, gridColumnVisibilityModelSelector, gridColumnsStateSelector, gridColumnsTotalWidthSelector, gridDataRowIdsSelector, gridDateComparator, gridDateFormatter, gridDateTimeFormatter, gridDensityFactorSelector, gridDensitySelector, gridDetailPanelExpandedRowIdsSelector, gridDetailPanelExpandedRowsContentCacheSelector, gridDetailPanelExpandedRowsHeightCacheSelector, gridDimensionsSelector, gridEditRowsStateSelector, gridExpandedRowCountSelector, gridExpandedSortedRowEntriesSelector, gridExpandedSortedRowIdsSelector, gridFilterActiveItemsLookupSelector, gridFilterActiveItemsSelector, gridFilterModelSelector, gridFilterableColumnDefinitionsSelector, gridFilterableColumnLookupSelector, gridFilteredDescendantCountLookupSelector, gridFilteredDescendantRowCountSelector, gridFilteredRowCountSelector, gridFilteredRowsLookupSelector, gridFilteredSortedRowEntriesSelector, gridFilteredSortedRowIdsSelector, gridFilteredSortedTopLevelRowEntriesSelector, gridFilteredTopLevelRowCountSelector, gridFocusCellSelector, gridFocusColumnGroupHeaderSelector, gridFocusColumnHeaderFilterSelector, gridFocusColumnHeaderSelector, gridFocusStateSelector, gridHasColSpanSelector, gridHeaderFilteringEditFieldSelector, gridHeaderFilteringEnabledSelector, gridHeaderFilteringMenuSelector, gridHeaderFilteringStateSelector, gridListColumnSelector, gridNumberComparator, gridPageCountSelector, gridPageSelector, gridPageSizeSelector, gridPaginatedVisibleSortedGridRowEntriesSelector, gridPaginatedVisibleSortedGridRowIdsSelector, gridPaginationEnabledClientSideSelector, gridPaginationMetaSelector, gridPaginationModelSelector, gridPaginationRowCountSelector, gridPaginationRowRangeSelector, gridPaginationSelector, gridPanelClasses, gridPinnedColumnsSelector, gridPreferencePanelStateSelector, gridQuickFilterValuesSelector, gridRenderContextColumnsSelector, gridRenderContextSelector, gridResizingColumnFieldSelector, gridRowCountSelector, gridRowGroupingModelSelector, gridRowGroupingNameSelector, gridRowGroupingSanitizedModelSelector, gridRowIdSelector, gridRowMaximumTreeDepthSelector, gridRowSelectionStateSelector, gridRowTreeDepthsSelector, gridRowTreeSelector, gridRowsDataRowIdToIdLookupSelector, gridRowsLoadingSelector, gridRowsLookupSelector, gridRowsMetaSelector, gridSortColumnLookupSelector, gridSortModelSelector, gridSortedRowEntriesSelector, gridSortedRowIdsSelector, gridStringOrNumberComparator, gridTabIndexCellSelector, gridTabIndexColumnGroupHeaderSelector, gridTabIndexColumnHeaderFilterSelector, gridTabIndexColumnHeaderSelector, gridTabIndexStateSelector, gridTopLevelRowCountSelector, gridVirtualizationColumnEnabledSelector, gridVirtualizationRowEnabledSelector, gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector, gridVisiblePinnedColumnDefinitionsSelector, gridVisibleRowsLookupSelector, gridVisibleRowsSelector, isGroupingColumn, renderActionsCell, renderBooleanCell, renderEditBooleanCell, renderEditDateCell, renderEditInputCell, renderEditSingleSelectCell, selectedGridRowsCountSelector, selectedGridRowsSelector, selectedIdsLookupSelector, setupExcelExportWebWorker, useGridApiContext, useGridApiEventHandler, useGridApiMethod, useGridApiOptionHandler, useGridApiRef, useGridLogger, useGridNativeEventListener, useGridRootProps, useGridSelector, useKeepGroupedColumnsHidden } from '@mui/x-data-grid-premium';
import SearchRoundedIcon from '@mui/icons-material/SearchRounded';
import ClearIcon from '@mui/icons-material/Clear';
import SearchIcon from '@mui/icons-material/Search';
import IconButton__default from '@mui/material/IconButton';
export * from '@mui/material/IconButton';
export { default as IconButton } from '@mui/material/IconButton';
import TextField$1 from '@mui/material/TextField';
export { getTextFieldUtilityClass, textFieldClasses } from '@mui/material/TextField';
import { styled as styled$1, keyframes as keyframes$1 } from '@mui/system';
import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import KeyboardArrowRightRoundedIcon from '@mui/icons-material/KeyboardArrowRightRounded';
import Paper__default from '@mui/material/Paper';
export * from '@mui/material/Paper';
export { default as Paper } from '@mui/material/Paper';
import Tab__default from '@mui/material/Tab';
export * from '@mui/material/Tab';
export { default as Tab } from '@mui/material/Tab';
import Tabs__default from '@mui/material/Tabs';
export * from '@mui/material/Tabs';
export { default as Tabs } from '@mui/material/Tabs';
import debounce$1 from 'lodash/debounce';
import InputBase__default from '@mui/material/InputBase';
export * from '@mui/material/InputBase';
export { default as InputBase } from '@mui/material/InputBase';
import { MuiPickersAdapterContext, DateRangePicker as DateRangePicker$1, SingleInputDateRangeField, DateTimePicker as DateTimePicker$1, DatePicker as DatePicker$1, TimePicker as TimePicker$1 } from '@mui/x-date-pickers-pro';
import MUITooltip from '@mui/material/Tooltip';
export { getTooltipUtilityClass, tooltipClasses } from '@mui/material/Tooltip';
import Typography__default from '@mui/material/Typography';
export * from '@mui/material/Typography';
export { default as Typography } from '@mui/material/Typography';
import { debounce } from 'lodash';
import useResizeObserver from '@react-hook/resize-observer';
import { arrayOfAllUnionTypes } from '@karoo/utils';
import { update, get } from 'idb-keyval';
export * from '@mui/x-date-pickers/DateCalendar';
import { DateField as DateField$1 } from '@mui/x-date-pickers/DateField';
export { unstable_useDateField as useDateField } from '@mui/x-date-pickers/DateField';
import { DatePicker as DatePicker$2 } from '@mui/x-date-pickers/DatePicker';
export { DatePickerToolbar, datePickerToolbarClasses } from '@mui/x-date-pickers/DatePicker';
import { DateRangeCalendar as DateRangeCalendar$1 } from '@mui/x-date-pickers-pro/DateRangeCalendar';
export { dateRangeCalendarClasses, getDateRangeCalendarUtilityClass } from '@mui/x-date-pickers-pro/DateRangeCalendar';
import { DateRangePicker as DateRangePicker$2 } from '@mui/x-date-pickers-pro/DateRangePicker';
export * from '@mui/x-date-pickers-pro/DateRangePickerDay';
import { DateTimeField as DateTimeField$1 } from '@mui/x-date-pickers/DateTimeField';
export { unstable_useDateTimeField as useDateTimeField } from '@mui/x-date-pickers/DateTimeField';
import { DateTimePicker as DateTimePicker$2 } from '@mui/x-date-pickers/DateTimePicker';
export { dateTimePickerTabsClasses, dateTimePickerToolbarClasses } from '@mui/x-date-pickers/DateTimePicker';
import { DateTimeRangePicker as DateTimeRangePicker$1 } from '@mui/x-date-pickers-pro/DateTimeRangePicker';
import MuiDialog from '@mui/material/Dialog';
export { dialogClasses, getDialogUtilityClass } from '@mui/material/Dialog';
export * from '@mui/material/DialogActions';
export { default as DialogActions } from '@mui/material/DialogActions';
export * from '@mui/material/DialogContent';
export { default as DialogContent } from '@mui/material/DialogContent';
export * from '@mui/material/DialogContentText';
export { default as DialogContentText } from '@mui/material/DialogContentText';
export * from '@mui/material/DialogTitle';
export { default as DialogTitle } from '@mui/material/DialogTitle';
export * from '@mui/material/Divider';
export { default as Divider } from '@mui/material/Divider';
import MuiDrawer from '@mui/material/Drawer';
export { drawerClasses, getDrawerUtilityClass } from '@mui/material/Drawer';
export * from '@mui/material/Fab';
export { default as Fab } from '@mui/material/Fab';
export * from '@mui/material/FormControl';
export { default as FormControl } from '@mui/material/FormControl';
import FormControlLabel__default from '@mui/material/FormControlLabel';
export * from '@mui/material/FormControlLabel';
export * from '@mui/material/FormGroup';
export { default as FormGroup } from '@mui/material/FormGroup';
export * from '@mui/material/FormHelperText';
export { default as FormHelperText } from '@mui/material/FormHelperText';
export { default as Grid, getGridUtilityClass } from '@mui/material/Grid';
export { default as GridLegacy, getGridLegacyUtilityClass, gridLegacyClasses } from '@mui/material/GridLegacy';
export * from '@mui/material/Grow';
export { default as Grow } from '@mui/material/Grow';
export * from '@mui/material/ImageList';
export { default as ImageList } from '@mui/material/ImageList';
export * from '@mui/material/ImageListItem';
export { default as ImageListItem } from '@mui/material/ImageListItem';
export * from '@mui/material/InputAdornment';
export { default as InputAdornment } from '@mui/material/InputAdornment';
export * from '@mui/material/InputLabel';
export { default as InputLabel } from '@mui/material/InputLabel';
export * from '@mui/material/LinearProgress';
export { default as LinearProgress } from '@mui/material/LinearProgress';
export { default as NativeLink, getLinkUtilityClass as getNativeLinkUtilityClass, linkClasses as nativeLinkClasses } from '@mui/material/Link';
export * from '@mui/material/List';
export { default as List } from '@mui/material/List';
export * from '@mui/material/ListItem';
export { default as ListItem } from '@mui/material/ListItem';
export * from '@mui/material/ListItemButton';
export { default as ListItemButton } from '@mui/material/ListItemButton';
export * from '@mui/material/ListItemIcon';
export { default as ListItemIcon } from '@mui/material/ListItemIcon';
export * from '@mui/material/ListItemText';
export { default as ListItemText } from '@mui/material/ListItemText';
export * from '@mui/material/ListSubheader';
export { default as ListSubheader } from '@mui/material/ListSubheader';
export * from '@mui/x-date-pickers/LocalizationProvider';
export * from '@mui/material/Menu';
export { default as Menu } from '@mui/material/Menu';
export * from '@mui/material/MenuItem';
export { default as MenuItem } from '@mui/material/MenuItem';
export * from '@mui/material/MenuList';
export { default as MenuList } from '@mui/material/MenuList';
export * from '@mui/material/Modal';
export { default as Modal } from '@mui/material/Modal';
export { MultiInputDateRangeField, unstable_useMultiInputDateRangeField as useMultiInputDateRangeField } from '@mui/x-date-pickers-pro/MultiInputDateRangeField';
import { MultiInputDateTimeRangeField as MultiInputDateTimeRangeField$1 } from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField';
export { unstable_useMultiInputDateTimeRangeField as useMultiInputDateTimeRangeField } from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField';
import { MultiInputTimeRangeField as MultiInputTimeRangeField$1 } from '@mui/x-date-pickers-pro/MultiInputTimeRangeField';
export { unstable_useMultiInputTimeRangeField as useMultiInputTimeRangeField } from '@mui/x-date-pickers-pro/MultiInputTimeRangeField';
export * from '@mui/material/Pagination';
export { default as Pagination } from '@mui/material/Pagination';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
export { PickersShortcuts } from '@mui/x-date-pickers/PickersShortcuts';
export { pickersLayoutClasses } from '@mui/x-date-pickers/PickersLayout';
export * from '@mui/material/Popover';
export { default as Popover } from '@mui/material/Popover';
export * from '@mui/material/Popper';
export { default as Popper } from '@mui/material/Popper';
export * from '@mui/material/Radio';
export { default as Radio } from '@mui/material/Radio';
export * from '@mui/material/RadioGroup';
export { default as RadioGroup } from '@mui/material/RadioGroup';
export * from '@mui/material/Rating';
export { default as Rating } from '@mui/material/Rating';
export * from '@mui/material/ScopedCssBaseline';
export { default as ScopedCssBaseline } from '@mui/material/ScopedCssBaseline';
import MUISelect__default from '@mui/material/Select';
export * from '@mui/material/Select';
export { SingleInputDateRangeField, unstable_useSingleInputDateRangeField as useSingleInputDateRangeField } from '@mui/x-date-pickers-pro/SingleInputDateRangeField';
import { SingleInputDateTimeRangeField as SingleInputDateTimeRangeField$1 } from '@mui/x-date-pickers-pro/SingleInputDateTimeRangeField';
export { unstable_useSingleInputDateTimeRangeField as useSingleInputDateTimeRangeField } from '@mui/x-date-pickers-pro/SingleInputDateTimeRangeField';
import { SingleInputTimeRangeField as SingleInputTimeRangeField$1 } from '@mui/x-date-pickers-pro/SingleInputTimeRangeField';
export { unstable_useSingleInputTimeRangeField as useSingleInputTimeRangeField } from '@mui/x-date-pickers-pro/SingleInputTimeRangeField';
export * from '@mui/material/Skeleton';
export { default as Skeleton } from '@mui/material/Skeleton';
export * from '@mui/material/Slide';
export { default as Slide } from '@mui/material/Slide';
export * from '@mui/material/Slider';
export { default as Slider } from '@mui/material/Slider';
export * from '@mui/material/Snackbar';
export { default as Snackbar } from '@mui/material/Snackbar';
export * from '@mui/material/SnackbarContent';
export { default as SnackbarContent } from '@mui/material/SnackbarContent';
export { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
export { StaticDateRangePicker } from '@mui/x-date-pickers-pro/StaticDateRangePicker';
export * from '@mui/material/Step';
export { default as Step } from '@mui/material/Step';
export * from '@mui/material/StepButton';
export { default as StepButton } from '@mui/material/StepButton';
export * from '@mui/material/StepConnector';
export { default as StepConnector } from '@mui/material/StepConnector';
export * from '@mui/material/StepContent';
export { default as StepContent } from '@mui/material/StepContent';
export * from '@mui/material/StepIcon';
export { default as StepIcon } from '@mui/material/StepIcon';
export * from '@mui/material/StepLabel';
export { default as StepLabel } from '@mui/material/StepLabel';
export * from '@mui/material/Stepper';
export { default as Stepper } from '@mui/material/Stepper';
export * from '@mui/material/SvgIcon';
export { default as SvgIcon } from '@mui/material/SvgIcon';
export * from '@mui/material/Switch';
export { default as Switch } from '@mui/material/Switch';
export * from '@mui/material/Table';
export { default as Table } from '@mui/material/Table';
export * from '@mui/material/TableBody';
export { default as TableBody } from '@mui/material/TableBody';
export * from '@mui/material/TableCell';
export { default as TableCell } from '@mui/material/TableCell';
export * from '@mui/material/TableContainer';
export { default as TableContainer } from '@mui/material/TableContainer';
export * from '@mui/material/TableFooter';
export { default as TableFooter } from '@mui/material/TableFooter';
export * from '@mui/material/TableHead';
export { default as TableHead } from '@mui/material/TableHead';
export * from '@mui/material/TablePagination';
export { default as TablePagination } from '@mui/material/TablePagination';
export * from '@mui/material/TableRow';
export { default as TableRow } from '@mui/material/TableRow';
export * from '@mui/material/TableSortLabel';
export { default as TableSortLabel } from '@mui/material/TableSortLabel';
import { TimeField as TimeField$1 } from '@mui/x-date-pickers/TimeField';
export { unstable_useTimeField as useTimeField } from '@mui/x-date-pickers/TimeField';
export * from '@mui/lab/Timeline';
export { default as Timeline } from '@mui/lab/Timeline';
export * from '@mui/lab/TimelineConnector';
export { default as TimelineConnector } from '@mui/lab/TimelineConnector';
export * from '@mui/lab/TimelineContent';
export { default as TimelineContent } from '@mui/lab/TimelineContent';
export * from '@mui/lab/TimelineDot';
export { default as TimelineDot } from '@mui/lab/TimelineDot';
export * from '@mui/lab/TimelineItem';
export { default as TimelineItem } from '@mui/lab/TimelineItem';
export * from '@mui/lab/TimelineOppositeContent';
export { default as TimelineOppositeContent } from '@mui/lab/TimelineOppositeContent';
export * from '@mui/lab/TimelineSeparator';
export { default as TimelineSeparator } from '@mui/lab/TimelineSeparator';
export { TimePickerToolbar, timePickerToolbarClasses } from '@mui/x-date-pickers/TimePicker';
export * from '@mui/material/ToggleButton';
export { default as ToggleButton } from '@mui/material/ToggleButton';
export * from '@mui/material/ToggleButtonGroup';
export { default as ToggleButtonGroup } from '@mui/material/ToggleButtonGroup';
export { TreeItem, TreeItemContent, getTreeItemUtilityClass, treeItemClasses, useTreeItemState } from '@mui/x-tree-view/TreeItem';
export { SimpleTreeView, SimpleTreeViewRoot, getSimpleTreeViewUtilityClass, simpleTreeViewClasses } from '@mui/x-tree-view/SimpleTreeView';
export * from '@mui/material/useMediaQuery';
export { default as useMediaQuery } from '@mui/material/useMediaQuery';
export * from '@mui/material/usePagination';
export { default as usePagination } from '@mui/material/usePagination';
export * from '@mui/material/Zoom';
export { default as Zoom } from '@mui/material/Zoom';
import * as colors from '@mui/material/colors';
export { colors };

const KarooExtendedLocalesContext = createContext(null);
const KarooExtendedLocalesProvider = KarooExtendedLocalesContext.Provider;
const useKarooExtendedLocales = () => {
    const value = useContext(KarooExtendedLocalesContext);
    if (!value) {
        throw new Error('[useKarooExtendedLocales] - no context provider');
    }
    return value;
};

const customKarooLocale_enUS = {
    components: {
        KarooSearchTextField: {
            defaultProps: {
                placeholder: 'Search',
            },
        },
        KarooGridToolbarSearchButton: {
            defaultProps: {
                children: 'Search',
            },
        },
    },
    extended: {
        filterOperators: {
            date_range: 'range',
        },
    },
};
// NOTE: Locales exports must be done explicitly and individually so they can be tree shakable by webpack, rollup, etc.
const locale_enUS = {
    base: baseLocales.enUS,
    dataGrid: dataGridLocales.enUS,
    datePickers: datePickerLocales.enUS,
    karoo: customKarooLocale_enUS,
};
// If mui adds support for enNZ, we should change this to use the enNZ locales
const locale_enNZ = {
    base: baseLocales.enUS,
    dataGrid: dataGridLocales.enUS,
    datePickers: datePickerLocales.enUS,
    karoo: customKarooLocale_enUS,
};
const locale_ptPT = {
    base: baseLocales.ptPT,
    dataGrid: {
        components: {
            MuiDataGrid: {
                defaultProps: {
                    localeText: {
                        ...dataGridLocales.ptPT.components.MuiDataGrid.defaultProps.localeText,
                        filterPanelRemoveAll: 'Remover Filtros', // Tested with "@mui/x-data-grid": "6.17.0". Will be deleted after MUI adds this translation
                    },
                },
            },
        },
    }, // fallback to ptBR as ptPT not yet supported by DataGridPremium,
    datePickers: datePickerLocales.ptBR,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Procurar',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Procurar',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'intervalo',
            },
        },
    },
};
const locale_arSD = {
    base: baseLocales.arSD,
    dataGrid: dataGridLocales.arSD,
    datePickers: datePickerLocales.enUS,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'البحث',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'البحث',
                },
            },
        },
        extended: locale_enUS.karoo.extended,
    },
};
const locale_bgBG = {
    base: baseLocales.bgBG,
    dataGrid: dataGridLocales.bgBG,
    datePickers: datePickerLocales.enUS,
    karoo: customKarooLocale_enUS,
};
const locale_csCZ = {
    base: baseLocales.csCZ,
    dataGrid: dataGridLocales.csCZ,
    datePickers: datePickerLocales.csCZ,
    karoo: customKarooLocale_enUS,
};
const locale_deDE = {
    base: baseLocales.deDE,
    dataGrid: dataGridLocales.deDE,
    datePickers: datePickerLocales.deDE,
    karoo: customKarooLocale_enUS,
};
const locale_elGR = {
    base: baseLocales.elGR,
    dataGrid: dataGridLocales.elGR,
    datePickers: datePickerLocales.elGR,
    karoo: customKarooLocale_enUS,
};
const locale_esES = {
    base: baseLocales.esES,
    dataGrid: dataGridLocales.esES,
    datePickers: datePickerLocales.esES,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Buscar',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Buscar',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'intervalo',
            },
        },
    },
};
const locale_faIR = {
    base: baseLocales.faIR,
    dataGrid: dataGridLocales.faIR,
    datePickers: datePickerLocales.faIR,
    karoo: customKarooLocale_enUS,
};
const locale_fiFI = {
    base: baseLocales.fiFI,
    dataGrid: dataGridLocales.fiFI,
    datePickers: datePickerLocales.fiFI,
    karoo: customKarooLocale_enUS,
};
const locale_frFR = {
    base: baseLocales.frFR,
    dataGrid: dataGridLocales.frFR,
    datePickers: datePickerLocales.frFR,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Chercher',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Chercher',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'intervalle',
            },
        },
    },
};
const locale_heIL = {
    base: baseLocales.heIL,
    dataGrid: dataGridLocales.heIL,
    datePickers: datePickerLocales.heIL,
    karoo: customKarooLocale_enUS,
};
const locale_itIT = {
    base: baseLocales.itIT,
    dataGrid: dataGridLocales.itIT,
    datePickers: datePickerLocales.itIT,
    karoo: customKarooLocale_enUS,
};
const locale_jaJP = {
    base: baseLocales.jaJP,
    dataGrid: dataGridLocales.jaJP,
    datePickers: datePickerLocales.jaJP,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'サーチ',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'サーチ',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: '範囲',
            },
        },
    },
};
const locale_koKR = {
    base: baseLocales.koKR,
    dataGrid: dataGridLocales.koKR,
    datePickers: datePickerLocales.koKR,
    karoo: customKarooLocale_enUS,
};
const locale_nlNL = {
    base: baseLocales.nlNL,
    dataGrid: dataGridLocales.nlNL,
    datePickers: datePickerLocales.nlNL,
    karoo: customKarooLocale_enUS,
};
const locale_plPL = {
    base: baseLocales.plPL,
    dataGrid: dataGridLocales.plPL,
    datePickers: datePickerLocales.plPL,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Szukaj',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Szukaj',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'zakres',
            },
        },
    },
};
const locale_ruRU = {
    base: baseLocales.ruRU,
    dataGrid: dataGridLocales.ruRU,
    datePickers: datePickerLocales.ruRU,
    karoo: customKarooLocale_enUS,
};
const locale_skSK = {
    base: baseLocales.skSK,
    dataGrid: dataGridLocales.skSK,
    datePickers: datePickerLocales.skSK,
    karoo: customKarooLocale_enUS,
};
const locale_trTR = {
    base: baseLocales.trTR,
    dataGrid: dataGridLocales.trTR,
    datePickers: datePickerLocales.trTR,
    karoo: customKarooLocale_enUS,
};
const locale_ukUA = {
    base: baseLocales.ukUA,
    dataGrid: dataGridLocales.ukUA,
    datePickers: datePickerLocales.ukUA,
    karoo: customKarooLocale_enUS,
};
const locale_viVN = {
    base: baseLocales.viVN,
    dataGrid: dataGridLocales.viVN,
    datePickers: datePickerLocales.viVN,
    karoo: customKarooLocale_enUS,
};
const locale_zhCN = {
    base: baseLocales.zhCN,
    dataGrid: dataGridLocales.zhCN,
    datePickers: datePickerLocales.zhCN,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: '搜寻',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: '搜寻',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: '范围',
            },
        },
    },
};
const locale_zhHK = {
    base: baseLocales.zhHK,
    dataGrid: dataGridLocales.zhHK,
    datePickers: datePickerLocales.zhHK,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: '搜尋',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: '搜尋',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: '范围',
            },
        },
    },
};
const locale_thTH = {
    base: baseLocales.thTH,
    dataGrid: dataGridLocales.enUS, // @mui/x-data-grid-premium does not yet have thTH
    datePickers: datePickerLocales.enUS,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'ค้นหา',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'ค้นหา',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'ช่วงระหว่าง',
            },
        },
    },
};
const locale_idID = {
    base: baseLocales.idID,
    dataGrid: dataGridLocales.enUS, // @mui/x-data-grid-premium does not yet have idID
    datePickers: datePickerLocales.enUS,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Cari',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Cari',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'lingkup',
            },
        },
    },
};
const locale_msMS = {
    base: baseLocales.msMS,
    dataGrid: dataGridLocales.enUS, // @mui/x-data-grid-premium does not yet have idID
    datePickers: datePickerLocales.enUS,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'Cari',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'Cari',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'pelbagai',
            },
        },
    },
};
const locale_khKH = {
    base: baseLocales.khKH,
    dataGrid: dataGridLocales.enUS, // @mui/x-data-grid-premium does not yet have idID
    datePickers: datePickerLocales.enUS,
    karoo: {
        components: {
            KarooSearchTextField: {
                defaultProps: {
                    placeholder: 'រុករក',
                },
            },
            KarooGridToolbarSearchButton: {
                defaultProps: {
                    children: 'រុករក',
                },
            },
        },
        extended: {
            filterOperators: {
                date_range: 'រវាង',
            },
        },
    },
};

/**
 * @description
 * These colors were defined by our designers for our default theme
 */
const defaultCustomizableTheme = {
    palette: {
        primary: {
            main: '#F47735',
            dark: '#BB4800',
            light: '#FFA863',
            contrastText: '#FFFFFF',
        },
        secondary: {
            main: '#333333',
            dark: '#0C0C0C',
            light: '#5C5C5C',
            contrastText: '#FFFFFF',
        },
    },
};
function ThemeProvider({ theme: themeWithoutDefault, locale: localeWithoutDefault, transitions, children, }) {
    const theme = themeWithoutDefault ?? defaultCustomizableTheme;
    const locale = localeWithoutDefault ?? locale_enUS;
    const muiTheme = useMemo(() => {
        // Mixed Locales configuration based on https://mui.com/components/data-grid/localization/#locale-text
        const htmlFontSize = 14;
        const createTypography = () => {
            return {
                /** Needs to be kept in sync with html { font-size: ... }.
                 * Very important so that mui keeps the original font-sizes for typography variants like caption, body1, etc
                 */
                htmlFontSize,
                fontSize: htmlFontSize,
            };
        };
        const computedTheme = createTheme({
            cssVariables: true,
            transitions,
            typography: createTypography(),
            palette: {
                ...theme.palette,
                // Properties that are not meant to be customizable for white labels
                success: {
                    main: '#4CAF50',
                    dark: '#388E3C',
                    light: '#81C784',
                    contrastText: '#FFFFFF',
                },
                error: {
                    main: '#F44336',
                    dark: '#D32F2F',
                    light: '#E57373',
                    contrastText: '#FFFFFF',
                },
                warning: {
                    main: '#FF9800',
                    dark: '#F57C00',
                    light: '#FFB74D',
                    contrastText: '#FFFFFF',
                },
                info: {
                    main: '#2196F3',
                    dark: '#1976D2',
                    light: '#64B5F6',
                    contrastText: '#FFFFFF',
                },
            },
            components: {
                ...locale.karoo.components,
                MuiAutocomplete: {
                    styleOverrides: {
                        paper: {
                            fontSize: '1rem',
                        },
                    },
                },
                MuiTypography: {
                    defaultProps: {
                        variant: 'body2',
                    },
                },
                MuiDialogContentText: {
                    defaultProps: {
                        variant: 'body2',
                    },
                },
                MuiListItemText: {
                    defaultProps: {
                        primaryTypographyProps: {
                            variant: 'body2',
                        },
                    },
                },
                MuiMenuItem: {
                    styleOverrides: {
                        root: {
                            fontSize: '1rem',
                        },
                    },
                },
                MuiCheckbox: {
                    defaultProps: {
                        size: 'small',
                    },
                },
                MuiRadio: {
                    defaultProps: {
                        size: 'small',
                    },
                },
                MuiSwitch: {
                    defaultProps: {
                        size: 'small',
                    },
                },
                MuiChip: {
                    defaultProps: {
                        size: 'small',
                    },
                },
                MuiDataGrid: {
                    styleOverrides: {
                        root: {
                            // Mui uses under 1rem font size for the data grid, which is too small when we use base font size of 14px
                            fontSize: htmlFontSize,
                        },
                    },
                },
                MuiDialogContent: {
                    styleOverrides: {
                        root: {
                            overflow: 'visible',
                        },
                    },
                },
                MuiIconButton: {
                    styleOverrides: {
                        root: ({ ownerState, }) => ({
                            ...(ownerState.disableRipple && {
                                padding: 0,
                            }),
                        }),
                    },
                },
                MuiToggleButtonGroup: {
                    styleOverrides: {
                        root: {
                            '& .MuiToggleButton-root': {
                                borderColor: '#BDBDBD',
                                '&.Mui-selected, &.Mui-selected:hover': {
                                    color: theme.palette.primary.contrastText,
                                    backgroundColor: theme.palette.primary.main,
                                },
                            },
                        },
                    },
                },
                MuiOutlinedInput: {
                    styleOverrides: {
                        root: (() => {
                            // Declared here so that the objects references are stable across renders
                            const readonlyFocusedStyles = {
                                '&.Mui-focused': {
                                    [`& .${outlinedInputClasses.notchedOutline}`]: {
                                        border: `1px solid rgba(0, 0, 0, 0.23)`,
                                    },
                                },
                            };
                            return ({ ownerState }) => {
                                if (ownerState.readOnly) {
                                    return readonlyFocusedStyles;
                                }
                                return undefined;
                            };
                        })(),
                    },
                },
                MuiInputLabel: {
                    styleOverrides: {
                        root: {
                            fontSize: '1rem',
                            '&[data-shrink="true"]': {
                                fontSize: '1.08rem',
                            },
                        },
                    },
                },
                MuiInputBase: {
                    styleOverrides: {
                        root: (() => {
                            // Declared here so that the objects references are stable across renders
                            const baseStyles = { fontSize: '1rem' };
                            const readonlyStyles = {
                                ...baseStyles,
                                [`&:not(.Mui-focused)`]: {
                                    '&:hover': {
                                        [`& .${outlinedInputClasses.notchedOutline}`]: {
                                            borderColor: 'rgba(0, 0, 0, 0.23)',
                                        },
                                    },
                                },
                            };
                            return ({ ownerState }) => {
                                if (ownerState.readOnly) {
                                    return readonlyStyles;
                                }
                                return baseStyles;
                            };
                        })(),
                    },
                },
                MuiFormLabel: {
                    styleOverrides: {
                        root: {
                            [`&.${formLabelClasses.focused}`]: {
                                color: 'rgba(0, 0, 0, 0.6)',
                            },
                        },
                        asterisk: ({ ownerState, theme }) => {
                            if (ownerState.disabled && !ownerState.error) {
                                // Only use grey color when the field is disabled and not in error state.
                                // If it's in error state, we want to use the error color (to maintain mui consistency)
                                return { color: theme.palette.text.disabled };
                            }
                            return { color: '#db3131' };
                        },
                    },
                },
            },
        }, locale.dataGrid, locale.datePickers, locale.base);
        const alphaValues = {
            selected: computedTheme.palette.action.selectedOpacity,
            hover: computedTheme.palette.action.hoverOpacity,
            focus: computedTheme.palette.action.focusOpacity,
        };
        return {
            ...computedTheme,
            palette: {
                ...computedTheme.palette,
                states: {
                    primary: {
                        selected: alpha(theme.palette.primary.main, alphaValues.selected),
                        hover: alpha(theme.palette.primary.main, alphaValues.hover),
                        focus: alpha(theme.palette.primary.main, alphaValues.focus),
                    },
                    secondary: {
                        selected: alpha(theme.palette.secondary.main, alphaValues.selected),
                        hover: alpha(theme.palette.secondary.main, alphaValues.hover),
                        focus: alpha(theme.palette.secondary.main, alphaValues.focus),
                    },
                },
            },
        };
    }, [locale, theme.palette, transitions]);
    return (jsx(ThemeProvider$1, { theme: muiTheme, children: jsx(KarooExtendedLocalesProvider, { value: locale.karoo.extended, children: children }) }));
}

// Allows to use forwardRef with generics (see https://fettblog.eu/typescript-react-generic-forward-refs/#option-3%3A-augment-forwardref)
const forwardRefTyped = forwardRef$1;

const KarooFormStateContext = createContext(null);
function useKarooFormStateContext() {
    const value = useContext(KarooFormStateContext);
    return value;
}
/**
 * Components that currently __use__ this context value __unless__ given an explicit prop that overrides it:
 * - `Autocomplete`
 * - `DatePicker`
 * - `DateTimePicker`
 * - `FormControlLabel` and thus any `Radio`, `Checkbox` or `Switch` within it
 * - `Select`
 * - `TextField`
 * - `TimePicker`
 * - `TimeField`
 */
function KarooFormStateContextProvider({ value: { disabled, readOnly }, children, }) {
    return (jsx(KarooFormStateContext.Provider, { value: useMemo(() => ({ disabled, readOnly }), [disabled, readOnly]), children: children }));
}

const Autocomplete = forwardRefTyped(function Autocomplete({ readOnly: readOnlyProp, disabled: disabledProp, sx = [], ...props }, ref) {
    const formState = useKarooFormStateContext();
    const readOnly = readOnlyProp ?? formState?.readOnly;
    const disabled = disabledProp ?? formState?.disabled;
    return (jsx(Autocomplete__default, { ref: ref, readOnly: readOnly, disabled: disabled, sx: [
            {
                '.MuiAutocomplete-endAdornment > button': readOnly
                    ? {
                        pointerEvents: 'none',
                        cursor: 'none',
                    }
                    : {},
            },
            ...(R.isArray(sx) ? sx : [sx]),
        ], ...props }));
});

/**
 *
 * Demos:
 *
 * - [Button Group](https://mui.com/material-ui/react-button-group/)
 * - [Button](https://mui.com/material-ui/react-button/)
 *
 * API:
 *
 * - [Button API](https://mui.com/material-ui/api/button/)
 * - inherits [ButtonBase API](https://mui.com/material-ui/api/button-base/)
 */
const Button = forwardRefTyped(function Button({ 
/* Designers don't want elevation by default */
disableElevation = true, ...props }, ref) {
    return (jsx(MuiButton__default, { ...props, disableElevation: disableElevation, ref: ref }));
});

/**
 * __DO NOT__ use this for mutations since the user wants to be informed of the mutation status immediately. Use __```CircularProgress```__ instead.
 *
 * __DO__ use this for queries
 */
const CircularProgressDelayed = forwardRefTyped(function CircularProgressDelayed({ delayedProps: { loading = true, delayInMs = 700 /* Following UX best practices from https://mui.com/material-ui/react-progress/#delaying-appearance */, } = {}, ...rest }, ref) {
    return (jsx(Fade__default, { ref: ref, in: loading, style: {
            transitionDelay: loading ? `${delayInMs}ms` : '0ms',
        }, unmountOnExit: true, children: jsx(CircularProgress__default, { ...rest }) }));
});

/**
 * Only use this when you want to show a loading indicator in the center of a container.
 *
 * If you need to style CircularProgressDelayed (e.g - using 'className' or 'sx' props), please use __```CircularProgressDelayed``` directly__ instead
 */
const CircularProgressDelayedAbsolute = forwardRefTyped(function CircularProgressDelayedAbsolute(props, ref) {
    return (jsx(CircularProgressDelayed, { ...props, ref: ref, sx: {
            position: 'absolute',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            margin: 'auto',
        } }));
});

function CircularProgressDelayedCentered({ circularProgressDelayedProps, }) {
    return (jsx(Stack__default, { alignItems: "center", justifyContent: "center", height: "100%", children: jsx(CircularProgressDelayed, { ...circularProgressDelayedProps }) }));
}

function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

var propTypes = {exports: {}};

var reactIs = {exports: {}};

var reactIs_production_min = {};

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredReactIs_production_min;

function requireReactIs_production_min () {
	if (hasRequiredReactIs_production_min) return reactIs_production_min;
	hasRequiredReactIs_production_min = 1;
var b="function"===typeof Symbol&&Symbol.for,c=b?Symbol.for("react.element"):60103,d=b?Symbol.for("react.portal"):60106,e=b?Symbol.for("react.fragment"):60107,f=b?Symbol.for("react.strict_mode"):60108,g=b?Symbol.for("react.profiler"):60114,h=b?Symbol.for("react.provider"):60109,k=b?Symbol.for("react.context"):60110,l=b?Symbol.for("react.async_mode"):60111,m=b?Symbol.for("react.concurrent_mode"):60111,n=b?Symbol.for("react.forward_ref"):60112,p=b?Symbol.for("react.suspense"):60113,q=b?
	Symbol.for("react.suspense_list"):60120,r=b?Symbol.for("react.memo"):60115,t=b?Symbol.for("react.lazy"):60116,v=b?Symbol.for("react.block"):60121,w=b?Symbol.for("react.fundamental"):60117,x=b?Symbol.for("react.responder"):60118,y=b?Symbol.for("react.scope"):60119;
	function z(a){if("object"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}reactIs_production_min.AsyncMode=l;reactIs_production_min.ConcurrentMode=m;reactIs_production_min.ContextConsumer=k;reactIs_production_min.ContextProvider=h;reactIs_production_min.Element=c;reactIs_production_min.ForwardRef=n;reactIs_production_min.Fragment=e;reactIs_production_min.Lazy=t;reactIs_production_min.Memo=r;reactIs_production_min.Portal=d;
	reactIs_production_min.Profiler=g;reactIs_production_min.StrictMode=f;reactIs_production_min.Suspense=p;reactIs_production_min.isAsyncMode=function(a){return A(a)||z(a)===l};reactIs_production_min.isConcurrentMode=A;reactIs_production_min.isContextConsumer=function(a){return z(a)===k};reactIs_production_min.isContextProvider=function(a){return z(a)===h};reactIs_production_min.isElement=function(a){return "object"===typeof a&&null!==a&&a.$$typeof===c};reactIs_production_min.isForwardRef=function(a){return z(a)===n};reactIs_production_min.isFragment=function(a){return z(a)===e};reactIs_production_min.isLazy=function(a){return z(a)===t};
	reactIs_production_min.isMemo=function(a){return z(a)===r};reactIs_production_min.isPortal=function(a){return z(a)===d};reactIs_production_min.isProfiler=function(a){return z(a)===g};reactIs_production_min.isStrictMode=function(a){return z(a)===f};reactIs_production_min.isSuspense=function(a){return z(a)===p};
	reactIs_production_min.isValidElementType=function(a){return "string"===typeof a||"function"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||"object"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};reactIs_production_min.typeOf=z;
	return reactIs_production_min;
}

var reactIs_development = {};

/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredReactIs_development;

function requireReactIs_development () {
	if (hasRequiredReactIs_development) return reactIs_development;
	hasRequiredReactIs_development = 1;



	if (process.env.NODE_ENV !== "production") {
	  (function() {

	// The Symbol used to tag the ReactElement-like types. If there is no native Symbol
	// nor polyfill, then a plain number is used for performance.
	var hasSymbol = typeof Symbol === 'function' && Symbol.for;
	var REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;
	var REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;
	var REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;
	var REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;
	var REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;
	var REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;
	var REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary
	// (unstable) APIs that have been removed. Can we remove the symbols?

	var REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;
	var REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;
	var REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;
	var REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;
	var REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;
	var REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;
	var REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;
	var REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;
	var REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;
	var REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;
	var REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;

	function isValidElementType(type) {
	  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
	  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
	}

	function typeOf(object) {
	  if (typeof object === 'object' && object !== null) {
	    var $$typeof = object.$$typeof;

	    switch ($$typeof) {
	      case REACT_ELEMENT_TYPE:
	        var type = object.type;

	        switch (type) {
	          case REACT_ASYNC_MODE_TYPE:
	          case REACT_CONCURRENT_MODE_TYPE:
	          case REACT_FRAGMENT_TYPE:
	          case REACT_PROFILER_TYPE:
	          case REACT_STRICT_MODE_TYPE:
	          case REACT_SUSPENSE_TYPE:
	            return type;

	          default:
	            var $$typeofType = type && type.$$typeof;

	            switch ($$typeofType) {
	              case REACT_CONTEXT_TYPE:
	              case REACT_FORWARD_REF_TYPE:
	              case REACT_LAZY_TYPE:
	              case REACT_MEMO_TYPE:
	              case REACT_PROVIDER_TYPE:
	                return $$typeofType;

	              default:
	                return $$typeof;
	            }

	        }

	      case REACT_PORTAL_TYPE:
	        return $$typeof;
	    }
	  }

	  return undefined;
	} // AsyncMode is deprecated along with isAsyncMode

	var AsyncMode = REACT_ASYNC_MODE_TYPE;
	var ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;
	var ContextConsumer = REACT_CONTEXT_TYPE;
	var ContextProvider = REACT_PROVIDER_TYPE;
	var Element = REACT_ELEMENT_TYPE;
	var ForwardRef = REACT_FORWARD_REF_TYPE;
	var Fragment = REACT_FRAGMENT_TYPE;
	var Lazy = REACT_LAZY_TYPE;
	var Memo = REACT_MEMO_TYPE;
	var Portal = REACT_PORTAL_TYPE;
	var Profiler = REACT_PROFILER_TYPE;
	var StrictMode = REACT_STRICT_MODE_TYPE;
	var Suspense = REACT_SUSPENSE_TYPE;
	var hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated

	function isAsyncMode(object) {
	  {
	    if (!hasWarnedAboutDeprecatedIsAsyncMode) {
	      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint

	      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');
	    }
	  }

	  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
	}
	function isConcurrentMode(object) {
	  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
	}
	function isContextConsumer(object) {
	  return typeOf(object) === REACT_CONTEXT_TYPE;
	}
	function isContextProvider(object) {
	  return typeOf(object) === REACT_PROVIDER_TYPE;
	}
	function isElement(object) {
	  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
	}
	function isForwardRef(object) {
	  return typeOf(object) === REACT_FORWARD_REF_TYPE;
	}
	function isFragment(object) {
	  return typeOf(object) === REACT_FRAGMENT_TYPE;
	}
	function isLazy(object) {
	  return typeOf(object) === REACT_LAZY_TYPE;
	}
	function isMemo(object) {
	  return typeOf(object) === REACT_MEMO_TYPE;
	}
	function isPortal(object) {
	  return typeOf(object) === REACT_PORTAL_TYPE;
	}
	function isProfiler(object) {
	  return typeOf(object) === REACT_PROFILER_TYPE;
	}
	function isStrictMode(object) {
	  return typeOf(object) === REACT_STRICT_MODE_TYPE;
	}
	function isSuspense(object) {
	  return typeOf(object) === REACT_SUSPENSE_TYPE;
	}

	reactIs_development.AsyncMode = AsyncMode;
	reactIs_development.ConcurrentMode = ConcurrentMode;
	reactIs_development.ContextConsumer = ContextConsumer;
	reactIs_development.ContextProvider = ContextProvider;
	reactIs_development.Element = Element;
	reactIs_development.ForwardRef = ForwardRef;
	reactIs_development.Fragment = Fragment;
	reactIs_development.Lazy = Lazy;
	reactIs_development.Memo = Memo;
	reactIs_development.Portal = Portal;
	reactIs_development.Profiler = Profiler;
	reactIs_development.StrictMode = StrictMode;
	reactIs_development.Suspense = Suspense;
	reactIs_development.isAsyncMode = isAsyncMode;
	reactIs_development.isConcurrentMode = isConcurrentMode;
	reactIs_development.isContextConsumer = isContextConsumer;
	reactIs_development.isContextProvider = isContextProvider;
	reactIs_development.isElement = isElement;
	reactIs_development.isForwardRef = isForwardRef;
	reactIs_development.isFragment = isFragment;
	reactIs_development.isLazy = isLazy;
	reactIs_development.isMemo = isMemo;
	reactIs_development.isPortal = isPortal;
	reactIs_development.isProfiler = isProfiler;
	reactIs_development.isStrictMode = isStrictMode;
	reactIs_development.isSuspense = isSuspense;
	reactIs_development.isValidElementType = isValidElementType;
	reactIs_development.typeOf = typeOf;
	  })();
	}
	return reactIs_development;
}

var hasRequiredReactIs;

function requireReactIs () {
	if (hasRequiredReactIs) return reactIs.exports;
	hasRequiredReactIs = 1;

	if (process.env.NODE_ENV === 'production') {
	  reactIs.exports = requireReactIs_production_min();
	} else {
	  reactIs.exports = requireReactIs_development();
	}
	return reactIs.exports;
}

/*
object-assign
(c) Sindre Sorhus
@license MIT
*/

var objectAssign;
var hasRequiredObjectAssign;

function requireObjectAssign () {
	if (hasRequiredObjectAssign) return objectAssign;
	hasRequiredObjectAssign = 1;
	/* eslint-disable no-unused-vars */
	var getOwnPropertySymbols = Object.getOwnPropertySymbols;
	var hasOwnProperty = Object.prototype.hasOwnProperty;
	var propIsEnumerable = Object.prototype.propertyIsEnumerable;

	function toObject(val) {
		if (val === null || val === undefined) {
			throw new TypeError('Object.assign cannot be called with null or undefined');
		}

		return Object(val);
	}

	function shouldUseNative() {
		try {
			if (!Object.assign) {
				return false;
			}

			// Detect buggy property enumeration order in older V8 versions.

			// https://bugs.chromium.org/p/v8/issues/detail?id=4118
			var test1 = new String('abc');  // eslint-disable-line no-new-wrappers
			test1[5] = 'de';
			if (Object.getOwnPropertyNames(test1)[0] === '5') {
				return false;
			}

			// https://bugs.chromium.org/p/v8/issues/detail?id=3056
			var test2 = {};
			for (var i = 0; i < 10; i++) {
				test2['_' + String.fromCharCode(i)] = i;
			}
			var order2 = Object.getOwnPropertyNames(test2).map(function (n) {
				return test2[n];
			});
			if (order2.join('') !== '0123456789') {
				return false;
			}

			// https://bugs.chromium.org/p/v8/issues/detail?id=3056
			var test3 = {};
			'abcdefghijklmnopqrst'.split('').forEach(function (letter) {
				test3[letter] = letter;
			});
			if (Object.keys(Object.assign({}, test3)).join('') !==
					'abcdefghijklmnopqrst') {
				return false;
			}

			return true;
		} catch (err) {
			// We don't expect any of the above to throw, but better to be safe.
			return false;
		}
	}

	objectAssign = shouldUseNative() ? Object.assign : function (target, source) {
		var from;
		var to = toObject(target);
		var symbols;

		for (var s = 1; s < arguments.length; s++) {
			from = Object(arguments[s]);

			for (var key in from) {
				if (hasOwnProperty.call(from, key)) {
					to[key] = from[key];
				}
			}

			if (getOwnPropertySymbols) {
				symbols = getOwnPropertySymbols(from);
				for (var i = 0; i < symbols.length; i++) {
					if (propIsEnumerable.call(from, symbols[i])) {
						to[symbols[i]] = from[symbols[i]];
					}
				}
			}
		}

		return to;
	};
	return objectAssign;
}

/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var ReactPropTypesSecret_1;
var hasRequiredReactPropTypesSecret;

function requireReactPropTypesSecret () {
	if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;
	hasRequiredReactPropTypesSecret = 1;

	var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';

	ReactPropTypesSecret_1 = ReactPropTypesSecret;
	return ReactPropTypesSecret_1;
}

var has;
var hasRequiredHas;

function requireHas () {
	if (hasRequiredHas) return has;
	hasRequiredHas = 1;
	has = Function.call.bind(Object.prototype.hasOwnProperty);
	return has;
}

/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var checkPropTypes_1;
var hasRequiredCheckPropTypes;

function requireCheckPropTypes () {
	if (hasRequiredCheckPropTypes) return checkPropTypes_1;
	hasRequiredCheckPropTypes = 1;

	var printWarning = function() {};

	if (process.env.NODE_ENV !== 'production') {
	  var ReactPropTypesSecret = requireReactPropTypesSecret();
	  var loggedTypeFailures = {};
	  var has = requireHas();

	  printWarning = function(text) {
	    var message = 'Warning: ' + text;
	    if (typeof console !== 'undefined') {
	      console.error(message);
	    }
	    try {
	      // --- Welcome to debugging React ---
	      // This error was thrown as a convenience so that you can use this stack
	      // to find the callsite that caused this warning to fire.
	      throw new Error(message);
	    } catch (x) { /**/ }
	  };
	}

	/**
	 * Assert that the values match with the type specs.
	 * Error messages are memorized and will only be shown once.
	 *
	 * @param {object} typeSpecs Map of name to a ReactPropType
	 * @param {object} values Runtime values that need to be type-checked
	 * @param {string} location e.g. "prop", "context", "child context"
	 * @param {string} componentName Name of the component for error messages.
	 * @param {?Function} getStack Returns the component stack.
	 * @private
	 */
	function checkPropTypes(typeSpecs, values, location, componentName, getStack) {
	  if (process.env.NODE_ENV !== 'production') {
	    for (var typeSpecName in typeSpecs) {
	      if (has(typeSpecs, typeSpecName)) {
	        var error;
	        // Prop type validation may throw. In case they do, we don't want to
	        // fail the render phase where it didn't fail before. So we log it.
	        // After these have been cleaned up, we'll let them throw.
	        try {
	          // This is intentionally an invariant that gets caught. It's the same
	          // behavior as without this statement except with a better message.
	          if (typeof typeSpecs[typeSpecName] !== 'function') {
	            var err = Error(
	              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +
	              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +
	              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'
	            );
	            err.name = 'Invariant Violation';
	            throw err;
	          }
	          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);
	        } catch (ex) {
	          error = ex;
	        }
	        if (error && !(error instanceof Error)) {
	          printWarning(
	            (componentName || 'React class') + ': type specification of ' +
	            location + ' `' + typeSpecName + '` is invalid; the type checker ' +
	            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +
	            'You may have forgotten to pass an argument to the type checker ' +
	            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +
	            'shape all require an argument).'
	          );
	        }
	        if (error instanceof Error && !(error.message in loggedTypeFailures)) {
	          // Only monitor this failure once because there tends to be a lot of the
	          // same error.
	          loggedTypeFailures[error.message] = true;

	          var stack = getStack ? getStack() : '';

	          printWarning(
	            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')
	          );
	        }
	      }
	    }
	  }
	}

	/**
	 * Resets warning cache when testing.
	 *
	 * @private
	 */
	checkPropTypes.resetWarningCache = function() {
	  if (process.env.NODE_ENV !== 'production') {
	    loggedTypeFailures = {};
	  }
	};

	checkPropTypes_1 = checkPropTypes;
	return checkPropTypes_1;
}

/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var factoryWithTypeCheckers;
var hasRequiredFactoryWithTypeCheckers;

function requireFactoryWithTypeCheckers () {
	if (hasRequiredFactoryWithTypeCheckers) return factoryWithTypeCheckers;
	hasRequiredFactoryWithTypeCheckers = 1;

	var ReactIs = requireReactIs();
	var assign = requireObjectAssign();

	var ReactPropTypesSecret = requireReactPropTypesSecret();
	var has = requireHas();
	var checkPropTypes = requireCheckPropTypes();

	var printWarning = function() {};

	if (process.env.NODE_ENV !== 'production') {
	  printWarning = function(text) {
	    var message = 'Warning: ' + text;
	    if (typeof console !== 'undefined') {
	      console.error(message);
	    }
	    try {
	      // --- Welcome to debugging React ---
	      // This error was thrown as a convenience so that you can use this stack
	      // to find the callsite that caused this warning to fire.
	      throw new Error(message);
	    } catch (x) {}
	  };
	}

	function emptyFunctionThatReturnsNull() {
	  return null;
	}

	factoryWithTypeCheckers = function(isValidElement, throwOnDirectAccess) {
	  /* global Symbol */
	  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;
	  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.

	  /**
	   * Returns the iterator method function contained on the iterable object.
	   *
	   * Be sure to invoke the function with the iterable as context:
	   *
	   *     var iteratorFn = getIteratorFn(myIterable);
	   *     if (iteratorFn) {
	   *       var iterator = iteratorFn.call(myIterable);
	   *       ...
	   *     }
	   *
	   * @param {?object} maybeIterable
	   * @return {?function}
	   */
	  function getIteratorFn(maybeIterable) {
	    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);
	    if (typeof iteratorFn === 'function') {
	      return iteratorFn;
	    }
	  }

	  /**
	   * Collection of methods that allow declaration and validation of props that are
	   * supplied to React components. Example usage:
	   *
	   *   var Props = require('ReactPropTypes');
	   *   var MyArticle = React.createClass({
	   *     propTypes: {
	   *       // An optional string prop named "description".
	   *       description: Props.string,
	   *
	   *       // A required enum prop named "category".
	   *       category: Props.oneOf(['News','Photos']).isRequired,
	   *
	   *       // A prop named "dialog" that requires an instance of Dialog.
	   *       dialog: Props.instanceOf(Dialog).isRequired
	   *     },
	   *     render: function() { ... }
	   *   });
	   *
	   * A more formal specification of how these methods are used:
	   *
	   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)
	   *   decl := ReactPropTypes.{type}(.isRequired)?
	   *
	   * Each and every declaration produces a function with the same signature. This
	   * allows the creation of custom validation functions. For example:
	   *
	   *  var MyLink = React.createClass({
	   *    propTypes: {
	   *      // An optional string or URI prop named "href".
	   *      href: function(props, propName, componentName) {
	   *        var propValue = props[propName];
	   *        if (propValue != null && typeof propValue !== 'string' &&
	   *            !(propValue instanceof URI)) {
	   *          return new Error(
	   *            'Expected a string or an URI for ' + propName + ' in ' +
	   *            componentName
	   *          );
	   *        }
	   *      }
	   *    },
	   *    render: function() {...}
	   *  });
	   *
	   * @internal
	   */

	  var ANONYMOUS = '<<anonymous>>';

	  // Important!
	  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.
	  var ReactPropTypes = {
	    array: createPrimitiveTypeChecker('array'),
	    bigint: createPrimitiveTypeChecker('bigint'),
	    bool: createPrimitiveTypeChecker('boolean'),
	    func: createPrimitiveTypeChecker('function'),
	    number: createPrimitiveTypeChecker('number'),
	    object: createPrimitiveTypeChecker('object'),
	    string: createPrimitiveTypeChecker('string'),
	    symbol: createPrimitiveTypeChecker('symbol'),

	    any: createAnyTypeChecker(),
	    arrayOf: createArrayOfTypeChecker,
	    element: createElementTypeChecker(),
	    elementType: createElementTypeTypeChecker(),
	    instanceOf: createInstanceTypeChecker,
	    node: createNodeChecker(),
	    objectOf: createObjectOfTypeChecker,
	    oneOf: createEnumTypeChecker,
	    oneOfType: createUnionTypeChecker,
	    shape: createShapeTypeChecker,
	    exact: createStrictShapeTypeChecker,
	  };

	  /**
	   * inlined Object.is polyfill to avoid requiring consumers ship their own
	   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is
	   */
	  /*eslint-disable no-self-compare*/
	  function is(x, y) {
	    // SameValue algorithm
	    if (x === y) {
	      // Steps 1-5, 7-10
	      // Steps 6.b-6.e: +0 != -0
	      return x !== 0 || 1 / x === 1 / y;
	    } else {
	      // Step 6.a: NaN == NaN
	      return x !== x && y !== y;
	    }
	  }
	  /*eslint-enable no-self-compare*/

	  /**
	   * We use an Error-like object for backward compatibility as people may call
	   * PropTypes directly and inspect their output. However, we don't use real
	   * Errors anymore. We don't inspect their stack anyway, and creating them
	   * is prohibitively expensive if they are created too often, such as what
	   * happens in oneOfType() for any type before the one that matched.
	   */
	  function PropTypeError(message, data) {
	    this.message = message;
	    this.data = data && typeof data === 'object' ? data: {};
	    this.stack = '';
	  }
	  // Make `instanceof Error` still work for returned errors.
	  PropTypeError.prototype = Error.prototype;

	  function createChainableTypeChecker(validate) {
	    if (process.env.NODE_ENV !== 'production') {
	      var manualPropTypeCallCache = {};
	      var manualPropTypeWarningCount = 0;
	    }
	    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {
	      componentName = componentName || ANONYMOUS;
	      propFullName = propFullName || propName;

	      if (secret !== ReactPropTypesSecret) {
	        if (throwOnDirectAccess) {
	          // New behavior only for users of `prop-types` package
	          var err = new Error(
	            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +
	            'Use `PropTypes.checkPropTypes()` to call them. ' +
	            'Read more at http://fb.me/use-check-prop-types'
	          );
	          err.name = 'Invariant Violation';
	          throw err;
	        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {
	          // Old behavior for people using React.PropTypes
	          var cacheKey = componentName + ':' + propName;
	          if (
	            !manualPropTypeCallCache[cacheKey] &&
	            // Avoid spamming the console because they are often not actionable except for lib authors
	            manualPropTypeWarningCount < 3
	          ) {
	            printWarning(
	              'You are manually calling a React.PropTypes validation ' +
	              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +
	              'and will throw in the standalone `prop-types` package. ' +
	              'You may be seeing this warning due to a third-party PropTypes ' +
	              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'
	            );
	            manualPropTypeCallCache[cacheKey] = true;
	            manualPropTypeWarningCount++;
	          }
	        }
	      }
	      if (props[propName] == null) {
	        if (isRequired) {
	          if (props[propName] === null) {
	            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));
	          }
	          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));
	        }
	        return null;
	      } else {
	        return validate(props, propName, componentName, location, propFullName);
	      }
	    }

	    var chainedCheckType = checkType.bind(null, false);
	    chainedCheckType.isRequired = checkType.bind(null, true);

	    return chainedCheckType;
	  }

	  function createPrimitiveTypeChecker(expectedType) {
	    function validate(props, propName, componentName, location, propFullName, secret) {
	      var propValue = props[propName];
	      var propType = getPropType(propValue);
	      if (propType !== expectedType) {
	        // `propValue` being instance of, say, date/regexp, pass the 'object'
	        // check, but we can offer a more precise error message here rather than
	        // 'of type `object`'.
	        var preciseType = getPreciseType(propValue);

	        return new PropTypeError(
	          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),
	          {expectedType: expectedType}
	        );
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createAnyTypeChecker() {
	    return createChainableTypeChecker(emptyFunctionThatReturnsNull);
	  }

	  function createArrayOfTypeChecker(typeChecker) {
	    function validate(props, propName, componentName, location, propFullName) {
	      if (typeof typeChecker !== 'function') {
	        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');
	      }
	      var propValue = props[propName];
	      if (!Array.isArray(propValue)) {
	        var propType = getPropType(propValue);
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));
	      }
	      for (var i = 0; i < propValue.length; i++) {
	        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);
	        if (error instanceof Error) {
	          return error;
	        }
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createElementTypeChecker() {
	    function validate(props, propName, componentName, location, propFullName) {
	      var propValue = props[propName];
	      if (!isValidElement(propValue)) {
	        var propType = getPropType(propValue);
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createElementTypeTypeChecker() {
	    function validate(props, propName, componentName, location, propFullName) {
	      var propValue = props[propName];
	      if (!ReactIs.isValidElementType(propValue)) {
	        var propType = getPropType(propValue);
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createInstanceTypeChecker(expectedClass) {
	    function validate(props, propName, componentName, location, propFullName) {
	      if (!(props[propName] instanceof expectedClass)) {
	        var expectedClassName = expectedClass.name || ANONYMOUS;
	        var actualClassName = getClassName(props[propName]);
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createEnumTypeChecker(expectedValues) {
	    if (!Array.isArray(expectedValues)) {
	      if (process.env.NODE_ENV !== 'production') {
	        if (arguments.length > 1) {
	          printWarning(
	            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +
	            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'
	          );
	        } else {
	          printWarning('Invalid argument supplied to oneOf, expected an array.');
	        }
	      }
	      return emptyFunctionThatReturnsNull;
	    }

	    function validate(props, propName, componentName, location, propFullName) {
	      var propValue = props[propName];
	      for (var i = 0; i < expectedValues.length; i++) {
	        if (is(propValue, expectedValues[i])) {
	          return null;
	        }
	      }

	      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {
	        var type = getPreciseType(value);
	        if (type === 'symbol') {
	          return String(value);
	        }
	        return value;
	      });
	      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createObjectOfTypeChecker(typeChecker) {
	    function validate(props, propName, componentName, location, propFullName) {
	      if (typeof typeChecker !== 'function') {
	        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');
	      }
	      var propValue = props[propName];
	      var propType = getPropType(propValue);
	      if (propType !== 'object') {
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));
	      }
	      for (var key in propValue) {
	        if (has(propValue, key)) {
	          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);
	          if (error instanceof Error) {
	            return error;
	          }
	        }
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createUnionTypeChecker(arrayOfTypeCheckers) {
	    if (!Array.isArray(arrayOfTypeCheckers)) {
	      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : undefined;
	      return emptyFunctionThatReturnsNull;
	    }

	    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {
	      var checker = arrayOfTypeCheckers[i];
	      if (typeof checker !== 'function') {
	        printWarning(
	          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +
	          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'
	        );
	        return emptyFunctionThatReturnsNull;
	      }
	    }

	    function validate(props, propName, componentName, location, propFullName) {
	      var expectedTypes = [];
	      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {
	        var checker = arrayOfTypeCheckers[i];
	        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);
	        if (checkerResult == null) {
	          return null;
	        }
	        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {
	          expectedTypes.push(checkerResult.data.expectedType);
	        }
	      }
	      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';
	      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createNodeChecker() {
	    function validate(props, propName, componentName, location, propFullName) {
	      if (!isNode(props[propName])) {
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function invalidValidatorError(componentName, location, propFullName, key, type) {
	    return new PropTypeError(
	      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +
	      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'
	    );
	  }

	  function createShapeTypeChecker(shapeTypes) {
	    function validate(props, propName, componentName, location, propFullName) {
	      var propValue = props[propName];
	      var propType = getPropType(propValue);
	      if (propType !== 'object') {
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));
	      }
	      for (var key in shapeTypes) {
	        var checker = shapeTypes[key];
	        if (typeof checker !== 'function') {
	          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
	        }
	        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);
	        if (error) {
	          return error;
	        }
	      }
	      return null;
	    }
	    return createChainableTypeChecker(validate);
	  }

	  function createStrictShapeTypeChecker(shapeTypes) {
	    function validate(props, propName, componentName, location, propFullName) {
	      var propValue = props[propName];
	      var propType = getPropType(propValue);
	      if (propType !== 'object') {
	        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));
	      }
	      // We need to check all keys in case some are required but missing from props.
	      var allKeys = assign({}, props[propName], shapeTypes);
	      for (var key in allKeys) {
	        var checker = shapeTypes[key];
	        if (has(shapeTypes, key) && typeof checker !== 'function') {
	          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
	        }
	        if (!checker) {
	          return new PropTypeError(
	            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +
	            '\nBad object: ' + JSON.stringify(props[propName], null, '  ') +
	            '\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')
	          );
	        }
	        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);
	        if (error) {
	          return error;
	        }
	      }
	      return null;
	    }

	    return createChainableTypeChecker(validate);
	  }

	  function isNode(propValue) {
	    switch (typeof propValue) {
	      case 'number':
	      case 'string':
	      case 'undefined':
	        return true;
	      case 'boolean':
	        return !propValue;
	      case 'object':
	        if (Array.isArray(propValue)) {
	          return propValue.every(isNode);
	        }
	        if (propValue === null || isValidElement(propValue)) {
	          return true;
	        }

	        var iteratorFn = getIteratorFn(propValue);
	        if (iteratorFn) {
	          var iterator = iteratorFn.call(propValue);
	          var step;
	          if (iteratorFn !== propValue.entries) {
	            while (!(step = iterator.next()).done) {
	              if (!isNode(step.value)) {
	                return false;
	              }
	            }
	          } else {
	            // Iterator will provide entry [k,v] tuples rather than values.
	            while (!(step = iterator.next()).done) {
	              var entry = step.value;
	              if (entry) {
	                if (!isNode(entry[1])) {
	                  return false;
	                }
	              }
	            }
	          }
	        } else {
	          return false;
	        }

	        return true;
	      default:
	        return false;
	    }
	  }

	  function isSymbol(propType, propValue) {
	    // Native Symbol.
	    if (propType === 'symbol') {
	      return true;
	    }

	    // falsy value can't be a Symbol
	    if (!propValue) {
	      return false;
	    }

	    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'
	    if (propValue['@@toStringTag'] === 'Symbol') {
	      return true;
	    }

	    // Fallback for non-spec compliant Symbols which are polyfilled.
	    if (typeof Symbol === 'function' && propValue instanceof Symbol) {
	      return true;
	    }

	    return false;
	  }

	  // Equivalent of `typeof` but with special handling for array and regexp.
	  function getPropType(propValue) {
	    var propType = typeof propValue;
	    if (Array.isArray(propValue)) {
	      return 'array';
	    }
	    if (propValue instanceof RegExp) {
	      // Old webkits (at least until Android 4.0) return 'function' rather than
	      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/
	      // passes PropTypes.object.
	      return 'object';
	    }
	    if (isSymbol(propType, propValue)) {
	      return 'symbol';
	    }
	    return propType;
	  }

	  // This handles more types than `getPropType`. Only used for error messages.
	  // See `createPrimitiveTypeChecker`.
	  function getPreciseType(propValue) {
	    if (typeof propValue === 'undefined' || propValue === null) {
	      return '' + propValue;
	    }
	    var propType = getPropType(propValue);
	    if (propType === 'object') {
	      if (propValue instanceof Date) {
	        return 'date';
	      } else if (propValue instanceof RegExp) {
	        return 'regexp';
	      }
	    }
	    return propType;
	  }

	  // Returns a string that is postfixed to a warning about an invalid type.
	  // For example, "undefined" or "of type array"
	  function getPostfixForTypeWarning(value) {
	    var type = getPreciseType(value);
	    switch (type) {
	      case 'array':
	      case 'object':
	        return 'an ' + type;
	      case 'boolean':
	      case 'date':
	      case 'regexp':
	        return 'a ' + type;
	      default:
	        return type;
	    }
	  }

	  // Returns class name of the object, if any.
	  function getClassName(propValue) {
	    if (!propValue.constructor || !propValue.constructor.name) {
	      return ANONYMOUS;
	    }
	    return propValue.constructor.name;
	  }

	  ReactPropTypes.checkPropTypes = checkPropTypes;
	  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;
	  ReactPropTypes.PropTypes = ReactPropTypes;

	  return ReactPropTypes;
	};
	return factoryWithTypeCheckers;
}

/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var factoryWithThrowingShims;
var hasRequiredFactoryWithThrowingShims;

function requireFactoryWithThrowingShims () {
	if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;
	hasRequiredFactoryWithThrowingShims = 1;

	var ReactPropTypesSecret = requireReactPropTypesSecret();

	function emptyFunction() {}
	function emptyFunctionWithReset() {}
	emptyFunctionWithReset.resetWarningCache = emptyFunction;

	factoryWithThrowingShims = function() {
	  function shim(props, propName, componentName, location, propFullName, secret) {
	    if (secret === ReactPropTypesSecret) {
	      // It is still safe when called from React.
	      return;
	    }
	    var err = new Error(
	      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +
	      'Use PropTypes.checkPropTypes() to call them. ' +
	      'Read more at http://fb.me/use-check-prop-types'
	    );
	    err.name = 'Invariant Violation';
	    throw err;
	  }	  shim.isRequired = shim;
	  function getShim() {
	    return shim;
	  }	  // Important!
	  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.
	  var ReactPropTypes = {
	    array: shim,
	    bigint: shim,
	    bool: shim,
	    func: shim,
	    number: shim,
	    object: shim,
	    string: shim,
	    symbol: shim,

	    any: shim,
	    arrayOf: getShim,
	    element: shim,
	    elementType: shim,
	    instanceOf: getShim,
	    node: shim,
	    objectOf: getShim,
	    oneOf: getShim,
	    oneOfType: getShim,
	    shape: getShim,
	    exact: getShim,

	    checkPropTypes: emptyFunctionWithReset,
	    resetWarningCache: emptyFunction
	  };

	  ReactPropTypes.PropTypes = ReactPropTypes;

	  return ReactPropTypes;
	};
	return factoryWithThrowingShims;
}

/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

if (process.env.NODE_ENV !== 'production') {
  var ReactIs = requireReactIs();

  // By explicitly using `prop-types` you are opting into new development behavior.
  // http://fb.me/prop-types-in-prod
  var throwOnDirectAccess = true;
  propTypes.exports = requireFactoryWithTypeCheckers()(ReactIs.isElement, throwOnDirectAccess);
} else {
  // By explicitly using `prop-types` you are opting into new production behavior.
  // http://fb.me/prop-types-in-prod
  propTypes.exports = requireFactoryWithThrowingShims()();
}

var propTypesExports = propTypes.exports;
var PropTypes = /*@__PURE__*/getDefaultExportFromCjs(propTypesExports);

/* eslint-disable */
/**
 * https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
 * @deprecated Use `globalThis` instead.
 */
var ponyfillGlobal = typeof window != 'undefined' && window.Math == Math ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();

const refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);

/**
 * TODO v5: consider making it private
 *
 * passes {value} to {ref}
 *
 * WARNING: Be sure to only call this inside a callback that is passed as a ref.
 * Otherwise, make sure to cleanup the previous {ref} if it changes. See
 * https://github.com/mui/material-ui/issues/13539
 *
 * Useful if you want to expose the ref of an inner component to the public API
 * while still using it inside the component.
 * @param ref A ref callback or ref object. If anything falsy, this is a no-op.
 */
function setRef(ref, value) {
  if (typeof ref === 'function') {
    ref(value);
  } else if (ref) {
    ref.current = value;
  }
}

/**
 * A version of `React.useLayoutEffect` that does not show a warning when server-side rendering.
 * This is useful for effects that are only needed for client-side rendering but not for SSR.
 *
 * Before you use this hook, make sure to read https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85
 * and confirm it doesn't apply to your use-case.
 */
const useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;

let globalId = 0;

// TODO React 17: Remove `useGlobalId` once React 17 support is removed
function useGlobalId(idOverride) {
  const [defaultId, setDefaultId] = React.useState(idOverride);
  const id = defaultId;
  React.useEffect(() => {
    if (defaultId == null) {
      // Fallback to this default id when possible.
      // Use the incrementing value for client-side rendering only.
      // We can't use it server-side.
      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem
      globalId += 1;
      setDefaultId(`mui-${globalId}`);
    }
  }, [defaultId]);
  return id;
}

// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why
const safeReact = {
  ...React
};
const maybeReactUseId = safeReact.useId;

/**
 *
 * @example <div id={useId()} />
 * @param idOverride
 * @returns {string}
 */
function useId(idOverride) {
  // React.useId() is only available from React 17.0.0.
  if (maybeReactUseId !== undefined) {
    const reactId = maybeReactUseId();
    return reactId;
  }

  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler
  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.
  return useGlobalId(idOverride);
}

/**
 * Takes an array of refs and returns a new ref which will apply any modification to all of the refs.
 * This is useful when you want to have the ref used in multiple places.
 *
 * ```tsx
 * const rootRef = React.useRef<Instance>(null);
 * const refFork = useForkRef(rootRef, props.ref);
 *
 * return (
 *   <Root {...props} ref={refFork} />
 * );
 * ```
 *
 * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.
 * @returns {React.RefCallback<Instance> | null} The new ref callback.
 */
function useForkRef(...refs) {
  /**
   * This will create a new function if the refs passed to this hook change and are all defined.
   * This means react will call the old forkRef with `null` and the new forkRef
   * with the ref. Cleanup naturally emerges from this behavior.
   */
  return React.useMemo(() => {
    if (refs.every(ref => ref == null)) {
      return null;
    }
    return instance => {
      refs.forEach(ref => {
        setRef(ref, instance);
      });
    };
    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, refs);
}

const UNINITIALIZED = {};

/**
 * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional
 * initialization argument, so the initialization function doesn't need to be an inline closure.
 *
 * @usage
 *   const ref = useLazyRef(sortColumns, columns)
 */
function useLazyRef(init, initArg) {
  const ref = React.useRef(UNINITIALIZED);
  if (ref.current === UNINITIALIZED) {
    ref.current = init(initArg);
  }
  return ref;
}

const EMPTY$1 = [];

/**
 * A React.useEffect equivalent that runs once, when the component is mounted.
 */
function useOnMount(fn) {
  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array
  /* eslint-disable react-hooks/exhaustive-deps */
  React.useEffect(fn, EMPTY$1);
  /* eslint-enable react-hooks/exhaustive-deps */
}

class Timeout {
  static create() {
    return new Timeout();
  }
  currentId = null;

  /**
   * Executes `fn` after `delay`, clearing any previously scheduled call.
   */
  start(delay, fn) {
    this.clear();
    this.currentId = setTimeout(() => {
      this.currentId = null;
      fn();
    }, delay);
  }
  clear = () => {
    if (this.currentId !== null) {
      clearTimeout(this.currentId);
      this.currentId = null;
    }
  };
  disposeEffect = () => {
    return this.clear;
  };
}
function useTimeout() {
  const timeout = useLazyRef(Timeout.create).current;
  useOnMount(timeout.disposeEffect);
  return timeout;
}

/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0
   ---
   These rules are preventing the performance optimizations below.
 */

/**
 * Compose classes from multiple sources.
 *
 * @example
 * ```tsx
 * const slots = {
 *  root: ['root', 'primary'],
 *  label: ['label'],
 * };
 *
 * const getUtilityClass = (slot) => `MuiButton-${slot}`;
 *
 * const classes = {
 *   root: 'my-root-class',
 * };
 *
 * const output = composeClasses(slots, getUtilityClass, classes);
 * // {
 * //   root: 'MuiButton-root MuiButton-primary my-root-class',
 * //   label: 'MuiButton-label',
 * // }
 * ```
 *
 * @param slots a list of classes for each possible slot
 * @param getUtilityClass a function to resolve the class based on the slot name
 * @param classes the input classes from props
 * @returns the resolved classes for all slots
 */
function composeClasses(slots, getUtilityClass, classes = undefined) {
  const output = {};
  for (const slotName in slots) {
    const slot = slots[slotName];
    let buffer = '';
    let start = true;
    for (let i = 0; i < slot.length; i += 1) {
      const value = slot[i];
      if (value) {
        buffer += (start === true ? '' : ' ') + getUtilityClass(value);
        start = false;
        if (classes && classes[value]) {
          buffer += ' ' + classes[value];
        }
      }
    }
    output[slotName] = buffer;
  }
  return output;
}

const defaultGenerator = componentName => componentName;
const createClassNameGenerator = () => {
  let generate = defaultGenerator;
  return {
    configure(generator) {
      generate = generator;
    },
    generate(componentName) {
      return generate(componentName);
    },
    reset() {
      generate = defaultGenerator;
    }
  };
};
const ClassNameGenerator = createClassNameGenerator();

const globalStateClasses = {
  active: 'active',
  checked: 'checked',
  completed: 'completed',
  disabled: 'disabled',
  error: 'error',
  expanded: 'expanded',
  focused: 'focused',
  focusVisible: 'focusVisible',
  open: 'open',
  readOnly: 'readOnly',
  required: 'required',
  selected: 'selected'
};
function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {
  const globalStateClass = globalStateClasses[slot];
  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;
}

function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {
  const result = {};
  slots.forEach(slot => {
    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);
  });
  return result;
}

function r(e){var t,f,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=" "),n+=f);}else for(f in e)e[f]&&(n&&(n+=" "),n+=f);return n}function clsx(){for(var e,t,f=0,n="",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=" "),n+=t);return n}

/**
 * @ignore - do not document.
 */

// Store the license information in a global, so it can be shared
// when module duplication occurs. The duplication of the modules can happen
// if using multiple version of MUI X at the same time of the bundler
// decide to duplicate to improve the size of the chunks.
// eslint-disable-next-line no-underscore-dangle
ponyfillGlobal.__MUI_LICENSE_INFO__ = ponyfillGlobal.__MUI_LICENSE_INFO__ || {
  key: undefined
};
class LicenseInfo {
  static getLicenseInfo() {
    // eslint-disable-next-line no-underscore-dangle
    return ponyfillGlobal.__MUI_LICENSE_INFO__;
  }
  static getLicenseKey() {
    return LicenseInfo.getLicenseInfo().key;
  }
  static setLicenseKey(key) {
    const licenseInfo = LicenseInfo.getLicenseInfo();
    licenseInfo.key = key;
  }
}

function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}

const is = Object.is;
function fastObjectShallowCompare(a, b) {
  if (a === b) {
    return true;
  }
  if (!(a instanceof Object) || !(b instanceof Object)) {
    return false;
  }
  let aLength = 0;
  let bLength = 0;

  /* eslint-disable guard-for-in */
  for (const key in a) {
    aLength += 1;
    if (!is(a[key], b[key])) {
      return false;
    }
    if (!(key in b)) {
      return false;
    }
  }

  /* eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars */
  for (const _ in b) {
    bLength += 1;
  }
  return aLength === bLength;
}

const getGridStringOperators$1 = (disableTrim) => getGridStringOperators$2(disableTrim);
const getGridNumericOperators = () => getGridNumericOperators$1();
const getGridBooleanOperators$1 = () => getGridBooleanOperators$2();
const getGridSingleSelectOperators = () => getGridSingleSelectOperators$1();
/*
 * We make sure that the type of the `GRID_CHECKBOX_SELECTION_COL_DEF` respects our `GridColDef` type override
 */
const GRID_CHECKBOX_SELECTION_COL_DEF = GRID_CHECKBOX_SELECTION_COL_DEF$1;

const TextField = forwardRefTyped(function TextField({ size = 'small', readOnly: readOnlyProp, disabled: disabledProp, slotProps, inputProps, ...props }, ref) {
    const providerValue = useKarooFormStateContext();
    const DeprecatedInputProps = props
        .InputProps;
    // As of "@mui/material": "7.0.1", the type of `slotProps.input` is messed up. As such, we cast it to the correct type.
    const slotPropsInput = slotProps?.input;
    const InputSlotProps = { ...DeprecatedInputProps, ...slotPropsInput };
    const disabled = disabledProp ?? providerValue?.disabled;
    // Fallback to inputProps.readOnly here because when using Autocomplete with renderInput, `params` still contains inputProps that may contain readOnly inside, if given to Autocomplete
    const readOnly = readOnlyProp ?? providerValue?.readOnly ?? inputProps?.readOnly;
    return (jsx(TextField$1, { size: size, ref: ref, disabled: disabled, slotProps: {
            ...slotProps,
            input: readOnly !== undefined
                ? { ...InputSlotProps, readOnly }
                : /** IMPORTANT - At runtime, we still allow InputProps to maybe contain `readonly` value.
                    Why? - When using mui components that spread `InputProps` into TextField, it may contain readonly and it may need to contain readonly for certain use cases, e.g:
                    <Autocomplete
                        readOnly
                        renderInput={(params) => (
                          <TextField
                            {...params} // main contain InputProps={{ readonly }} prop according to the types of props of Autocomplete
                            label="example"
                          />
                        )}
                      />
                */
                    InputSlotProps,
        }, inputProps: inputProps, ...props }));
});

/**
 * Wrapped with forwardRef to work with ClickAwayListener if needed
 */
const SearchTextField = forwardRef$1(function SearchTextField(inProps, ref) {
    const { onClearIconClick, value, size = 'small', searchIconProps, ...rest } = useThemeProps({
        name: 'KarooSearchTextField',
        props: inProps,
    });
    return (jsx(TextField, { inputRef: ref, slotProps: {
            input: {
                startAdornment: (jsx(SearchIcon, { fontSize: size, ...searchIconProps, sx: { mr: 1, ...searchIconProps?.sx } })),
                endAdornment: (jsx(IconButton__default, { sx: { mr: -1 }, title: "Clear", "aria-label": "Clear", size: size, style: { visibility: value ? 'visible' : 'hidden' }, onClick: onClearIconClick, children: jsx(ClearIcon, { fontSize: size }) })),
            },
        }, size: size, value: value, ...rest }));
});
function useSearchTextField(initialValue) {
    const [value, setValue] = useState(initialValue);
    const onChange = useCallback(({ target }) => {
        setValue(target.value);
    }, []);
    const onClearIconClick = useCallback((_) => {
        setValue('');
    }, []);
    return {
        value,
        onChange,
        onClearIconClick,
    };
}

/* NOTE: This cast to any is NOT ideal. __However__ material has conflicting types on version 5.4.0 and 5.5.0 that cause a silly typescript error.
Please try to remove the cast when possible */
const styled = 
// eslint-disable-next-line @typescript-eslint/no-explicit-any
styled$1;
const keyframes = keyframes$1;
const mergeSxValues = (array, anotherSx) => {
    const anotherSxDefined = anotherSx ?? [];
    return [
        ...array,
        ...(R.isArray(anotherSxDefined) ? anotherSxDefined : [anotherSxDefined]),
    ];
};

const standardProps = {
    sx: ({ palette }) => ({
        color: palette.secondary.main,
    }),
};
function GridToolbarColumnsButton(props) {
    return jsx(GridToolbarColumnsButton$1, { slotProps: { button: standardProps, ...props } });
}
function GridToolbarFilterButton(props) {
    const buttonSx = props.slotProps?.button?.sx ?? [];
    return (jsx(GridToolbarFilterButton$1, { slotProps: {
            button: {
                ...standardProps,
                ...props.slotProps?.button,
                sx: [standardProps.sx, ...(R.isArray(buttonSx) ? buttonSx : [buttonSx])],
            },
            ...props,
        } }));
}
function GridToolbarDensitySelector(props) {
    return (jsx(GridToolbarDensitySelector$1, { slotProps: { button: standardProps, ...props } }));
}
function GridToolbarExport({ slotProps, ...props }) {
    return (jsx(GridToolbarExport$1, { ...props, 'data-testid': 'GridToolbarExport', slotProps: {
            ...slotProps,
            button: {
                ...standardProps,
                variant: 'outlined',
                color: 'secondary',
                ...slotProps?.button,
            },
        } }));
}
function GridToolbarSearchButton(inButtonProps) {
    const themeProps = useThemeProps({
        name: 'KarooGridToolbarSearchButton',
        props: inButtonProps,
    });
    return (jsx(MuiButton__default, { startIcon: jsx(SearchRoundedIcon, {}), size: "small", variant: "text", ...standardProps, ...themeProps }));
}
function GridToolbarSearchButtonTextField({ GridToolbarQuickFilterProps, }) {
    const [showSearchTextField, setShowSearchTextField] = useState(false);
    const apiRef = useGridApiContext$1();
    const quickFilterValue = gridQuickFilterValuesSelector$1(apiRef);
    if (showSearchTextField) {
        return (jsx(ClickAwayListener__default, { onClickAway: () => {
                // Hide the search text field on click away if value is empty
                if (quickFilterValue?.length === 0) {
                    setShowSearchTextField(false);
                }
            }, children: jsx(Box__default, { children: jsx(GridToolbarQuickFilter, { sx: {
                        px: '5px',
                        pb: '0px',
                        '.MuiSvgIcon-root': {
                            height: '18px',
                            width: '18px',
                            ml: '-2px',
                        },
                    }, variant: "standard", autoFocus: true, placeholder: "", debounceMs: 100, ...GridToolbarQuickFilterProps }) }) }));
    }
    return (jsx(GridToolbarSearchButton, { onClick: () => {
            setShowSearchTextField((prev) => !prev);
        }, ...standardProps }));
}
function GridToolbarSearchOld({ SearchTextFieldProps: { value, onClearIconClick, ...restSearchTextFieldProps }, SearchButtonProps: { onClick, ...restSearchButtonProps } = {}, }) {
    const [showSearchTextField, setShowSearchTextField] = useState(value.length > 0);
    if (showSearchTextField) {
        return (jsx(ClickAwayListener__default, { onClickAway: () => {
                if (value === '') {
                    setShowSearchTextField(false);
                }
            }, children: jsx(SearchTextField, { value: value, variant: "standard", autoFocus: true, placeholder: "", onClearIconClick: (...args) => {
                    onClearIconClick(...args);
                    setShowSearchTextField(false);
                }, ...restSearchTextFieldProps, sx: {
                    px: '5px',
                    '.MuiInput-input': {
                        pb: 0.5,
                    },
                    ...restSearchTextFieldProps.sx,
                }, searchIconProps: {
                    // hardcoded to avoid layout shifting
                    sx: {
                        height: '18px',
                        width: '18px',
                        ml: '-2px',
                    },
                    ...restSearchTextFieldProps.searchIconProps,
                } }) }));
    }
    return (jsx(GridToolbarSearchButton, { onClick: (e) => {
            onClick?.(e);
            setShowSearchTextField((prev) => !prev);
        }, ...standardProps, ...restSearchButtonProps }));
}
function ToolbarStandardContent() {
    return (jsxs(Fragment, { children: [jsx(GridToolbarColumnsButton, { "data-testid": "GridToolbarColumnsButton" }), jsx(GridToolbarFilterButton, { "data-testid": "GridToolbarFilterButton" }), jsx(GridToolbarDensitySelector, { "data-testid": "GridToolbarDensitySelector" })] }));
}
const BaseGridToolbarContainer = styled(GridToolbarContainer)(({ theme }) => theme.unstable_sx({ p: 1 }));
const GridToolbarContainerWithItemsOnBothSides = styled(BaseGridToolbarContainer) `
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
const BaseGridToolbarContainerWithItems = styled('div')(({ theme }) => theme.unstable_sx({
    display: 'flex',
    alignItems: 'center',
    gap: 0.5,
}));
const GridToolbarLeftContainer = styled(BaseGridToolbarContainerWithItems) ``;
const GridToolbarRightContainer = styled(BaseGridToolbarContainerWithItems) ``;
/**GridToolbar that uses the __QuickFilter__ (https://next.mui.com/x/react-data-grid/filtering/#quick-filter) which works better for data grids with selection and has better behavior with search, sort and filtering. */
function _GridToolbarWithQuickFilter({ gridToolbarLeftContent, gridToolbarRightContent, withExport, exportProps, }) {
    return (jsxs(GridToolbarContainerWithItemsOnBothSides, { "data-testid": "GridToolbarQF", children: [jsxs(GridToolbarLeftContainer, { children: [jsx(ToolbarStandardContent, {}), withExport && (jsxs(GridToolbarExportContainer, { slotProps: {
                            button: {
                                color: 'secondary',
                            },
                        }, children: [!exportProps?.csvOptions?.disableToolbarButton && (jsx(GridCsvExportMenuItem, { options: exportProps?.csvOptions, "data-testid": "GridCsvExportMenuItem" })), !exportProps?.printOptions?.disableToolbarButton && (jsx(GridPrintExportMenuItem, { options: exportProps?.printOptions, "data-testid": "GridPrintExportMenuItem" })), !exportProps?.excelOptions?.disableToolbarButton && (jsx(GridExcelExportMenuItem, { options: exportProps?.excelOptions, "data-testid": "GridExcelExportMenuItem" }))] })), jsx(GridToolbarSearchButtonTextField, {}), gridToolbarLeftContent] }), jsx(GridToolbarRightContainer, { "data-testid": "GridToolbarQFRight", children: gridToolbarRightContent })] }));
}
const GridToolbarWithQuickFilter = Object.assign(_GridToolbarWithQuickFilter, {
    createProps: (props) => props ?? {},
});
/**This toolbar is using the old way of filtering with a search text field. Please use the __GridToolbarWithQuickFilter__ instead as it was better behavior with search, sort and filtering.*/
function _GridToolbarStandardOld({ SearchTextFieldProps, SearchButtonProps, gridToolbarLeftContent, gridToolbarRightContent, }) {
    return (jsxs(GridToolbarContainerWithItemsOnBothSides, { children: [jsxs(GridToolbarLeftContainer, { children: [jsx(ToolbarStandardContent, {}), jsx(GridToolbarSearchOld, { SearchTextFieldProps: SearchTextFieldProps, SearchButtonProps: SearchButtonProps }), gridToolbarLeftContent] }), jsx(GridToolbarRightContainer, { children: gridToolbarRightContent })] }));
}
const GridToolbarStandardOld = Object.assign(_GridToolbarStandardOld, {
    createProps: (props) => props,
});
function _GridToolbarStandard({ gridToolbarLeftContent, gridToolbarRightContent, }) {
    return (jsxs(GridToolbarContainerWithItemsOnBothSides, { children: [jsxs(GridToolbarLeftContainer, { children: [jsx(ToolbarStandardContent, {}), gridToolbarLeftContent] }), jsx(GridToolbarRightContainer, { children: gridToolbarRightContent })] }));
}
const GridToolbarStandard = Object.assign(_GridToolbarStandard, {
    createProps: (props) => props,
});

function DataGridBase({ slots, slotProps, columns: columnsProp_, sx: sxProp, apiRef: apiRefProp, onFilterModelChange, getRowHeight, groupingColDef, onRowClick, ...restProps }) {
    const columnsProp = useMemo(() => columnsProp_.map((column) => ({
        ...column,
        /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
        // Set default __groupable__ and __aggregable__ to false. These features are not yet stable to be used by default for __every__ column. It specifically causes issues with the `singleSelect` columns.
        // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
        groupable: column.groupable ?? false,
        aggregable: column.aggregable ?? false,
        display: column.display ?? 'flex',
    })), [columnsProp_]);
    const [stableColumns, setStableColumns] = useState(columnsProp);
    const _gridApiRef = useGridApiRef();
    const apiRef = apiRefProp ?? _gridApiRef;
    const prevColumnsProp = usePrevious(columnsProp);
    const columnHasBeenRemoved = useMemo(() => {
        if (prevColumnsProp === undefined) {
            return false;
        }
        return prevColumnsProp.some((stableColumn) => {
            const propColumnExistsInProp = columnsProp.some((column) => stableColumn.field === column.field);
            return !propColumnExistsInProp;
        });
    }, [columnsProp, prevColumnsProp]);
    useEffect(() => {
        if (columnHasBeenRemoved) {
            // If a column has been removed, we reset the stable columns to the prop columns.
            // apiRef.current.updateColumns is not enough because it doesn't remove columns from the grid, only upserts them.
            setStableColumns(columnsProp);
            return;
        }
        // We check for the existence of the `updateColumns` method to avoid a crash. DataGrid lazy loads some methods like `updateColumns` which are not available immediately.
        if (apiRef.current && apiRef.current.updateColumns) {
            // Fixes https://gitlab.cartrack.com/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/679
            apiRef.current.updateColumns(columnsProp);
        }
    }, [columnsProp, apiRef, columnHasBeenRemoved]);
    const filterPanelSlotPropsSx = slotProps?.filterPanel?.sx ?? [];
    const sx = sxProp ?? [];
    return (jsx(StyledDataGridMui, { ...restProps, onRowClick: onRowClick, getRowHeight: getRowHeight, groupingColDef: groupingColDef, apiRef: apiRef, 
        /* From mui docs https://mui.com/x/react-data-grid/column-definition/:
             The columns prop should keep the same reference between two renders. The columns are designed to be definitions, to never change once the component is mounted.
             Otherwise, you take the risk of losing elements like column width or order. You can create the array outside the render function or memoize it.
        */
        columns: stableColumns, onFilterModelChange: onFilterModelChange, sx: [
            {
                [`& .${gridClasses.cell}:focus, & .${gridClasses.cell}:focus-within`]: {
                    outline: 'none',
                },
                [`& .${gridClasses.columnHeader}:focus, & .${gridClasses.columnHeader}:focus-within`]: {
                    outline: 'none',
                },
                ...(restProps.disableRowSelectionOnClick && !onRowClick
                    ? {
                        '& .MuiDataGrid-row:hover': {
                            backgroundColor: 'inherit',
                        },
                    }
                    : {}),
            },
            ...(R.isArray(sx) ? sx : [sx]),
        ], slots: {
            detailPanelExpandIcon: KeyboardArrowRightRoundedIcon,
            detailPanelCollapseIcon: KeyboardArrowDownRoundedIcon,
            ...slots,
        }, slotProps: {
            ...slotProps,
            filterPanel: {
                ...slotProps?.filterPanel,
                sx: [
                    {
                        /* Increase the width of the filter panel to accommodate the new datetime picker input (when using am/pm)
                           This makes sure that there is enough space to show the full date and time.
                           The reason we do this to ALL filter panel input values is to maintain consistency between all input types.
                        */
                        width: '580px', // (default=510px) + 70px
                        minWidth: '320px', // Allow shrinking if window is resized horizontally
                        '& .MuiDataGrid-filterFormValueInput': {
                            width: '225px', // (default=190px) + 35px
                        },
                    },
                    ...(R.isArray(filterPanelSlotPropsSx)
                        ? filterPanelSlotPropsSx
                        : [filterPanelSlotPropsSx]),
                ],
            },
        } }));
}
const StyledDataGridMui = styled(DataGridPremium)({
    border: 'none',
    '& .MuiDataGrid-row.Mui-selected': {
        backgroundColor: 'rgb(91 91 91 / 13.5%)',
    },
    '& .MuiDataGrid-row.Mui-selected:hover': {
        backgroundColor: 'rgb(91 91 91 / 20.5%)',
    },
    '& .actionHeader:last-child .MuiDataGrid-iconSeparator': {
        display: 'none',
    },
    '& .MuiDataGrid-main': {
        // Solves https://cartrack.atlassian.net/browse/FTW-8383
        // Without it, if the grid at some point was resized vertically in way where it's height is near zero, mui would crash.
        minHeight: '130px',
    },
});
/**
 * Custom hook to get previous value
 *
 * @returns Returns previous value (from last render)
 * @example
 * function App() {
 *  // State value and setter for our example
 *  const [count, setCount] = useState(0);
 *
 *  // Get the previous value (was passed into hook on last render)
 *  const prevCount = usePrevious(count);
 *
 *  // Display both current and previous count value
 *  return (
 *    <>
 *      <h1>Now: {count}, before: {prevCount}</h1>
 *    </>
 *   );
 * }
 *
 */
function usePrevious(value) {
    const ref = useRef(undefined);
    // Store current value in ref
    useEffect(() => {
        ref.current = value;
    }, [value]);
    // Return previous value (happens before update in useEffect above)
    return ref.current;
}

/**
 * DataGridBase with Paper as container
 */
function DataGrid({ RootPaperProps, ...restProps }) {
    const rootPaperSx = RootPaperProps?.sx ?? [];
    return (jsx(Paper__default, { ...RootPaperProps, sx: [
            {
                // Needed to maintain responsiveness https://github.com/mui/mui-x/issues/8758
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                position: 'relative',
                height: '100%',
                overflow: 'hidden',
                // ------------------------------------------------------------
            },
            ...(R.isArray(rootPaperSx) ? rootPaperSx : [rootPaperSx]),
        ], children: jsx(DataGridBase, { ...restProps }) }));
}

function DataGridAsTabItem({ sx = [], ...props }) {
    return (jsx(DataGridBase, { sx: [{ overflow: 'hidden' }, ...(R.isArray(sx) ? sx : [sx])], ...props }));
}

// Allows us to overwrite the default styles of the tab/tabs component without having to change the code everywhere these components are used
const ContainerTab = Tab__default;
const ContainerTabs = Tabs__default;
function ContainerWithTabsForDataGridPrivate({ renderTabs, children, ...rest }) {
    return (jsxs(Paper__default, { ...rest, sx: {
            // Needed to maintain responsiveness https://github.com/mui/mui-x/issues/8758
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
            position: 'relative',
            height: '100%',
            overflow: 'hidden',
            ...rest?.sx,
            // ------------------------------------------------------------
        }, children: [jsx(Box__default, { sx: { borderBottom: 1, borderColor: 'divider', mb: 1, mx: 1.5 }, children: renderTabs() }), children] }));
}
const ContainerWithTabsForDataGrid = Object.assign(ContainerWithTabsForDataGridPrivate, { Tab: ContainerTab, Tabs: ContainerTabs });

function usePickersAdapterContextUtils() {
    const context = useContext(MuiPickersAdapterContext);
    if (context === null || context.utils === null) {
        throw new Error('usePickersAdapterContextUtils must be used within a MuiPickersAdapterContext');
    }
    return context.utils;
}

const Tooltip = forwardRefTyped(function Tooltip(props, ref) {
    return (jsx(MUITooltip, { ref: ref, 
        // According to our design guidelines, tooltips should have an arrow by default.
        // Can still be overwritten on specific situations if needed.
        arrow: true, ...props }));
});

function useOverflowableElement({ initialValue = false, orientation, }, deps = []) {
    const [isOverflowed, setIsOverflowed] = useState(initialValue);
    const elementRef = useRef(null);
    const compareSize = useMemo(() => debounce(() => {
        if (elementRef.current === null) {
            return;
        }
        const isHorizontalOverflowed = elementRef.current.scrollWidth > elementRef.current.clientWidth;
        const isVerticalOverflowed = elementRef.current.scrollHeight > elementRef.current.clientHeight;
        setIsOverflowed(() => {
            switch (orientation) {
                case 'horizontal':
                    return isHorizontalOverflowed;
                case 'vertical':
                    return isVerticalOverflowed;
                case 'all':
                    return isHorizontalOverflowed || isVerticalOverflowed;
            }
        });
    }, 65), [orientation]);
    useEffect(() => {
        // Run once after mount OR, if "deps" are provided and it's elements change
        compareSize();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [compareSize, ...deps]);
    useResizeObserver(elementRef.current, () => compareSize());
    useEffect(() => {
        window.addEventListener('resize', compareSize);
        return () => window.removeEventListener('resize', compareSize);
    }, [compareSize]);
    return [isOverflowed, elementRef];
}
function OverflowableElementHeadless({ children, orientation, deps, initialValue, }) {
    const [isOverflowed, elementRef] = useOverflowableElement({
        initialValue,
        orientation,
    }, deps);
    return children({ isOverflowed, elementRef });
}

const OverflowTypography = ({ typographyProps, tooltipProps, children: childrenProp, }) => {
    const childrenInBody = typeof childrenProp === 'function'
        ? childrenProp({ renderedIn: 'body' })
        : childrenProp;
    const [isOverflown, wrapperRef] = useOverflowableElement({ initialValue: false, orientation: 'all' }, [childrenInBody]);
    return (jsx(Tooltip, { title: typeof childrenProp === 'function'
            ? childrenProp({ renderedIn: 'tooltip' })
            : childrenProp, disableHoverListener: !isOverflown, ...tooltipProps, children: jsx(Typography__default, { ref: wrapperRef, noWrap: true, overflow: "hidden", textOverflow: "ellipsis", ...typographyProps, children: childrenInBody }) }));
};

function CellTextOverflowTypography({ typographyProps, ...props }) {
    return (jsx(OverflowTypography
    // inherit is important here to allow the cell to inherit the correct font size from the mui table specified font sizes
    , { 
        // inherit is important here to allow the cell to inherit the correct font size from the mui table specified font sizes
        typographyProps: { variant: 'inherit', ...typographyProps }, ...props }));
}

// The mui team already plans to remove support for "string" values in the future so we should remove this option right now to avoid breaking changes in the future.
// This way, only "Date" values are supported.
const GRID_DATETIME_COL_DEF = GRID_DATETIME_COL_DEF$1;
const GRID_DATE_COL_DEF = GRID_DATE_COL_DEF$1;
/** We created these types to deviate from standard a few behaviors of mui date and dateTime columns:
 *  - Export in excel - mui exports out of the box "date" and "dateTime" type columns in a way that messes up the timezone
 */
const KAROO_DATETIME_COLUMN_TYPE = 'karoo-datetime';
const KAROO_DATE_COLUMN_TYPE = 'karoo-date';
function buildApplyDateFilterFn({ compareFn, filterItem: { value: filterItemValue_ }, showTime = false, pickersAdapterUtils, }) {
    const filterItemValue = filterItemValue_;
    if (!filterItemValue) {
        return null;
    }
    const createDateForComparison = (adapterDate) => {
        if (showTime) {
            const adapterDateWithZeroSeconds = pickersAdapterUtils.setSeconds(adapterDate, 0);
            const date = pickersAdapterUtils.toJsDate(adapterDateWithZeroSeconds);
            date.setMilliseconds(0); // pickersAdapterUtils does not support setMilliseconds so we need to use the native Date object
            return date;
        }
        return pickersAdapterUtils.toJsDate(pickersAdapterUtils.startOfDay(adapterDate));
    };
    const filterValueMs = createDateForComparison(filterItemValue).getTime();
    return (value) => {
        if (!value) {
            return false;
        }
        const cellValueAsDate = createDateForComparison(pickersAdapterUtils.date(value.toISOString()));
        const cellValueMs = cellValueAsDate.getTime();
        return compareFn(cellValueMs, filterValueMs);
    };
}
function createGridDateColumnFilterOperatorGetters({ pickersAdapterUtils, filterMode, extendedLocales, }) {
    const getGridDateColumnRangeOperator = ({ InputComponentProps, }) => {
        return {
            value: 'range',
            label: extendedLocales.filterOperators.date_range,
            headerLabel: extendedLocales.filterOperators.date_range,
            getApplyFilterFn: (filterItem_) => {
                const filterItemValue = filterItem_.value;
                if (!filterItemValue) {
                    return null;
                }
                if (!R.isArray(filterItemValue)) {
                    return null;
                }
                if (filterItemValue.length !== 2) {
                    return null;
                }
                const [startFilterValue, endFilterValue] = filterItemValue;
                const filterRangeInMs = {
                    start: R.isNullish(startFilterValue)
                        ? null
                        : pickersAdapterUtils
                            .toJsDate(pickersAdapterUtils.startOfDay(startFilterValue))
                            .getTime(),
                    end: R.isNullish(endFilterValue)
                        ? null
                        : pickersAdapterUtils
                            .toJsDate(pickersAdapterUtils.endOfDay(endFilterValue))
                            .getTime(),
                };
                return (value) => {
                    if (!value) {
                        return false;
                    }
                    const cellValueMs = value.getTime();
                    return ((filterRangeInMs.start === null
                        ? true
                        : cellValueMs >= filterRangeInMs.start) &&
                        (filterRangeInMs.end === null ? true : cellValueMs <= filterRangeInMs.end));
                };
            },
            InputComponent: GridFilterDateRangeInput,
            InputComponentProps,
        };
    };
    return {
        getGridDateColumnFilterOperators({ showTime }) {
            const inputComponent = GridFilterDateInput;
            const inputComponentProps = {
                showTime,
                filterMode,
            };
            return [
                getGridDateColumnRangeOperator({
                    InputComponentProps: {},
                }),
                {
                    value: 'is',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 === value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'not',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 !== value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'after',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 > value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'onOrAfter',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 >= value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'before',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 < value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'onOrBefore',
                    getApplyFilterFn: (filterItem) => {
                        return buildApplyDateFilterFn({
                            filterItem,
                            compareFn: (value1, value2) => value1 <= value2,
                            showTime,
                            pickersAdapterUtils,
                        });
                    },
                    InputComponent: inputComponent,
                    InputComponentProps: inputComponentProps,
                },
                {
                    value: 'isEmpty',
                    getApplyFilterFn: () => {
                        return (value) => {
                            return value == null;
                        };
                    },
                    requiresFilterValue: false,
                },
                {
                    value: 'isNotEmpty',
                    getApplyFilterFn: () => {
                        return (value) => {
                            return value != null;
                        };
                    },
                    requiresFilterValue: false,
                },
            ];
        },
        getGridDateColumnRangeOperator,
    };
}
const GridEditDateInput = styled(InputBase__default)({
    fontSize: 'inherit',
    padding: '0 9px',
});
function WrappedGridEditDateInput(props) {
    const { InputProps, ...other } = props;
    return (jsx(GridEditDateInput, { fullWidth: true, ...InputProps, ...other }));
}
function GridEditDateCell({ id, field, value: valueProp, colDef, }) {
    const apiRef = useGridApiContext$1();
    const utils = usePickersAdapterContextUtils();
    const handleChange = (newValue) => {
        apiRef.current.setEditCellValue({
            id,
            field,
            value: R.isNullish(newValue) ? newValue : utils.toJsDate(newValue),
        });
    };
    const Component = colDef.type === KAROO_DATETIME_COLUMN_TYPE ? DateTimePicker$1 : DatePicker$1;
    const parsedValue = useMemo(() => {
        if (R.isNullish(valueProp)) {
            return valueProp;
        }
        return utils.date(valueProp.toISOString());
    }, [utils, valueProp]);
    return (jsx(Component, { value: parsedValue, autoFocus: true, onChange: handleChange, slots: { textField: WrappedGridEditDateInput } }));
}
function GridFilterDateInput({ item, showTime, applyValue, apiRef, filterMode, }) {
    const [isPickerOpen, setIsPickerOpen] = useState(false);
    const onChangeHandler = useMemo(() => {
        const handleFilterChange_ = (newValue, context) => {
            if (context.validationError === null) {
                applyValue({ ...item, value: newValue });
            }
        };
        if (isPickerOpen) {
            // Do nothing when the picker is open. onAccept will take care of it
            return undefined;
        }
        switch (filterMode) {
            case 'client': {
                return handleFilterChange_;
            }
            case 'server': {
                // When the date is changed through the input, we want to debounce the change (prevent multiple requests)
                return debounce$1(handleFilterChange_, 600);
            }
        }
    }, [applyValue, filterMode, isPickerOpen, item]);
    const onAcceptHandler = useCallback((newValue) => {
        applyValue({ ...item, value: newValue });
    }, [applyValue, item]);
    const baseProps = {
        open: isPickerOpen,
        onOpen: () => setIsPickerOpen(true),
        onClose: () => setIsPickerOpen(false),
        onAccept: onAcceptHandler,
        value: item.value || null,
        onChange: onChangeHandler,
        autoFocus: true,
        label: apiRef.current.getLocaleText('filterPanelInputLabel'),
        slotProps: {
            textField: {
                variant: 'standard',
            },
            inputAdornment: {
                sx: {
                    '& .MuiButtonBase-root': {
                        marginRight: -1,
                    },
                },
            },
        },
    };
    return showTime ? (jsx(DateTimePicker$1, { ...baseProps, closeOnSelect: false, timeSteps: {
            hours: 1,
            minutes: 1,
        } })) : (jsx(DatePicker$1, { ...baseProps }));
}
function GridFilterDateRangeInput({ item, applyValue, apiRef, variant, size, dateRangePickerProps, }) {
    const onChangeHandler = useMemo(() => {
        const handleFilterChange_ = (newValue, context) => {
            if (context.validationError === null &&
                context.validationError[0] === null &&
                context.validationError[1] === null) {
                applyValue({ ...item, value: newValue });
            }
        };
        return handleFilterChange_;
    }, [applyValue, item]);
    const onAcceptHandler = useCallback((newValue) => {
        applyValue({ ...item, value: newValue });
    }, [applyValue, item]);
    const baseProps = {
        onAccept: onAcceptHandler,
        /** Our users prefer this behaviour and is less confusing */
        disableAutoMonthSwitching: true,
        value: item.value || [null, null],
        onChange: onChangeHandler,
        autoFocus: true,
        label: apiRef.current.getLocaleText('filterPanelInputLabel'),
        slotProps: {
            textField: {
                variant: variant ?? 'standard',
                size: size,
            },
            field: {
                slotProps: {
                    textField: {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        size: size,
                    },
                },
            },
            actionBar: {
                actions: ['clear'],
            },
        },
        sx: {
            // In order to see the full date
            minWidth: 208,
        },
        slots: {
            field: SingleInputDateRangeField,
        },
        ...dateRangePickerProps,
    };
    return jsx(DateRangePicker$1, { ...baseProps });
}
/**
 * @deprecated Use `columnHelper.date()` or `columnHelper.dateTime()` from `useDataGridColumnHelper` hook, instead.
 */
function useDataGridDateColumns({ filterMode }) {
    const utils = usePickersAdapterContextUtils();
    const extendedLocales = useKarooExtendedLocales();
    return useMemo(() => {
        const dateTimeColDefaultFormatter = (dateValue) => utils.format(utils.date(dateValue.toISOString()), 'keyboardDateTime');
        const { getGridDateColumnFilterOperators, getGridDateColumnRangeOperator } = createGridDateColumnFilterOperatorGetters({
            extendedLocales,
            filterMode,
            pickersAdapterUtils: utils,
        });
        const getGridDateColumnOperators = ({ showTime }) => {
            return getGridDateColumnFilterOperators({
                showTime,
            });
        };
        function createDateTimeColumn({ valueFormatter, renderCell, ...columnDefinition }) {
            const defaultFormatter = dateTimeColDefaultFormatter;
            return {
                ...GRID_DATETIME_COL_DEF,
                type: KAROO_DATETIME_COLUMN_TYPE,
                // Can still be overwritten if needed
                width: DATAGRID_DATETIME_COLUMN_WIDTH,
                resizable: false,
                renderEditCell: (params) => {
                    return jsx(GridEditDateCell, { ...params });
                },
                filterOperators: getGridDateColumnOperators({
                    showTime: true,
                }),
                valueFormatter: (value, row, _column, apiRef) => {
                    if (valueFormatter) {
                        return valueFormatter(value, row, apiRef, { defaultFormatter });
                    }
                    if (value) {
                        return defaultFormatter(value);
                    }
                    return '';
                },
                renderCell: renderCell ??
                    (({ formattedValue }) => (jsx(CellTextOverflowTypography, { children: formattedValue }))),
                ...columnDefinition,
                /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
                // Set default __groupable__ and __aggregable__ to false by default. These features are not yet stable to be used by default for __every__ column.
                // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
                groupable: columnDefinition.groupable ?? false,
                aggregable: columnDefinition.aggregable ?? false,
            };
        }
        const dateColDefaultFormatter = (dateValue) => utils.format(utils.date(dateValue.toISOString()), 'keyboardDate');
        function createDateColumn({ valueFormatter, renderCell, ...columnDefinition }) {
            const defaultFormatter = dateColDefaultFormatter;
            return {
                ...GRID_DATE_COL_DEF,
                type: KAROO_DATE_COLUMN_TYPE,
                resizable: false,
                renderEditCell: (params) => {
                    return jsx(GridEditDateCell, { ...params });
                },
                filterOperators: getGridDateColumnOperators({
                    showTime: false,
                }),
                valueFormatter: (value, row, _column, apiRef) => {
                    if (valueFormatter) {
                        return valueFormatter(value, row, apiRef, { defaultFormatter });
                    }
                    if (value) {
                        return defaultFormatter(value);
                    }
                    return '';
                },
                renderCell: renderCell ??
                    (({ formattedValue }) => (jsx(CellTextOverflowTypography, { children: formattedValue }))),
                ...columnDefinition,
                /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
                // Set default __groupable__ and __aggregable__ to false by default. These features are not yet stable to be used by default for __every__ column.
                // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
                groupable: columnDefinition.groupable ?? false,
                aggregable: columnDefinition.aggregable ?? false,
            };
        }
        return {
            createDateTimeColumn,
            createDateColumn,
            dateTimeColDefaultFormatter,
            dateColDefaultFormatter,
            getGridDateColumnOperators,
            getGridDateColumnRangeOperator,
        };
    }, [extendedLocales, filterMode, utils]);
}
/**
 * Minimum width to make sure that __all__ default formats for different locales are not overflown by default
 */
const DATAGRID_DATETIME_COLUMN_WIDTH = 175;

/**
 * Super type-safe util to create base columns.
 * Prevents leaking dangerous __any__ types.
 */
const createDataGridBaseColumn = (column) => column;
/**
 * Inspired by Tanstack Table createColumnHelper util.
 */
function createDataGridColumnHelper() {
    return {
        valueGetter: (valueGetter, column) => {
            const def = { valueGetter, ...column };
            return def;
        },
        singleSelect: (valueGetter, column) => {
            const def = { valueGetter, type: 'singleSelect', ...column };
            return def;
        },
        string: (valueGetter, { renderCell, ...column }) => {
            const baseDef = {
                type: 'string',
                renderCell: (...args) => {
                    if (renderCell) {
                        const cell = renderCell(...args);
                        return typeof cell === 'string' || typeof cell === 'number' ? (jsx(CellTextOverflowTypography, { children: cell })) : (cell);
                    }
                    const { formattedValue } = args[0];
                    return (jsx(CellTextOverflowTypography, { children: formattedValue }));
                },
                ...column,
            };
            const def = { valueGetter, ...baseDef };
            return def;
        },
        number: (valueGetter, { renderCell, ...column }) => {
            const baseDef = {
                type: 'number',
                renderCell: (...args) => {
                    if (renderCell) {
                        const cell = renderCell(...args);
                        return typeof cell === 'string' || typeof cell === 'number' ? (jsx(CellTextOverflowTypography, { children: cell })) : (cell);
                    }
                    const { formattedValue } = args[0];
                    return (jsx(CellTextOverflowTypography, { children: formattedValue }));
                },
                ...column,
            };
            const def = { valueGetter, ...baseDef };
            return def;
        },
        boolean: (valueGetter, { renderCell, ...column }) => {
            const baseDef = {
                type: 'boolean',
                renderCell: (...args) => {
                    if (renderCell) {
                        const cell = renderCell(...args);
                        return typeof cell === 'string' || typeof cell === 'number' ? (jsx(CellTextOverflowTypography, { children: cell })) : (cell);
                    }
                    const { formattedValue } = args[0];
                    return (jsx(CellTextOverflowTypography, { children: formattedValue }));
                },
                ...column,
            };
            const def = { valueGetter, ...baseDef };
            return def;
        },
    };
}
/**
 *  * Inspired by Tanstack Table createColumnHelper util.
 */
function useDataGridColumnHelper({ filterMode, }) {
    const { createDateColumn, createDateTimeColumn, dateColDefaultFormatter, dateTimeColDefaultFormatter, getGridDateColumnOperators, getGridDateColumnRangeOperator, } = useDataGridDateColumns({ filterMode });
    return useMemo(() => {
        const baseHelpers = createDataGridColumnHelper();
        return {
            ...baseHelpers,
            date: (colDef) => createDateColumn(colDef),
            dateTime: (colDef) => createDateTimeColumn(colDef),
            utils: {
                dateColDefaultFormatter,
                dateTimeColDefaultFormatter,
                getGridDateColumnOperators,
                getGridDateColumnRangeOperator,
            },
        };
    }, [
        createDateColumn,
        createDateTimeColumn,
        dateColDefaultFormatter,
        dateTimeColDefaultFormatter,
        getGridDateColumnOperators,
        getGridDateColumnRangeOperator,
    ]);
}

LicenseInfo.setLicenseKey('955bf8527a233fe800832b3daf2b9c9dTz0xMDExNjksRT0xNzY0MzI0ODI0MDAwLFM9cHJlbWl1bSxMTT1zdWJzY3JpcHRpb24sUFY9aW5pdGlhbCxLVj0y');

const GridApiContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== 'production') {
  GridApiContext.displayName = 'GridApiContext';
}

function useGridApiContext() {
  const apiRef = React.useContext(GridApiContext);
  if (apiRef === undefined) {
    throw new Error(['MUI X: Could not find the Data Grid context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\n'));
  }
  return apiRef;
}

const GridRootPropsContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== 'production') {
  GridRootPropsContext.displayName = 'GridRootPropsContext';
}

const useGridRootProps = () => {
  const contextValue = React.useContext(GridRootPropsContext);
  if (!contextValue) {
    throw new Error('MUI X: useGridRootProps should only be used inside the DataGrid, DataGridPro or DataGridPremium component.');
  }
  return contextValue;
};

var reactMajor = parseInt(React.version, 10);

// Compatibility shim that ensures stable props object for forwardRef components
// Fixes https://github.com/facebook/react/issues/31613
// We ensure that the ref is always present in the props object (even if that's not the case for older versions of React) to avoid the footgun of spreading props over the ref in the newer versions of React.
// Footgun: <Component ref={ref} {...props} /> will break past React 19, but the types will now warn us that we should use <Component {...props} ref={ref} /> instead.
const forwardRef = render => {
  if (reactMajor >= 19) {
    const Component = props => render(props, props.ref ?? null);
    Component.displayName = render.displayName ?? render.name;
    return Component;
  }
  return /*#__PURE__*/React.forwardRef(render);
};

// src/devModeChecks/identityFunctionCheck.ts
var runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {
  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {
    let isInputSameAsOutput = false;
    try {
      const emptyObject = {};
      if (resultFunc(emptyObject) === emptyObject)
        isInputSameAsOutput = true;
    } catch {
    }
    if (isInputSameAsOutput) {
      let stack = undefined;
      try {
        throw new Error();
      } catch (e) {
        ({ stack } = e);
      }
      console.warn(
        "The result function returned its own inputs without modification. e.g\n`createSelector([state => state.todos], todos => todos)`\nThis could lead to inefficient memoization and unnecessary re-renders.\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.",
        { stack }
      );
    }
  }
};

// src/devModeChecks/inputStabilityCheck.ts
var runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {
  const { memoize, memoizeOptions } = options;
  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;
  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);
  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);
  if (!areInputSelectorResultsEqual) {
    let stack = undefined;
    try {
      throw new Error();
    } catch (e) {
      ({ stack } = e);
    }
    console.warn(
      "An input selector returned a different result when passed same arguments.\nThis means your output selector will likely run more frequently than intended.\nAvoid returning a new reference inside your input selector, e.g.\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`",
      {
        arguments: inputSelectorArgs,
        firstInputs: inputSelectorResults,
        secondInputs: inputSelectorResultsCopy,
        stack
      }
    );
  }
};

// src/devModeChecks/setGlobalDevModeChecks.ts
var globalDevModeChecks = {
  inputStabilityCheck: "once",
  identityFunctionCheck: "once"
};

// src/utils.ts
var NOT_FOUND = /* @__PURE__ */ Symbol("NOT_FOUND");
function assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {
  if (typeof func !== "function") {
    throw new TypeError(errorMessage);
  }
}
function assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {
  if (typeof object !== "object") {
    throw new TypeError(errorMessage);
  }
}
function assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {
  if (!array.every((item) => typeof item === "function")) {
    const itemTypes = array.map(
      (item) => typeof item === "function" ? `function ${item.name || "unnamed"}()` : typeof item
    ).join(", ");
    throw new TypeError(`${errorMessage}[${itemTypes}]`);
  }
}
var ensureIsArray = (item) => {
  return Array.isArray(item) ? item : [item];
};
function getDependencies(createSelectorArgs) {
  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;
  assertIsArrayOfFunctions(
    dependencies,
    `createSelector expects all input-selectors to be functions, but received the following types: `
  );
  return dependencies;
}
function collectInputSelectorResults(dependencies, inputSelectorArgs) {
  const inputSelectorResults = [];
  const { length } = dependencies;
  for (let i = 0; i < length; i++) {
    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));
  }
  return inputSelectorResults;
}
var getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {
  const { identityFunctionCheck, inputStabilityCheck } = {
    ...globalDevModeChecks,
    ...devModeChecks
  };
  return {
    identityFunctionCheck: {
      shouldRun: identityFunctionCheck === "always" || identityFunctionCheck === "once" && firstRun,
      run: runIdentityFunctionCheck
    },
    inputStabilityCheck: {
      shouldRun: inputStabilityCheck === "always" || inputStabilityCheck === "once" && firstRun,
      run: runInputStabilityCheck
    }
  };
};

// src/lruMemoize.ts
function createSingletonCache(equals) {
  let entry;
  return {
    get(key) {
      if (entry && equals(entry.key, key)) {
        return entry.value;
      }
      return NOT_FOUND;
    },
    put(key, value) {
      entry = { key, value };
    },
    getEntries() {
      return entry ? [entry] : [];
    },
    clear() {
      entry = undefined;
    }
  };
}
function createLruCache(maxSize, equals) {
  let entries = [];
  function get(key) {
    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));
    if (cacheIndex > -1) {
      const entry = entries[cacheIndex];
      if (cacheIndex > 0) {
        entries.splice(cacheIndex, 1);
        entries.unshift(entry);
      }
      return entry.value;
    }
    return NOT_FOUND;
  }
  function put(key, value) {
    if (get(key) === NOT_FOUND) {
      entries.unshift({ key, value });
      if (entries.length > maxSize) {
        entries.pop();
      }
    }
  }
  function getEntries() {
    return entries;
  }
  function clear() {
    entries = [];
  }
  return { get, put, getEntries, clear };
}
var referenceEqualityCheck = (a, b) => a === b;
function createCacheKeyComparator(equalityCheck) {
  return function areArgumentsShallowlyEqual(prev, next) {
    if (prev === null || next === null || prev.length !== next.length) {
      return false;
    }
    const { length } = prev;
    for (let i = 0; i < length; i++) {
      if (!equalityCheck(prev[i], next[i])) {
        return false;
      }
    }
    return true;
  };
}
function lruMemoize(func, equalityCheckOrOptions) {
  const providedOptions = typeof equalityCheckOrOptions === "object" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };
  const {
    equalityCheck = referenceEqualityCheck,
    maxSize = 1,
    resultEqualityCheck
  } = providedOptions;
  const comparator = createCacheKeyComparator(equalityCheck);
  let resultsCount = 0;
  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);
  function memoized() {
    let value = cache.get(arguments);
    if (value === NOT_FOUND) {
      value = func.apply(null, arguments);
      resultsCount++;
      if (resultEqualityCheck) {
        const entries = cache.getEntries();
        const matchingEntry = entries.find(
          (entry) => resultEqualityCheck(entry.value, value)
        );
        if (matchingEntry) {
          value = matchingEntry.value;
          resultsCount !== 0 && resultsCount--;
        }
      }
      cache.put(arguments, value);
    }
    return value;
  }
  memoized.clearCache = () => {
    cache.clear();
    memoized.resetResultsCount();
  };
  memoized.resultsCount = () => resultsCount;
  memoized.resetResultsCount = () => {
    resultsCount = 0;
  };
  return memoized;
}

// src/weakMapMemoize.ts
var StrongRef = class {
  constructor(value) {
    this.value = value;
  }
  deref() {
    return this.value;
  }
};
var Ref = typeof WeakRef !== "undefined" ? WeakRef : StrongRef;
var UNTERMINATED = 0;
var TERMINATED = 1;
function createCacheNode() {
  return {
    s: UNTERMINATED,
    v: undefined,
    o: null,
    p: null
  };
}
function weakMapMemoize(func, options = {}) {
  let fnNode = createCacheNode();
  const { resultEqualityCheck } = options;
  let lastResult;
  let resultsCount = 0;
  function memoized() {
    let cacheNode = fnNode;
    const { length } = arguments;
    for (let i = 0, l = length; i < l; i++) {
      const arg = arguments[i];
      if (typeof arg === "function" || typeof arg === "object" && arg !== null) {
        let objectCache = cacheNode.o;
        if (objectCache === null) {
          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();
        }
        const objectNode = objectCache.get(arg);
        if (objectNode === undefined) {
          cacheNode = createCacheNode();
          objectCache.set(arg, cacheNode);
        } else {
          cacheNode = objectNode;
        }
      } else {
        let primitiveCache = cacheNode.p;
        if (primitiveCache === null) {
          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();
        }
        const primitiveNode = primitiveCache.get(arg);
        if (primitiveNode === undefined) {
          cacheNode = createCacheNode();
          primitiveCache.set(arg, cacheNode);
        } else {
          cacheNode = primitiveNode;
        }
      }
    }
    const terminatedNode = cacheNode;
    let result;
    if (cacheNode.s === TERMINATED) {
      result = cacheNode.v;
    } else {
      result = func.apply(null, arguments);
      resultsCount++;
      if (resultEqualityCheck) {
        const lastResultValue = lastResult?.deref?.() ?? lastResult;
        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {
          result = lastResultValue;
          resultsCount !== 0 && resultsCount--;
        }
        const needsWeakRef = typeof result === "object" && result !== null || typeof result === "function";
        lastResult = needsWeakRef ? new Ref(result) : result;
      }
    }
    terminatedNode.s = TERMINATED;
    terminatedNode.v = result;
    return result;
  }
  memoized.clearCache = () => {
    fnNode = createCacheNode();
    memoized.resetResultsCount();
  };
  memoized.resultsCount = () => resultsCount;
  memoized.resetResultsCount = () => {
    resultsCount = 0;
  };
  return memoized;
}

// src/createSelectorCreator.ts
function createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {
  const createSelectorCreatorOptions = typeof memoizeOrOptions === "function" ? {
    memoize: memoizeOrOptions,
    memoizeOptions: memoizeOptionsFromArgs
  } : memoizeOrOptions;
  const createSelector2 = (...createSelectorArgs) => {
    let recomputations = 0;
    let dependencyRecomputations = 0;
    let lastResult;
    let directlyPassedOptions = {};
    let resultFunc = createSelectorArgs.pop();
    if (typeof resultFunc === "object") {
      directlyPassedOptions = resultFunc;
      resultFunc = createSelectorArgs.pop();
    }
    assertIsFunction(
      resultFunc,
      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`
    );
    const combinedOptions = {
      ...createSelectorCreatorOptions,
      ...directlyPassedOptions
    };
    const {
      memoize,
      memoizeOptions = [],
      argsMemoize = weakMapMemoize,
      argsMemoizeOptions = [],
      devModeChecks = {}
    } = combinedOptions;
    const finalMemoizeOptions = ensureIsArray(memoizeOptions);
    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);
    const dependencies = getDependencies(createSelectorArgs);
    const memoizedResultFunc = memoize(function recomputationWrapper() {
      recomputations++;
      return resultFunc.apply(
        null,
        arguments
      );
    }, ...finalMemoizeOptions);
    let firstRun = true;
    const selector = argsMemoize(function dependenciesChecker() {
      dependencyRecomputations++;
      const inputSelectorResults = collectInputSelectorResults(
        dependencies,
        arguments
      );
      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);
      if (process.env.NODE_ENV !== "production") {
        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);
        if (identityFunctionCheck.shouldRun) {
          identityFunctionCheck.run(
            resultFunc,
            inputSelectorResults,
            lastResult
          );
        }
        if (inputStabilityCheck.shouldRun) {
          const inputSelectorResultsCopy = collectInputSelectorResults(
            dependencies,
            arguments
          );
          inputStabilityCheck.run(
            { inputSelectorResults, inputSelectorResultsCopy },
            { memoize, memoizeOptions: finalMemoizeOptions },
            arguments
          );
        }
        if (firstRun)
          firstRun = false;
      }
      return lastResult;
    }, ...finalArgsMemoizeOptions);
    return Object.assign(selector, {
      resultFunc,
      memoizedResultFunc,
      dependencies,
      dependencyRecomputations: () => dependencyRecomputations,
      resetDependencyRecomputations: () => {
        dependencyRecomputations = 0;
      },
      lastResult: () => lastResult,
      recomputations: () => recomputations,
      resetRecomputations: () => {
        recomputations = 0;
      },
      memoize,
      argsMemoize
    });
  };
  Object.assign(createSelector2, {
    withTypes: () => createSelector2
  });
  return createSelector2;
}
var createSelector$1 = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);

// src/createStructuredSelector.ts
var createStructuredSelector = Object.assign(
  (inputSelectorsObject, selectorCreator = createSelector$1) => {
    assertIsObject(
      inputSelectorsObject,
      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`
    );
    const inputSelectorKeys = Object.keys(inputSelectorsObject);
    const dependencies = inputSelectorKeys.map(
      (key) => inputSelectorsObject[key]
    );
    const structuredSelector = selectorCreator(
      dependencies,
      (...inputSelectorResults) => {
        return inputSelectorResults.reduce((composition, value, index) => {
          composition[inputSelectorKeys[index]] = value;
          return composition;
        }, {});
      }
    );
    return structuredSelector;
  },
  { withTypes: () => createStructuredSelector }
);

const warnedOnceCache = new Set();

// TODO move to @base_ui/internals. Base UI, etc. need this helper.
function warnOnce(message, gravity = 'warning') {
  if (process.env.NODE_ENV === 'production') {
    return;
  }
  const cleanMessage = Array.isArray(message) ? message.join('\n') : message;
  if (!warnedOnceCache.has(cleanMessage)) {
    warnedOnceCache.add(cleanMessage);
    if (gravity === 'error') {
      console.error(cleanMessage);
    } else {
      console.warn(cleanMessage);
    }
  }
}

var shim = {exports: {}};

var useSyncExternalStoreShim_production = {};

/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredUseSyncExternalStoreShim_production;

function requireUseSyncExternalStoreShim_production () {
	if (hasRequiredUseSyncExternalStoreShim_production) return useSyncExternalStoreShim_production;
	hasRequiredUseSyncExternalStoreShim_production = 1;
	var React = React__default;
	function is(x, y) {
	  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);
	}
	var objectIs = "function" === typeof Object.is ? Object.is : is,
	  useState = React.useState,
	  useEffect = React.useEffect,
	  useLayoutEffect = React.useLayoutEffect,
	  useDebugValue = React.useDebugValue;
	function useSyncExternalStore$2(subscribe, getSnapshot) {
	  var value = getSnapshot(),
	    _useState = useState({ inst: { value: value, getSnapshot: getSnapshot } }),
	    inst = _useState[0].inst,
	    forceUpdate = _useState[1];
	  useLayoutEffect(
	    function () {
	      inst.value = value;
	      inst.getSnapshot = getSnapshot;
	      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	    },
	    [subscribe, value, getSnapshot]
	  );
	  useEffect(
	    function () {
	      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	      return subscribe(function () {
	        checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	      });
	    },
	    [subscribe]
	  );
	  useDebugValue(value);
	  return value;
	}
	function checkIfSnapshotChanged(inst) {
	  var latestGetSnapshot = inst.getSnapshot;
	  inst = inst.value;
	  try {
	    var nextValue = latestGetSnapshot();
	    return !objectIs(inst, nextValue);
	  } catch (error) {
	    return true;
	  }
	}
	function useSyncExternalStore$1(subscribe, getSnapshot) {
	  return getSnapshot();
	}
	var shim =
	  "undefined" === typeof window ||
	  "undefined" === typeof window.document ||
	  "undefined" === typeof window.document.createElement
	    ? useSyncExternalStore$1
	    : useSyncExternalStore$2;
	useSyncExternalStoreShim_production.useSyncExternalStore =
	  undefined !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;
	return useSyncExternalStoreShim_production;
}

var useSyncExternalStoreShim_development = {};

/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredUseSyncExternalStoreShim_development;

function requireUseSyncExternalStoreShim_development () {
	if (hasRequiredUseSyncExternalStoreShim_development) return useSyncExternalStoreShim_development;
	hasRequiredUseSyncExternalStoreShim_development = 1;
	"production" !== process.env.NODE_ENV &&
	  (function () {
	    function is(x, y) {
	      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);
	    }
	    function useSyncExternalStore$2(subscribe, getSnapshot) {
	      didWarnOld18Alpha ||
	        undefined === React.startTransition ||
	        ((didWarnOld18Alpha = true),
	        console.error(
	          "You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."
	        ));
	      var value = getSnapshot();
	      if (!didWarnUncachedGetSnapshot) {
	        var cachedValue = getSnapshot();
	        objectIs(value, cachedValue) ||
	          (console.error(
	            "The result of getSnapshot should be cached to avoid an infinite loop"
	          ),
	          (didWarnUncachedGetSnapshot = true));
	      }
	      cachedValue = useState({
	        inst: { value: value, getSnapshot: getSnapshot }
	      });
	      var inst = cachedValue[0].inst,
	        forceUpdate = cachedValue[1];
	      useLayoutEffect(
	        function () {
	          inst.value = value;
	          inst.getSnapshot = getSnapshot;
	          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	        },
	        [subscribe, value, getSnapshot]
	      );
	      useEffect(
	        function () {
	          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	          return subscribe(function () {
	            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });
	          });
	        },
	        [subscribe]
	      );
	      useDebugValue(value);
	      return value;
	    }
	    function checkIfSnapshotChanged(inst) {
	      var latestGetSnapshot = inst.getSnapshot;
	      inst = inst.value;
	      try {
	        var nextValue = latestGetSnapshot();
	        return !objectIs(inst, nextValue);
	      } catch (error) {
	        return true;
	      }
	    }
	    function useSyncExternalStore$1(subscribe, getSnapshot) {
	      return getSnapshot();
	    }
	    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&
	      "function" ===
	        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&
	      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
	    var React = React__default,
	      objectIs = "function" === typeof Object.is ? Object.is : is,
	      useState = React.useState,
	      useEffect = React.useEffect,
	      useLayoutEffect = React.useLayoutEffect,
	      useDebugValue = React.useDebugValue,
	      didWarnOld18Alpha = false,
	      didWarnUncachedGetSnapshot = false,
	      shim =
	        "undefined" === typeof window ||
	        "undefined" === typeof window.document ||
	        "undefined" === typeof window.document.createElement
	          ? useSyncExternalStore$1
	          : useSyncExternalStore$2;
	    useSyncExternalStoreShim_development.useSyncExternalStore =
	      undefined !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;
	    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&
	      "function" ===
	        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&
	      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
	  })();
	return useSyncExternalStoreShim_development;
}

if (process.env.NODE_ENV === 'production') {
  shim.exports = requireUseSyncExternalStoreShim_production();
} else {
  shim.exports = requireUseSyncExternalStoreShim_development();
}

var shimExports = shim.exports;

function isOutputSelector(selector) {
  return selector.acceptsApiRef;
}
// TODO v8: Remove this function
function applySelector(apiRef, selector) {
  if (isOutputSelector(selector)) {
    return selector(apiRef);
  }
  return selector(apiRef.current.state);
}
const defaultCompare = Object.is;
const objectShallowCompare = fastObjectShallowCompare;
const createRefs = () => ({
  state: null,
  equals: null,
  selector: null,
  args: undefined
});
const EMPTY = [];
const emptyGetSnapshot = () => null;

// TODO v8: Remove this function
const useGridSelector = (apiRef, selector, equals = defaultCompare) => {
  if (process.env.NODE_ENV !== 'production') {
    if (!apiRef.current.state) {
      warnOnce(['MUI X: `useGridSelector` has been called before the initialization of the state.', 'This hook can only be used inside the context of the grid.']);
    }
  }
  const refs = useLazyRef(createRefs);
  const didInit = refs.current.selector !== null;
  const [state, setState] = React.useState(
  // We don't use an initialization function to avoid allocations
  didInit ? null : applySelector(apiRef, selector));
  refs.current.state = state;
  refs.current.equals = equals;
  refs.current.selector = selector;
  const subscribe = React.useCallback(() => {
    if (refs.current.subscription) {
      return null;
    }
    refs.current.subscription = apiRef.current.store.subscribe(() => {
      const newState = applySelector(apiRef, refs.current.selector);
      if (!refs.current.equals(refs.current.state, newState)) {
        refs.current.state = newState;
        setState(newState);
      }
    });
    return null;
  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  EMPTY);
  const unsubscribe = React.useCallback(() => {
    return () => {
      if (refs.current.subscription) {
        refs.current.subscription();
        refs.current.subscription = undefined;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, EMPTY);
  shimExports.useSyncExternalStore(unsubscribe, subscribe, emptyGetSnapshot);
  return state;
};

const reselectCreateSelector = createSelectorCreator({
  memoize: lruMemoize,
  memoizeOptions: {
    maxSize: 1,
    equalityCheck: Object.is
  }
});

// TODO v8: Remove this type

// TODO v8: Rename this type to `OutputSelector`

// TODO v8: Remove this type

// TODO v8: Rename this type to `SelectorArgs`

// TODO v8: Remove this type

// TODO v8: Rename this type to `CreateSelectorFunction`

const cache = new WeakMap();
function checkIsAPIRef(value) {
  return 'current' in value && 'instanceId' in value.current;
}
const DEFAULT_INSTANCE_ID = {
  id: 'default'
};

// TODO v8: Remove this function
const createSelector = (a, b, c, d, e, f, ...other) => {
  if (other.length > 0) {
    throw new Error('Unsupported number of selectors');
  }
  let selector;

  // eslint-disable-next-line id-denylist
  if (a && b && c && d && e && f) {
    selector = (stateOrApiRef, instanceIdParam) => {
      const isAPIRef = checkIsAPIRef(stateOrApiRef);
      const instanceId = instanceIdParam ?? (isAPIRef ? stateOrApiRef.current.instanceId : DEFAULT_INSTANCE_ID);
      const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
      const va = a(state, instanceId);
      const vb = b(state, instanceId);
      const vc = c(state, instanceId);
      const vd = d(state, instanceId);
      const ve = e(state, instanceId);
      return f(va, vb, vc, vd, ve);
    };
    // eslint-disable-next-line id-denylist
  } else if (a && b && c && d && e) {
    selector = (stateOrApiRef, instanceIdParam) => {
      const isAPIRef = checkIsAPIRef(stateOrApiRef);
      const instanceId = instanceIdParam ?? (isAPIRef ? stateOrApiRef.current.instanceId : DEFAULT_INSTANCE_ID);
      const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
      const va = a(state, instanceId);
      const vb = b(state, instanceId);
      const vc = c(state, instanceId);
      const vd = d(state, instanceId);
      return e(va, vb, vc, vd);
    };
  } else if (a && b && c && d) {
    selector = (stateOrApiRef, instanceIdParam) => {
      const isAPIRef = checkIsAPIRef(stateOrApiRef);
      const instanceId = instanceIdParam ?? (isAPIRef ? stateOrApiRef.current.instanceId : DEFAULT_INSTANCE_ID);
      const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
      const va = a(state, instanceId);
      const vb = b(state, instanceId);
      const vc = c(state, instanceId);
      return d(va, vb, vc);
    };
  } else if (a && b && c) {
    selector = (stateOrApiRef, instanceIdParam) => {
      const isAPIRef = checkIsAPIRef(stateOrApiRef);
      const instanceId = instanceIdParam ?? (isAPIRef ? stateOrApiRef.current.instanceId : DEFAULT_INSTANCE_ID);
      const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
      const va = a(state, instanceId);
      const vb = b(state, instanceId);
      return c(va, vb);
    };
  } else if (a && b) {
    selector = (stateOrApiRef, instanceIdParam) => {
      const isAPIRef = checkIsAPIRef(stateOrApiRef);
      const instanceId = instanceIdParam ?? (isAPIRef ? stateOrApiRef.current.instanceId : DEFAULT_INSTANCE_ID);
      const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
      const va = a(state, instanceId);
      return b(va);
    };
  } else {
    throw new Error('Missing arguments');
  }

  // We use this property to detect if the selector was created with createSelector
  // or it's only a simple function the receives the state and returns part of it.
  selector.acceptsApiRef = true;
  return selector;
};

// TODO v8: Remove this function
const createSelectorMemoized = (...args) => {
  const selector = (stateOrApiRef, instanceId) => {
    const isAPIRef = checkIsAPIRef(stateOrApiRef);
    const cacheKey = isAPIRef ? stateOrApiRef.current.instanceId : instanceId ?? DEFAULT_INSTANCE_ID;
    const state = isAPIRef ? stateOrApiRef.current.state : stateOrApiRef;
    if (process.env.NODE_ENV !== 'production') {
      if (cacheKey.id === 'default') {
        warnOnce(['MUI X: A selector was called without passing the instance ID, which may impact the performance of the grid.', 'To fix, call it with `apiRef`, for example `mySelector(apiRef)`, or pass the instance ID explicitly, for example `mySelector(state, apiRef.current.instanceId)`.']);
      }
    }
    const cacheArgsInit = cache.get(cacheKey);
    const cacheArgs = cacheArgsInit ?? new Map();
    const cacheFn = cacheArgs?.get(args);
    if (cacheArgs && cacheFn) {
      // We pass the cache key because the called selector might have as
      // dependency another selector created with this `createSelector`.
      return cacheFn(state, cacheKey);
    }
    const fn = reselectCreateSelector(...args);
    if (!cacheArgsInit) {
      cache.set(cacheKey, cacheArgs);
    }
    cacheArgs.set(args, fn);
    return fn(state, cacheKey);
  };

  // We use this property to detect if the selector was created with createSelector
  // or it's only a simple function the receives the state and returns part of it.
  selector.acceptsApiRef = true;
  return selector;
};

function getDataGridUtilityClass(slot) {
  return generateUtilityClass('MuiDataGrid', slot);
}
generateUtilityClasses('MuiDataGrid', ['actionsCell', 'aggregationColumnHeader', 'aggregationColumnHeader--alignLeft', 'aggregationColumnHeader--alignCenter', 'aggregationColumnHeader--alignRight', 'aggregationColumnHeaderLabel', 'autoHeight', 'autosizing', 'booleanCell', 'cell--editable', 'cell--editing', 'cell--flex', 'cell--textCenter', 'cell--textLeft', 'cell--textRight', 'cell--rangeTop', 'cell--rangeBottom', 'cell--rangeLeft', 'cell--rangeRight', 'cell--pinnedLeft', 'cell--pinnedRight', 'cell--selectionMode', 'cell', 'cellCheckbox', 'cellEmpty', 'cellSkeleton', 'cellOffsetLeft', 'checkboxInput', 'columnHeader', 'columnHeader--alignCenter', 'columnHeader--alignLeft', 'columnHeader--alignRight', 'columnHeader--dragging', 'columnHeader--moving', 'columnHeader--numeric', 'columnHeader--sortable', 'columnHeader--sorted', 'columnHeader--filtered', 'columnHeader--pinnedLeft', 'columnHeader--pinnedRight', 'columnHeader--last', 'columnHeader--lastUnpinned', 'columnHeader--siblingFocused', 'columnHeaderCheckbox', 'columnHeaderDraggableContainer', 'columnHeaderTitle', 'columnHeaderTitleContainer', 'columnHeaderTitleContainerContent', 'columnHeader--filledGroup', 'columnHeader--emptyGroup', 'columnHeaders', 'columnSeparator--resizable', 'columnSeparator--resizing', 'columnSeparator--sideLeft', 'columnSeparator--sideRight', 'columnSeparator', 'columnsManagement', 'columnsManagementRow', 'columnsManagementHeader', 'columnsManagementSearchInput', 'columnsManagementFooter', 'container--top', 'container--bottom', 'detailPanel', 'detailPanels', 'detailPanelToggleCell', 'detailPanelToggleCell--expanded', 'footerCell', 'panel', 'panelHeader', 'panelWrapper', 'panelContent', 'panelFooter', 'paper', 'editBooleanCell', 'editInputCell', 'filler', 'filler--borderBottom', 'filler--pinnedLeft', 'filler--pinnedRight', 'filterForm', 'filterFormDeleteIcon', 'filterFormLogicOperatorInput', 'filterFormColumnInput', 'filterFormOperatorInput', 'filterFormValueInput', 'filterIcon', 'footerContainer', 'headerFilterRow', 'iconButtonContainer', 'iconSeparator', 'main', 'main--hasPinnedRight', 'main--hasSkeletonLoadingOverlay', 'menu', 'menuIcon', 'menuIconButton', 'menuOpen', 'menuList', 'overlay', 'overlayWrapper', 'overlayWrapperInner', 'root', 'root--densityStandard', 'root--densityComfortable', 'root--densityCompact', 'root--disableUserSelection', 'root--noToolbar', 'row', 'row--editable', 'row--editing', 'row--firstVisible', 'row--lastVisible', 'row--dragging', 'row--dynamicHeight', 'row--detailPanelExpanded', 'row--borderBottom', 'rowReorderCellPlaceholder', 'rowCount', 'rowReorderCellContainer', 'rowReorderCell', 'rowReorderCell--draggable', 'rowSkeleton', 'scrollArea--left', 'scrollArea--right', 'scrollArea', 'scrollbar', 'scrollbar--vertical', 'scrollbar--horizontal', 'scrollbarFiller', 'scrollbarFiller--header', 'scrollbarFiller--borderTop', 'scrollbarFiller--borderBottom', 'scrollbarFiller--pinnedRight', 'selectedRowCount', 'sortIcon', 'toolbarContainer', 'toolbarFilterList', 'virtualScroller', 'virtualScroller--hasScrollX', 'virtualScrollerContent', 'virtualScrollerContent--overflowed', 'virtualScrollerRenderZone', 'pinnedColumns', 'withVerticalBorder', 'withBorderColor', 'cell--withRightBorder', 'cell--withLeftBorder', 'columnHeader--withRightBorder', 'columnHeader--withLeftBorder', 'treeDataGroupingCell', 'treeDataGroupingCellToggle', 'treeDataGroupingCellLoadingContainer', 'groupingCriteriaCell', 'groupingCriteriaCellToggle', 'groupingCriteriaCellLoadingContainer', 'pinnedRows', 'pinnedRows--top', 'pinnedRows--bottom', 'pinnedRowsRenderZone']);

/**
 * Signal to the underlying logic what version of the public component API
 * of the Data Grid is exposed.
 */
var GridSignature = /*#__PURE__*/function (GridSignature) {
  GridSignature["DataGrid"] = "DataGrid";
  GridSignature["DataGridPro"] = "DataGridPro";
  GridSignature["DataGridPremium"] = "DataGridPremium";
  return GridSignature;
}(GridSignature || {});

function _objectWithoutPropertiesLoose(r, e) {
  if (null == r) return {};
  var t = {};
  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {
    if (-1 !== e.indexOf(n)) continue;
    t[n] = r[n];
  }
  return t;
}

const _excluded$7 = ["id", "value", "formattedValue", "api", "field", "row", "rowNode", "colDef", "cellMode", "isEditable", "tabIndex", "hasFocus", "isValidating", "debounceMs", "isProcessingProps", "onValueChange"];
const useUtilityClasses$4 = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['editInputCell']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
const GridEditInputCellRoot = styled$2(InputBase__default, {
  name: 'MuiDataGrid',
  slot: 'EditInputCell',
  overridesResolver: (props, styles) => styles.editInputCell
})(({
  theme
}) => _extends({}, theme.typography.body2, {
  padding: '1px 0',
  '& input': {
    padding: '0 16px',
    height: '100%'
  }
}));
const GridEditInputCell = forwardRef((props, ref) => {
  const rootProps = useGridRootProps();
  const {
      id,
      value,
      field,
      colDef,
      hasFocus,
      debounceMs = 200,
      isProcessingProps,
      onValueChange
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded$7);
  const apiRef = useGridApiContext();
  const inputRef = React.useRef(null);
  const [valueState, setValueState] = React.useState(value);
  const classes = useUtilityClasses$4(rootProps);
  const handleChange = React.useCallback(async event => {
    const newValue = event.target.value;
    if (onValueChange) {
      await onValueChange(event, newValue);
    }
    const column = apiRef.current.getColumn(field);
    let parsedValue = newValue;
    if (column.valueParser) {
      parsedValue = column.valueParser(newValue, apiRef.current.getRow(id), column, apiRef);
    }
    setValueState(parsedValue);
    apiRef.current.setEditCellValue({
      id,
      field,
      value: parsedValue,
      debounceMs,
      unstable_skipValueParser: true
    }, event);
  }, [apiRef, debounceMs, field, id, onValueChange]);
  const meta = apiRef.current.unstable_getEditCellMeta(id, field);
  React.useEffect(() => {
    if (meta?.changeReason !== 'debouncedSetEditCellValue') {
      setValueState(value);
    }
  }, [meta, value]);
  useEnhancedEffect(() => {
    if (hasFocus) {
      inputRef.current.focus();
    }
  }, [hasFocus]);
  return /*#__PURE__*/jsx(GridEditInputCellRoot, _extends({
    inputRef: inputRef,
    className: classes.root,
    ownerState: rootProps,
    fullWidth: true,
    type: colDef.type === 'number' ? colDef.type : 'text',
    value: valueState ?? '',
    onChange: handleChange,
    endAdornment: isProcessingProps ? /*#__PURE__*/jsx(rootProps.slots.loadIcon, {
      fontSize: "small",
      color: "action"
    }) : undefined
  }, other, {
    ref: ref
  }));
});
process.env.NODE_ENV !== "production" ? GridEditInputCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: PropTypes.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,
  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: PropTypes.object.isRequired,
  debounceMs: PropTypes.number,
  /**
   * The column field of the cell that triggered the event.
   */
  field: PropTypes.string.isRequired,
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: PropTypes.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: PropTypes.bool.isRequired,
  /**
   * The grid row id.
   */
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: PropTypes.bool,
  isProcessingProps: PropTypes.bool,
  isValidating: PropTypes.bool,
  /**
   * Callback called when the value is changed by the user.
   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.
   * @param {Date | null} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.
   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`
   */
  onValueChange: PropTypes.func,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: PropTypes.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: PropTypes.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: PropTypes.any
} : undefined;
const renderEditInputCell = params => /*#__PURE__*/jsx(GridEditInputCell, _extends({}, params));

const gridRowsStateSelector = state => state.rows;
createSelector(gridRowsStateSelector, rows => rows.totalRowCount);
createSelector(gridRowsStateSelector, rows => rows.loading);
createSelector(gridRowsStateSelector, rows => rows.totalTopLevelRowCount);

// TODO rows v6: Rename
const gridRowsLookupSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIdToModelLookup);
createSelector(gridRowsStateSelector, rows => rows.dataRowIdToIdLookup);
const gridRowTreeSelector = createSelector(gridRowsStateSelector, rows => rows.tree);
createSelector(gridRowsStateSelector, rows => rows.groupsToFetch);
createSelector(gridRowsStateSelector, rows => rows.groupingName);
createSelector(gridRowsStateSelector, rows => rows.treeDepths);
const gridRowMaximumTreeDepthSelector = createSelectorMemoized(gridRowsStateSelector, rows => {
  const entries = Object.entries(rows.treeDepths);
  if (entries.length === 0) {
    return 1;
  }
  return (entries.filter(([, nodeCount]) => nodeCount > 0).map(([depth]) => Number(depth)).sort((a, b) => b - a)[0] ?? 0) + 1;
});
createSelector(gridRowsStateSelector, rows => rows.dataRowIds);

/**
 * @ignore - do not document.
 */
const gridAdditionalRowGroupsSelector = createSelector(gridRowsStateSelector, rows => rows?.additionalRowGroups);

/**
 * @ignore - do not document.
 */
const gridPinnedRowsSelector = createSelectorMemoized(gridAdditionalRowGroupsSelector, additionalRowGroups => {
  const rawPinnedRows = additionalRowGroups?.pinnedRows;
  return {
    bottom: rawPinnedRows?.bottom?.map(rowEntry => ({
      id: rowEntry.id,
      model: rowEntry.model ?? {}
    })) ?? [],
    top: rawPinnedRows?.top?.map(rowEntry => ({
      id: rowEntry.id,
      model: rowEntry.model ?? {}
    })) ?? []
  };
});

/**
 * @ignore - do not document.
 */
createSelector(gridPinnedRowsSelector, pinnedRows => {
  return (pinnedRows?.top?.length || 0) + (pinnedRows?.bottom?.length || 0);
});

const gridNillComparator = (v1, v2) => {
  if (v1 == null && v2 != null) {
    return -1;
  }
  if (v2 == null && v1 != null) {
    return 1;
  }
  if (v1 == null && v2 == null) {
    return 0;
  }
  return null;
};
const collator = new Intl.Collator();
const gridStringOrNumberComparator = (value1, value2) => {
  const nillResult = gridNillComparator(value1, value2);
  if (nillResult !== null) {
    return nillResult;
  }
  if (typeof value1 === 'string') {
    return collator.compare(value1.toString(), value2.toString());
  }
  return value1 - value2;
};
const gridNumberComparator = (value1, value2) => {
  const nillResult = gridNillComparator(value1, value2);
  if (nillResult !== null) {
    return nillResult;
  }
  return Number(value1) - Number(value2);
};

const _excluded$6 = ["item", "applyValue", "type", "apiRef", "focusElementRef", "tabIndex", "disabled", "isFilterActive", "clearButton", "InputProps", "variant"];
function GridFilterInputValue(props) {
  const {
      item,
      applyValue,
      type,
      apiRef,
      focusElementRef,
      tabIndex,
      disabled,
      clearButton,
      InputProps,
      variant = 'standard'
    } = props,
    others = _objectWithoutPropertiesLoose(props, _excluded$6);
  const filterTimeout = useTimeout();
  const [filterValueState, setFilterValueState] = React.useState(sanitizeFilterItemValue$1(item.value));
  const [applying, setIsApplying] = React.useState(false);
  const id = useId();
  const rootProps = useGridRootProps();
  const onFilterChange = React.useCallback(event => {
    const value = sanitizeFilterItemValue$1(event.target.value);
    setFilterValueState(value);
    setIsApplying(true);
    filterTimeout.start(rootProps.filterDebounceMs, () => {
      const newItem = _extends({}, item, {
        value: type === 'number' && !Number.isNaN(Number(value)) ? Number(value) : value,
        fromInput: id
      });
      applyValue(newItem);
      setIsApplying(false);
    });
  }, [filterTimeout, rootProps.filterDebounceMs, item, type, id, applyValue]);
  React.useEffect(() => {
    const itemPlusTag = item;
    if (itemPlusTag.fromInput !== id || item.value == null) {
      setFilterValueState(sanitizeFilterItemValue$1(item.value));
    }
  }, [id, item]);
  return /*#__PURE__*/jsx(rootProps.slots.baseTextField, _extends({
    id: id,
    label: apiRef.current.getLocaleText('filterPanelInputLabel'),
    placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),
    value: filterValueState ?? '',
    onChange: onFilterChange,
    variant: variant,
    type: type || 'text',
    InputProps: _extends({}, applying || clearButton ? {
      endAdornment: applying ? /*#__PURE__*/jsx(rootProps.slots.loadIcon, {
        fontSize: "small",
        color: "action"
      }) : clearButton
    } : {}, {
      disabled
    }, InputProps, {
      inputProps: _extends({
        tabIndex
      }, InputProps?.inputProps)
    }),
    InputLabelProps: {
      shrink: true
    },
    inputRef: focusElementRef
  }, others, rootProps.slotProps?.baseTextField));
}
function sanitizeFilterItemValue$1(value) {
  if (value == null || value === '') {
    return undefined;
  }
  return String(value);
}
process.env.NODE_ENV !== "production" ? GridFilterInputValue.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: PropTypes.shape({
    current: PropTypes.object.isRequired
  }).isRequired,
  applyValue: PropTypes.func.isRequired,
  clearButton: PropTypes.node,
  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),
  /**
   * It is `true` if the filter either has a value or an operator with no value
   * required is selected (for example `isEmpty`)
   */
  isFilterActive: PropTypes.bool,
  item: PropTypes.shape({
    field: PropTypes.string.isRequired,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    operator: PropTypes.string.isRequired,
    value: PropTypes.any
  }).isRequired
} : undefined;

function escapeRegExp(value) {
  return value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
}

const _excluded$5 = ["item", "applyValue", "type", "apiRef", "focusElementRef", "color", "error", "helperText", "size", "variant"],
  _excluded2 = ["key"];
function GridFilterInputMultipleValue(props) {
  const {
      item,
      applyValue,
      type,
      apiRef,
      focusElementRef,
      color,
      error,
      helperText,
      size,
      variant = 'standard'
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded$5);
  const TextFieldProps = {
    color,
    error,
    helperText,
    size,
    variant
  };
  const [filterValueState, setFilterValueState] = React.useState(item.value || []);
  const id = useId();
  const rootProps = useGridRootProps();
  React.useEffect(() => {
    const itemValue = item.value ?? [];
    setFilterValueState(itemValue.map(String));
  }, [item.value]);
  const handleChange = React.useCallback((event, value) => {
    setFilterValueState(value.map(String));
    applyValue(_extends({}, item, {
      value: [...value.map(filterItemValue => type === 'number' ? Number(filterItemValue) : filterItemValue)]
    }));
  }, [applyValue, item, type]);
  return /*#__PURE__*/jsx(Autocomplete__default, _extends({
    multiple: true,
    freeSolo: true,
    options: [],
    filterOptions: (options, params) => {
      const {
        inputValue
      } = params;
      return inputValue == null || inputValue === '' ? [] : [inputValue];
    },
    id: id,
    value: filterValueState,
    onChange: handleChange,
    renderTags: (value, getTagProps) => value.map((option, index) => {
      const _getTagProps = getTagProps({
          index
        }),
        {
          key
        } = _getTagProps,
        tagProps = _objectWithoutPropertiesLoose(_getTagProps, _excluded2);
      return /*#__PURE__*/jsx(rootProps.slots.baseChip, _extends({
        variant: "outlined",
        size: "small",
        label: option
      }, tagProps), key);
    }),
    renderInput: params => /*#__PURE__*/jsx(rootProps.slots.baseTextField, _extends({}, params, {
      label: apiRef.current.getLocaleText('filterPanelInputLabel'),
      placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),
      InputLabelProps: _extends({}, params.InputLabelProps, {
        shrink: true
      }),
      inputRef: focusElementRef,
      type: type || 'text'
    }, TextFieldProps, rootProps.slotProps?.baseTextField))
  }, other));
}
process.env.NODE_ENV !== "production" ? GridFilterInputMultipleValue.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: PropTypes.shape({
    current: PropTypes.object.isRequired
  }).isRequired,
  applyValue: PropTypes.func.isRequired,
  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),
  item: PropTypes.shape({
    field: PropTypes.string.isRequired,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    operator: PropTypes.string.isRequired,
    value: PropTypes.any
  }).isRequired,
  type: PropTypes.oneOf(['date', 'datetime-local', 'number', 'text'])
} : undefined;

const EMPTY_PINNED_COLUMN_FIELDS = {
  left: [],
  right: []
};

/**
 * Get the theme state
 * @category Core
 */
const gridIsRtlSelector = state => state.isRtl;

/**
 * Get the columns state
 * @category Columns
 */
const gridColumnsStateSelector = state => state.columns;

/**
 * Get an array of column fields in the order rendered on screen.
 * @category Columns
 */
const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);

/**
 * Get the columns as a lookup (an object containing the field for keys and the definition for values).
 * @category Columns
 */
const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);

/**
 * Get an array of column definitions in the order rendered on screen..
 * @category Columns
 */
const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));

/**
 * Get the column visibility model, containing the visibility status of each column.
 * If a column is not registered in the model, it is visible.
 * @category Visible Columns
 */
const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);

/**
 * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).
 * @category Visible Columns
 */
const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, (columns, columnVisibilityModel) => columns.filter(column => columnVisibilityModel[column.field] !== false));

/**
 * Get the field of each visible column.
 * @category Visible Columns
 */
const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));

/**
 * Get the visible pinned columns model.
 * @category Visible Columns
 */
const gridPinnedColumnsSelector = state => state.pinnedColumns;

/**
 * Get the visible pinned columns.
 * @category Visible Columns
 */
createSelectorMemoized(gridColumnsStateSelector, gridPinnedColumnsSelector, gridVisibleColumnFieldsSelector, gridIsRtlSelector, (columnsState, model, visibleColumnFields, isRtl) => {
  const visiblePinnedFields = filterVisibleColumns(model, visibleColumnFields, isRtl);
  const visiblePinnedColumns = {
    left: visiblePinnedFields.left.map(field => columnsState.lookup[field]),
    right: visiblePinnedFields.right.map(field => columnsState.lookup[field])
  };
  return visiblePinnedColumns;
});
function filterVisibleColumns(pinnedColumns, columns, invert) {
  if (!Array.isArray(pinnedColumns.left) && !Array.isArray(pinnedColumns.right)) {
    return EMPTY_PINNED_COLUMN_FIELDS;
  }
  if (pinnedColumns.left?.length === 0 && pinnedColumns.right?.length === 0) {
    return EMPTY_PINNED_COLUMN_FIELDS;
  }
  const filter = (newPinnedColumns, remainingColumns) => {
    if (!Array.isArray(newPinnedColumns)) {
      return [];
    }
    return newPinnedColumns.filter(field => remainingColumns.includes(field));
  };
  const leftPinnedColumns = filter(pinnedColumns.left, columns);
  const columnsWithoutLeftPinnedColumns = columns.filter(
  // Filter out from the remaining columns those columns already pinned to the left
  field => !leftPinnedColumns.includes(field));
  const rightPinnedColumns = filter(pinnedColumns.right, columnsWithoutLeftPinnedColumns);
  if (invert) {
    return {
      left: rightPinnedColumns,
      right: leftPinnedColumns
    };
  }
  return {
    left: leftPinnedColumns,
    right: rightPinnedColumns
  };
}

/**
 * Get the left position in pixel of each visible columns relative to the left of the first column.
 * @category Visible Columns
 */
createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {
  const positions = [];
  let currentPosition = 0;
  for (let i = 0; i < visibleColumns.length; i += 1) {
    positions.push(currentPosition);
    currentPosition += visibleColumns[i].computedWidth;
  }
  return positions;
});

/**
 * Get the filterable columns as an array.
 * @category Columns
 */
createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));

/**
 * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).
 * @category Columns
 */
createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {
  if (col.filterable) {
    acc[col.field] = col;
  }
  return acc;
}, {}));

/**
 * Checks if some column has a colSpan field.
 * @category Columns
 * @ignore - Do not document
 */
createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.some(column => column.colSpan !== undefined));

const removeDiacritics = value => {
  if (typeof value === 'string') {
    return value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }
  return value;
};

const getGridStringQuickFilterFn = value => {
  if (!value) {
    return null;
  }
  const filterRegex = new RegExp(escapeRegExp(value), 'i');
  return (_, row, column, apiRef) => {
    let columnValue = apiRef.current.getRowFormattedValue(row, column);
    if (apiRef.current.ignoreDiacritics) {
      columnValue = removeDiacritics(columnValue);
    }
    return columnValue != null ? filterRegex.test(columnValue.toString()) : false;
  };
};
const createContainsFilterFn = (disableTrim, negate) => filterItem => {
  if (!filterItem.value) {
    return null;
  }
  const trimmedValue = disableTrim ? filterItem.value : filterItem.value.trim();
  const filterRegex = new RegExp(escapeRegExp(trimmedValue), 'i');
  return value => {
    if (value == null) {
      return negate;
    }
    const matches = filterRegex.test(String(value));
    return negate ? !matches : matches;
  };
};
const createEqualityFilterFn = (disableTrim, negate) => filterItem => {
  if (!filterItem.value) {
    return null;
  }
  const trimmedValue = disableTrim ? filterItem.value : filterItem.value.trim();
  const collator = new Intl.Collator(undefined, {
    sensitivity: 'base',
    usage: 'search'
  });
  return value => {
    if (value == null) {
      return negate;
    }
    const isEqual = collator.compare(trimmedValue, value.toString()) === 0;
    return negate ? !isEqual : isEqual;
  };
};
const createEmptyFilterFn = negate => () => {
  return value => {
    const isEmpty = value === '' || value == null;
    return negate ? !isEmpty : isEmpty;
  };
};
const getGridStringOperators = (disableTrim = false) => [{
  value: 'contains',
  getApplyFilterFn: createContainsFilterFn(disableTrim, false),
  InputComponent: GridFilterInputValue
}, {
  value: 'doesNotContain',
  getApplyFilterFn: createContainsFilterFn(disableTrim, true),
  InputComponent: GridFilterInputValue
}, {
  value: 'equals',
  getApplyFilterFn: createEqualityFilterFn(disableTrim, false),
  InputComponent: GridFilterInputValue
}, {
  value: 'doesNotEqual',
  getApplyFilterFn: createEqualityFilterFn(disableTrim, true),
  InputComponent: GridFilterInputValue
}, {
  value: 'startsWith',
  getApplyFilterFn: filterItem => {
    if (!filterItem.value) {
      return null;
    }
    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();
    const filterRegex = new RegExp(`^${escapeRegExp(filterItemValue)}.*$`, 'i');
    return value => {
      return value != null ? filterRegex.test(value.toString()) : false;
    };
  },
  InputComponent: GridFilterInputValue
}, {
  value: 'endsWith',
  getApplyFilterFn: filterItem => {
    if (!filterItem.value) {
      return null;
    }
    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();
    const filterRegex = new RegExp(`.*${escapeRegExp(filterItemValue)}$`, 'i');
    return value => {
      return value != null ? filterRegex.test(value.toString()) : false;
    };
  },
  InputComponent: GridFilterInputValue
}, {
  value: 'isEmpty',
  getApplyFilterFn: createEmptyFilterFn(false),
  requiresFilterValue: false
}, {
  value: 'isNotEmpty',
  getApplyFilterFn: createEmptyFilterFn(true),
  requiresFilterValue: false
}, {
  value: 'isAnyOf',
  getApplyFilterFn: filterItem => {
    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {
      return null;
    }
    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.map(val => val.trim());
    const collator = new Intl.Collator(undefined, {
      sensitivity: 'base',
      usage: 'search'
    });
    return value => value != null ? filterItemValue.some(filterValue => {
      return collator.compare(filterValue, value.toString() || '') === 0;
    }) : false;
  },
  InputComponent: GridFilterInputMultipleValue
}];

/**
 * TODO: Move pro and premium properties outside of this Community file
 */
const GRID_STRING_COL_DEF = {
  width: 100,
  minWidth: 50,
  maxWidth: Infinity,
  hideable: true,
  sortable: true,
  resizable: true,
  filterable: true,
  groupable: true,
  pinnable: true,
  // @ts-ignore
  aggregable: true,
  editable: false,
  sortComparator: gridStringOrNumberComparator,
  type: 'string',
  align: 'left',
  filterOperators: getGridStringOperators(),
  renderEditCell: renderEditInputCell,
  getApplyQuickFilterFn: getGridStringQuickFilterFn
};

const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');
const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;

const GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD = '__row_group_by_columns_group__';

const _excluded$4 = ["id", "value", "formattedValue", "api", "field", "row", "rowNode", "colDef", "cellMode", "isEditable", "hasFocus", "tabIndex", "hideDescendantCount"];
const useUtilityClasses$3 = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['booleanCell']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridBooleanCellRaw(props) {
  const {
      value,
      rowNode
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded$4);
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses$3(ownerState);
  const maxDepth = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector);
  const isServerSideRowGroupingRow =
  // @ts-expect-error - Access tree data prop
  maxDepth > 0 && rowNode.type === 'group' && rootProps.treeData === false;
  const Icon = React.useMemo(() => value ? rootProps.slots.booleanCellTrueIcon : rootProps.slots.booleanCellFalseIcon, [rootProps.slots.booleanCellFalseIcon, rootProps.slots.booleanCellTrueIcon, value]);
  if (isServerSideRowGroupingRow && value === undefined) {
    return null;
  }
  return /*#__PURE__*/jsx(Icon, _extends({
    fontSize: "small",
    className: classes.root,
    titleAccess: apiRef.current.getLocaleText(value ? 'booleanCellTrueLabel' : 'booleanCellFalseLabel'),
    "data-value": Boolean(value)
  }, other));
}
process.env.NODE_ENV !== "production" ? GridBooleanCellRaw.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: PropTypes.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: PropTypes.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: PropTypes.string.isRequired,
  /**
   * A ref allowing to set imperative focus.
   * It can be passed to the element that should receive focus.
   * @ignore - do not document.
   */
  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
    current: PropTypes.shape({
      focus: PropTypes.func.isRequired
    })
  })]),
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: PropTypes.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: PropTypes.bool.isRequired,
  hideDescendantCount: PropTypes.bool,
  /**
   * The grid row id.
   */
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: PropTypes.bool,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: PropTypes.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: PropTypes.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: PropTypes.any
} : undefined;
const GridBooleanCell = /*#__PURE__*/React.memo(GridBooleanCellRaw);
const renderBooleanCell = params => {
  if (params.field !== GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD && isAutogeneratedRowNode(params.rowNode)) {
    return '';
  }
  return /*#__PURE__*/jsx(GridBooleanCell, _extends({}, params));
};

const _excluded$3 = ["id", "value", "formattedValue", "api", "field", "row", "rowNode", "colDef", "cellMode", "isEditable", "tabIndex", "className", "hasFocus", "isValidating", "isProcessingProps", "error", "onValueChange"];
const useUtilityClasses$2 = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['editBooleanCell']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
function GridEditBooleanCell(props) {
  const {
      id: idProp,
      value,
      field,
      className,
      hasFocus,
      onValueChange
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded$3);
  const apiRef = useGridApiContext();
  const inputRef = React.useRef(null);
  const id = useId();
  const [valueState, setValueState] = React.useState(value);
  const rootProps = useGridRootProps();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses$2(ownerState);
  const handleChange = React.useCallback(async event => {
    const newValue = event.target.checked;
    if (onValueChange) {
      await onValueChange(event, newValue);
    }
    setValueState(newValue);
    await apiRef.current.setEditCellValue({
      id: idProp,
      field,
      value: newValue
    }, event);
  }, [apiRef, field, idProp, onValueChange]);
  React.useEffect(() => {
    setValueState(value);
  }, [value]);
  useEnhancedEffect(() => {
    if (hasFocus) {
      inputRef.current.focus();
    }
  }, [hasFocus]);
  return /*#__PURE__*/jsx("label", _extends({
    htmlFor: id,
    className: clsx(classes.root, className)
  }, other, {
    children: /*#__PURE__*/jsx(rootProps.slots.baseCheckbox, _extends({
      id: id,
      inputRef: inputRef,
      checked: Boolean(valueState),
      onChange: handleChange,
      size: "small"
    }, rootProps.slotProps?.baseCheckbox))
  }));
}
process.env.NODE_ENV !== "production" ? GridEditBooleanCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: PropTypes.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,
  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: PropTypes.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: PropTypes.string.isRequired,
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: PropTypes.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: PropTypes.bool.isRequired,
  /**
   * The grid row id.
   */
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: PropTypes.bool,
  isProcessingProps: PropTypes.bool,
  isValidating: PropTypes.bool,
  /**
   * Callback called when the value is changed by the user.
   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.
   * @param {boolean} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.
   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`
   */
  onValueChange: PropTypes.func,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: PropTypes.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: PropTypes.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: PropTypes.any
} : undefined;
const renderEditBooleanCell = params => /*#__PURE__*/jsx(GridEditBooleanCell, _extends({}, params));

const _excluded$2 = ["item", "applyValue", "apiRef", "focusElementRef", "isFilterActive", "clearButton", "tabIndex", "label", "variant", "InputLabelProps"];
const sanitizeFilterItemValue = value => {
  if (String(value).toLowerCase() === 'true') {
    return true;
  }
  if (String(value).toLowerCase() === 'false') {
    return false;
  }
  return undefined;
};
const BooleanOperatorContainer = styled$2('div')({
  display: 'flex',
  alignItems: 'center',
  width: '100%',
  [`& button`]: {
    margin: 'auto 0px 5px 5px'
  }
});
function GridFilterInputBoolean(props) {
  const {
      item,
      applyValue,
      apiRef,
      focusElementRef,
      clearButton,
      tabIndex,
      label: labelProp,
      variant = 'standard'
    } = props,
    others = _objectWithoutPropertiesLoose(props, _excluded$2);
  const [filterValueState, setFilterValueState] = React.useState(sanitizeFilterItemValue(item.value));
  const rootProps = useGridRootProps();
  const labelId = useId();
  const selectId = useId();
  const baseSelectProps = rootProps.slotProps?.baseSelect || {};
  const isSelectNative = baseSelectProps.native ?? false;
  const baseSelectOptionProps = rootProps.slotProps?.baseSelectOption || {};
  const onFilterChange = React.useCallback(event => {
    const value = sanitizeFilterItemValue(event.target.value);
    setFilterValueState(value);
    applyValue(_extends({}, item, {
      value
    }));
  }, [applyValue, item]);
  React.useEffect(() => {
    setFilterValueState(sanitizeFilterItemValue(item.value));
  }, [item.value]);
  const label = labelProp ?? apiRef.current.getLocaleText('filterPanelInputLabel');
  return /*#__PURE__*/jsxs(BooleanOperatorContainer, {
    children: [/*#__PURE__*/jsxs(rootProps.slots.baseFormControl, {
      fullWidth: true,
      children: [/*#__PURE__*/jsx(rootProps.slots.baseInputLabel, _extends({}, rootProps.slotProps?.baseInputLabel, {
        id: labelId,
        shrink: true,
        variant: variant,
        children: label
      })), /*#__PURE__*/jsxs(rootProps.slots.baseSelect, _extends({
        labelId: labelId,
        id: selectId,
        label: label,
        value: filterValueState === undefined ? '' : String(filterValueState),
        onChange: onFilterChange,
        variant: variant,
        notched: variant === 'outlined' ? true : undefined,
        native: isSelectNative,
        displayEmpty: true,
        inputProps: {
          ref: focusElementRef,
          tabIndex
        }
      }, others /* FIXME: typing error */, baseSelectProps, {
        children: [/*#__PURE__*/jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {
          native: isSelectNative,
          value: "",
          children: apiRef.current.getLocaleText('filterValueAny')
        })), /*#__PURE__*/jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {
          native: isSelectNative,
          value: "true",
          children: apiRef.current.getLocaleText('filterValueTrue')
        })), /*#__PURE__*/jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {
          native: isSelectNative,
          value: "false",
          children: apiRef.current.getLocaleText('filterValueFalse')
        }))]
      }))]
    }), clearButton]
  });
}
process.env.NODE_ENV !== "production" ? GridFilterInputBoolean.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: PropTypes.shape({
    current: PropTypes.object.isRequired
  }).isRequired,
  applyValue: PropTypes.func.isRequired,
  clearButton: PropTypes.node,
  focusElementRef: refType,
  /**
   * It is `true` if the filter either has a value or an operator with no value
   * required is selected (for example `isEmpty`)
   */
  isFilterActive: PropTypes.bool,
  item: PropTypes.shape({
    field: PropTypes.string.isRequired,
    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    operator: PropTypes.string.isRequired,
    value: PropTypes.any
  }).isRequired
} : undefined;

const getGridBooleanOperators = () => [{
  value: 'is',
  getApplyFilterFn: filterItem => {
    const sanitizedValue = sanitizeFilterItemValue(filterItem.value);
    if (sanitizedValue === undefined) {
      return null;
    }
    return value => Boolean(value) === sanitizedValue;
  },
  InputComponent: GridFilterInputBoolean
}];

const gridBooleanFormatter = (value, row, column, apiRef) => {
  return value ? apiRef.current.getLocaleText('booleanCellTrueLabel') : apiRef.current.getLocaleText('booleanCellFalseLabel');
};
const stringToBoolean = value => {
  switch (value.toLowerCase().trim()) {
    case 'true':
    case 'yes':
    case '1':
      return true;
    case 'false':
    case 'no':
    case '0':
    case 'null':
    case 'undefined':
      return false;
    default:
      return undefined;
  }
};
const GRID_BOOLEAN_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {
  type: 'boolean',
  display: 'flex',
  align: 'center',
  headerAlign: 'center',
  renderCell: renderBooleanCell,
  renderEditCell: renderEditBooleanCell,
  sortComparator: gridNumberComparator,
  valueFormatter: gridBooleanFormatter,
  filterOperators: getGridBooleanOperators(),
  getApplyQuickFilterFn: undefined,
  // @ts-ignore
  aggregable: false,
  // @ts-ignore
  pastedValueParser: value => stringToBoolean(value)
});

/**
 * @category Sorting
 * @ignore - do not document.
 */
const gridSortingStateSelector = state => state.sorting;

/**
 * Get the id of the rows after the sorting process.
 * @category Sorting
 */
const gridSortedRowIdsSelector = createSelector(gridSortingStateSelector, sortingState => sortingState.sortedRows);

/**
 * Get the id and the model of the rows after the sorting process.
 * @category Sorting
 */
const gridSortedRowEntriesSelector = createSelectorMemoized(gridSortedRowIdsSelector, gridRowsLookupSelector, gridRowTreeSelector, (sortedIds, idRowsLookup, rowTree) => sortedIds.reduce((acc, id) => {
  const model = idRowsLookup[id];
  if (model) {
    acc.push({
      id,
      model
    });
  } else {
    const rowNode = rowTree[id];
    if (rowNode && isAutogeneratedRowNode(rowNode)) {
      acc.push({
        id,
        model: {
          [GRID_ID_AUTOGENERATED]: id
        }
      });
    }
  }
  return acc;
}, []));

/**
 * Get the current sorting model.
 * @category Sorting
 */
const gridSortModelSelector = createSelector(gridSortingStateSelector, sorting => sorting.sortModel);
/**
 * @category Sorting
 * @ignore - do not document.
 */
createSelectorMemoized(gridSortModelSelector, sortModel => {
  const result = sortModel.reduce((res, sortItem, index) => {
    res[sortItem.field] = {
      sortDirection: sortItem.sort,
      sortIndex: sortModel.length > 1 ? index + 1 : undefined
    };
    return res;
  }, {});
  return result;
});

/**
 * @category Sorting
 * @ignore - do not document.
 */
createSelectorMemoized(gridSortedRowIdsSelector, sortedIds => {
  return sortedIds.reduce((acc, id, index) => {
    acc[id] = index;
    return acc;
  }, Object.create(null));
});

/**
 * @category Filtering
 */
const gridFilterStateSelector = state => state.filter;

/**
 * Get the current filter model.
 * @category Filtering
 */
const gridFilterModelSelector = createSelector(gridFilterStateSelector, filterState => filterState.filterModel);

/**
 * Get the current quick filter values.
 * @category Filtering
 */
const gridQuickFilterValuesSelector = createSelector(gridFilterModelSelector, filterModel => filterModel.quickFilterValues);

/**
 * @category Visible rows
 * @ignore - do not document.
 */
const gridVisibleRowsLookupSelector = state => state.visibleRowsLookup;

/**
 * @category Filtering
 * @ignore - do not document.
 */
const gridFilteredRowsLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredRowsLookup);

/**
 * @category Filtering
 * @ignore - do not document.
 */
createSelector(gridFilterStateSelector, filterState => filterState.filteredChildrenCountLookup);

/**
 * @category Filtering
 * @ignore - do not document.
 */
createSelector(gridFilterStateSelector, filterState => filterState.filteredDescendantCountLookup);

/**
 * Get the id and the model of the rows accessible after the filtering process.
 * Does not contain the collapsed children.
 * @category Filtering
 */
const gridExpandedSortedRowEntriesSelector = createSelectorMemoized(gridVisibleRowsLookupSelector, gridSortedRowEntriesSelector, gridRowMaximumTreeDepthSelector, gridFilterModelSelector, gridQuickFilterValuesSelector, (visibleRowsLookup, sortedRows, maxDepth, filterModel, quickFilterValues) => {
  if (maxDepth < 2 && !filterModel.items.length && !quickFilterValues?.length) {
    return sortedRows;
  }
  return sortedRows.filter(row => visibleRowsLookup[row.id] !== false);
});

/**
 * Get the id of the rows accessible after the filtering process.
 * Does not contain the collapsed children.
 * @category Filtering
 */
const gridExpandedSortedRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, visibleSortedRowEntries => visibleSortedRowEntries.map(row => row.id));

/**
 * Get the id and the model of the rows accessible after the filtering process.
 * Contains the collapsed children.
 * @category Filtering
 */
const gridFilteredSortedRowEntriesSelector = createSelectorMemoized(gridFilteredRowsLookupSelector, gridSortedRowEntriesSelector, (filteredRowsLookup, sortedRows) => sortedRows.filter(row => filteredRowsLookup[row.id] !== false));

/**
 * Get the id of the rows accessible after the filtering process.
 * Contains the collapsed children.
 * @category Filtering
 */
createSelectorMemoized(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.map(row => row.id));

/**
 * Get the ids to position in the current tree level lookup of the rows accessible after the filtering process.
 * Does not contain the collapsed children.
 * @category Filtering
 * @ignore - do not document.
 */
createSelectorMemoized(gridExpandedSortedRowIdsSelector, gridRowTreeSelector, (visibleSortedRowIds, rowTree) => {
  const depthPositionCounter = {};
  let lastDepth = 0;
  return visibleSortedRowIds.reduce((acc, rowId) => {
    const rowNode = rowTree[rowId];
    if (!depthPositionCounter[rowNode.depth]) {
      depthPositionCounter[rowNode.depth] = 0;
    }

    // going deeper in the tree should reset the counter
    // since it might have been used in some other branch at the same level, up in the tree
    // going back up should keep the counter and continue where it left off
    if (rowNode.depth > lastDepth) {
      depthPositionCounter[rowNode.depth] = 0;
    }
    lastDepth = rowNode.depth;
    depthPositionCounter[rowNode.depth] += 1;
    acc[rowId] = depthPositionCounter[rowNode.depth];
    return acc;
  }, {});
});

/**
 * Get the id and the model of the top level rows accessible after the filtering process.
 * @category Filtering
 */
const gridFilteredSortedTopLevelRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, (visibleSortedRows, rowTree, rowTreeDepth) => {
  if (rowTreeDepth < 2) {
    return visibleSortedRows;
  }
  return visibleSortedRows.filter(row => rowTree[row.id]?.depth === 0);
});

/**
 * Get the amount of rows accessible after the filtering process.
 * @category Filtering
 */
createSelector(gridExpandedSortedRowEntriesSelector, visibleSortedRows => visibleSortedRows.length);

/**
 * Get the amount of top level rows accessible after the filtering process.
 * @category Filtering
 */
const gridFilteredTopLevelRowCountSelector = createSelector(gridFilteredSortedTopLevelRowEntriesSelector, visibleSortedTopLevelRows => visibleSortedTopLevelRows.length);

/**
 * Get the amount of rows accessible after the filtering process.
 * Includes top level and descendant rows.
 * @category Filtering
 */
const gridFilteredRowCountSelector = createSelector(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.length);

/**
 * Get the amount of descendant rows accessible after the filtering process.
 * @category Filtering
 */
createSelector(gridFilteredRowCountSelector, gridFilteredTopLevelRowCountSelector, (totalRowCount, topLevelRowCount) => totalRowCount - topLevelRowCount);

/**
 * @category Filtering
 * @ignore - do not document.
 */
const gridFilterActiveItemsSelector = createSelectorMemoized(gridFilterModelSelector, gridColumnLookupSelector, (filterModel, columnLookup) => filterModel.items?.filter(item => {
  if (!item.field) {
    return false;
  }
  const column = columnLookup[item.field];
  if (!column?.filterOperators || column?.filterOperators?.length === 0) {
    return false;
  }
  const filterOperator = column.filterOperators.find(operator => operator.value === item.operator);
  if (!filterOperator) {
    return false;
  }
  return !filterOperator.InputComponent || item.value != null && item.value?.toString() !== '';
}));
/**
 * @category Filtering
 * @ignore - do not document.
 */
createSelectorMemoized(gridFilterActiveItemsSelector, activeFilters => {
  const result = activeFilters.reduce((res, filterItem) => {
    if (!res[filterItem.field]) {
      res[filterItem.field] = [filterItem];
    } else {
      res[filterItem.field].push(filterItem);
    }
    return res;
  }, {});
  return result;
});

const gridRowSelectionStateSelector = state => state.rowSelection;
createSelector(gridRowSelectionStateSelector, selection => selection.length);
createSelectorMemoized(gridRowSelectionStateSelector, gridRowsLookupSelector, (selectedRows, rowsLookup) => new Map(selectedRows.map(id => [id, rowsLookup[id]])));
const selectedIdsLookupSelector = createSelectorMemoized(gridRowSelectionStateSelector, selection => selection.reduce((lookup, rowId) => {
  lookup[rowId] = rowId;
  return lookup;
}, {}));

// TODO v8: Use `createSelectorV8`
function getCheckboxPropsSelector(groupId, autoSelectParents) {
  return createSelector(gridRowTreeSelector, gridSortedRowIdsSelector, gridFilteredRowsLookupSelector, selectedIdsLookupSelector, (rowTree, sortedRowIds, filteredRowsLookup, rowSelectionLookup) => {
    const groupNode = rowTree[groupId];
    if (!groupNode || groupNode.type !== 'group') {
      return {
        isIndeterminate: false,
        isChecked: rowSelectionLookup[groupId] === groupId
      };
    }
    if (rowSelectionLookup[groupId] === groupId) {
      return {
        isIndeterminate: false,
        isChecked: true
      };
    }
    let selectableDescendantsCount = 0;
    let selectedDescendantsCount = 0;
    const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;
    for (let index = startIndex; index < sortedRowIds.length && rowTree[sortedRowIds[index]]?.depth > groupNode.depth; index += 1) {
      const id = sortedRowIds[index];
      if (filteredRowsLookup[id] !== false) {
        selectableDescendantsCount += 1;
        if (rowSelectionLookup[id] !== undefined) {
          selectedDescendantsCount += 1;
        }
      }
    }
    return {
      isIndeterminate: selectedDescendantsCount > 0 && (selectedDescendantsCount < selectableDescendantsCount || rowSelectionLookup[groupId] === undefined),
      isChecked: autoSelectParents ? selectedDescendantsCount > 0 : rowSelectionLookup[groupId] === groupId
    };
  });
}
function isMultipleRowSelectionEnabled(props) {
  if (props.signature === GridSignature.DataGrid) {
    // DataGrid Community has multiple row selection enabled only if checkbox selection is enabled.
    return props.checkboxSelection && props.disableMultipleRowSelection !== true;
  }
  return !props.disableMultipleRowSelection;
}

const _excluded$1 = ["field", "id", "formattedValue", "row", "rowNode", "colDef", "isEditable", "cellMode", "hasFocus", "tabIndex", "api"];
const useUtilityClasses$1 = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['checkboxInput']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
const GridCellCheckboxForwardRef = forwardRef(function GridCellCheckboxRenderer(props, ref) {
  const {
      field,
      id,
      rowNode,
      hasFocus,
      tabIndex
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded$1);
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses$1(ownerState);
  const checkboxElement = React.useRef(null);
  const rippleRef = React.useRef(null);
  const handleRef = useForkRef(checkboxElement, ref);
  const handleChange = event => {
    const params = {
      value: event.target.checked,
      id
    };
    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);
  };
  React.useLayoutEffect(() => {
    if (tabIndex === 0) {
      const element = apiRef.current.getCellElement(id, field);
      if (element) {
        element.tabIndex = -1;
      }
    }
  }, [apiRef, tabIndex, id, field]);
  React.useEffect(() => {
    if (hasFocus) {
      const input = checkboxElement.current?.querySelector('input');
      input?.focus({
        preventScroll: true
      });
    } else if (rippleRef.current) {
      // Only available in @mui/material v5.4.1 or later
      rippleRef.current.stop({});
    }
  }, [hasFocus]);
  const handleKeyDown = React.useCallback(event => {
    if (event.key === ' ') {
      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom
      // TODO: Remove and add a check inside useGridKeyboardNavigation
      event.stopPropagation();
    }
  }, []);
  const isSelectable = apiRef.current.isRowSelectable(id);
  const checkboxPropsSelector = getCheckboxPropsSelector(id, rootProps.rowSelectionPropagation?.parents ?? false);
  const {
    isIndeterminate,
    isChecked
  } = useGridSelector(apiRef, checkboxPropsSelector, objectShallowCompare);
  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {
    return null;
  }
  const checked = rootProps.indeterminateCheckboxAction === 'select' ? isChecked && !isIndeterminate : isChecked;
  const label = apiRef.current.getLocaleText(checked ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');
  return /*#__PURE__*/jsx(rootProps.slots.baseCheckbox, _extends({
    tabIndex: tabIndex,
    checked: checked,
    onChange: handleChange,
    className: classes.root,
    inputProps: {
      'aria-label': label,
      name: 'select_row'
    },
    onKeyDown: handleKeyDown,
    indeterminate: isIndeterminate,
    disabled: !isSelectable,
    touchRippleRef: rippleRef /* FIXME: typing error */
  }, rootProps.slotProps?.baseCheckbox, other, {
    ref: handleRef
  }));
});
process.env.NODE_ENV !== "production" ? GridCellCheckboxForwardRef.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * GridApi that let you manipulate the grid.
   */
  api: PropTypes.object.isRequired,
  /**
   * The mode of the cell.
   */
  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,
  /**
   * The column of the row that the current cell belongs to.
   */
  colDef: PropTypes.object.isRequired,
  /**
   * The column field of the cell that triggered the event.
   */
  field: PropTypes.string.isRequired,
  /**
   * A ref allowing to set imperative focus.
   * It can be passed to the element that should receive focus.
   * @ignore - do not document.
   */
  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
    current: PropTypes.shape({
      focus: PropTypes.func.isRequired
    })
  })]),
  /**
   * The cell value formatted with the column valueFormatter.
   */
  formattedValue: PropTypes.any,
  /**
   * If true, the cell is the active element.
   */
  hasFocus: PropTypes.bool.isRequired,
  /**
   * The grid row id.
   */
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  /**
   * If true, the cell is editable.
   */
  isEditable: PropTypes.bool,
  /**
   * The row model of the row that the current cell belongs to.
   */
  row: PropTypes.any.isRequired,
  /**
   * The node of the row that the current cell belongs to.
   */
  rowNode: PropTypes.object.isRequired,
  /**
   * the tabIndex value.
   */
  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,
  /**
   * The cell value.
   * If the column has `valueGetter`, use `params.row` to directly access the fields.
   */
  value: PropTypes.any
} : undefined;
const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;

const gridFocusStateSelector = state => state.focus;
createSelector(gridFocusStateSelector, focusState => focusState.cell);
createSelector(gridFocusStateSelector, focusState => focusState.columnHeader);
createSelector(gridFocusStateSelector, focusState => focusState.columnHeaderFilter);
createSelector(gridFocusStateSelector, focusState => focusState.columnGroupHeader);
const gridTabIndexStateSelector = state => state.tabIndex;
createSelector(gridTabIndexStateSelector, state => state.cell);
const gridTabIndexColumnHeaderSelector = createSelector(gridTabIndexStateSelector, state => state.columnHeader);
createSelector(gridTabIndexStateSelector, state => state.columnHeaderFilter);
createSelector(gridTabIndexStateSelector, state => state.columnGroupHeader);

const getPageCount = (rowCount, pageSize, page) => {
  if (pageSize > 0 && rowCount > 0) {
    return Math.ceil(rowCount / pageSize);
  }
  if (rowCount === -1) {
    // With unknown row-count, we can assume a page after the current one
    return page + 2;
  }
  return 0;
};

const ALL_RESULTS_PAGE_VALUE = -1;

/**
 * @category Pagination
 * @ignore - do not document.
 */
const gridPaginationSelector = state => state.pagination;

/**
 * @category Pagination
 * @ignore - do not document.
 */
const gridPaginationEnabledClientSideSelector = createSelector(gridPaginationSelector, pagination => pagination.enabled && pagination.paginationMode === 'client');

/**
 * Get the pagination model
 * @category Pagination
 */
const gridPaginationModelSelector = createSelector(gridPaginationSelector, pagination => pagination.paginationModel);

/**
 * Get the row count
 * @category Pagination
 */
const gridPaginationRowCountSelector = createSelector(gridPaginationSelector, pagination => pagination.rowCount);

/**
 * Get the pagination meta
 * @category Pagination
 */
createSelector(gridPaginationSelector, pagination => pagination.meta);

/**
 * Get the index of the page to render if the pagination is enabled
 * @category Pagination
 */
createSelector(gridPaginationModelSelector, paginationModel => paginationModel.page);

/**
 * Get the maximum amount of rows to display on a single page if the pagination is enabled
 * @category Pagination
 */
createSelector(gridPaginationModelSelector, paginationModel => paginationModel.pageSize);

/**
 * Get the amount of pages needed to display all the rows if the pagination is enabled
 * @category Pagination
 */
createSelector(gridPaginationModelSelector, gridPaginationRowCountSelector, (paginationModel, rowCount) => getPageCount(rowCount, paginationModel.pageSize, paginationModel.page));

/**
 * Get the index of the first and the last row to include in the current page if the pagination is enabled.
 * @category Pagination
 */
const gridPaginationRowRangeSelector = createSelectorMemoized(gridPaginationEnabledClientSideSelector, gridPaginationModelSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, gridExpandedSortedRowEntriesSelector, gridFilteredSortedTopLevelRowEntriesSelector, (clientSidePaginationEnabled, paginationModel, rowTree, rowTreeDepth, visibleSortedRowEntries, visibleSortedTopLevelRowEntries) => {
  if (!clientSidePaginationEnabled) {
    return null;
  }
  const visibleTopLevelRowCount = visibleSortedTopLevelRowEntries.length;
  const topLevelFirstRowIndex = Math.min(paginationModel.pageSize * paginationModel.page, visibleTopLevelRowCount - 1);
  const topLevelLastRowIndex = paginationModel.pageSize === ALL_RESULTS_PAGE_VALUE ? visibleTopLevelRowCount - 1 : Math.min(topLevelFirstRowIndex + paginationModel.pageSize - 1, visibleTopLevelRowCount - 1);

  // The range contains no element
  if (topLevelFirstRowIndex === -1 || topLevelLastRowIndex === -1) {
    return null;
  }

  // The tree is flat, there is no need to look for children
  if (rowTreeDepth < 2) {
    return {
      firstRowIndex: topLevelFirstRowIndex,
      lastRowIndex: topLevelLastRowIndex
    };
  }
  const topLevelFirstRow = visibleSortedTopLevelRowEntries[topLevelFirstRowIndex];
  const topLevelRowsInCurrentPageCount = topLevelLastRowIndex - topLevelFirstRowIndex + 1;
  const firstRowIndex = visibleSortedRowEntries.findIndex(row => row.id === topLevelFirstRow.id);
  let lastRowIndex = firstRowIndex;
  let topLevelRowAdded = 0;
  while (lastRowIndex < visibleSortedRowEntries.length && topLevelRowAdded <= topLevelRowsInCurrentPageCount) {
    const row = visibleSortedRowEntries[lastRowIndex];
    const depth = rowTree[row.id]?.depth;
    if (depth === undefined) {
      lastRowIndex += 1;
    } else {
      if (topLevelRowAdded < topLevelRowsInCurrentPageCount || depth > 0) {
        lastRowIndex += 1;
      }
      if (depth === 0) {
        topLevelRowAdded += 1;
      }
    }
  }
  return {
    firstRowIndex,
    lastRowIndex: lastRowIndex - 1
  };
});

/**
 * Get the id and the model of each row to include in the current page if the pagination is enabled.
 * @category Pagination
 */
const gridPaginatedVisibleSortedGridRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridPaginationRowRangeSelector, (visibleSortedRowEntries, paginationRange) => {
  if (!paginationRange) {
    return [];
  }
  return visibleSortedRowEntries.slice(paginationRange.firstRowIndex, paginationRange.lastRowIndex + 1);
});

/**
 * Get the id of each row to include in the current page if the pagination is enabled.
 * @category Pagination
 */
const gridPaginatedVisibleSortedGridRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowIdsSelector, gridPaginationRowRangeSelector, (visibleSortedRowIds, paginationRange) => {
  if (!paginationRange) {
    return [];
  }
  return visibleSortedRowIds.slice(paginationRange.firstRowIndex, paginationRange.lastRowIndex + 1);
});

/**
 * Get the rows, range and rowIndex lookup map after filtering and sorting.
 * Does not contain the collapsed children.
 * @category Pagination
 */
createSelectorMemoized(gridPaginationEnabledClientSideSelector, gridPaginationRowRangeSelector, gridPaginatedVisibleSortedGridRowEntriesSelector, gridExpandedSortedRowEntriesSelector, (clientPaginationEnabled, paginationRowRange, paginationRows, expandedSortedRowEntries) => {
  if (clientPaginationEnabled) {
    return {
      rows: paginationRows,
      range: paginationRowRange,
      rowToIndexMap: paginationRows.reduce((lookup, row, index) => {
        lookup.set(row.model, index);
        return lookup;
      }, new Map())
    };
  }
  return {
    rows: expandedSortedRowEntries,
    range: expandedSortedRowEntries.length === 0 ? null : {
      firstRowIndex: 0,
      lastRowIndex: expandedSortedRowEntries.length - 1
    },
    rowToIndexMap: expandedSortedRowEntries.reduce((lookup, row, index) => {
      lookup.set(row.model, index);
      return lookup;
    }, new Map())
  };
});

const _excluded = ["field", "colDef"];
const useUtilityClasses = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['checkboxInput']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
const GridHeaderCheckbox = forwardRef(function GridHeaderCheckbox(props, ref) {
  const other = _objectWithoutPropertiesLoose(props, _excluded);
  const [, forceUpdate] = React.useState(false);
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps();
  const ownerState = {
    classes: rootProps.classes
  };
  const classes = useUtilityClasses(ownerState);
  const tabIndexState = useGridSelector(apiRef, gridTabIndexColumnHeaderSelector);
  const selection = useGridSelector(apiRef, gridRowSelectionStateSelector);
  const visibleRowIds = useGridSelector(apiRef, gridExpandedSortedRowIdsSelector);
  const paginatedVisibleRowIds = useGridSelector(apiRef, gridPaginatedVisibleSortedGridRowIdsSelector);
  const filteredSelection = React.useMemo(() => {
    if (typeof rootProps.isRowSelectable !== 'function') {
      return selection;
    }
    return selection.filter(id => {
      if (rootProps.keepNonExistentRowsSelected) {
        return true;
      }
      // The row might have been deleted
      if (!apiRef.current.getRow(id)) {
        return false;
      }
      return rootProps.isRowSelectable(apiRef.current.getRowParams(id));
    });
  }, [apiRef, rootProps.isRowSelectable, selection, rootProps.keepNonExistentRowsSelected]);

  // All the rows that could be selected / unselected by toggling this checkbox
  const selectionCandidates = React.useMemo(() => {
    const rowIds = !rootProps.pagination || !rootProps.checkboxSelectionVisibleOnly || rootProps.paginationMode === 'server' ? visibleRowIds : paginatedVisibleRowIds;

    // Convert to an object to make O(1) checking if a row exists or not
    // TODO create selector that returns visibleRowIds/paginatedVisibleRowIds as an object
    return rowIds.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {});
  }, [rootProps.pagination, rootProps.paginationMode, rootProps.checkboxSelectionVisibleOnly, paginatedVisibleRowIds, visibleRowIds]);

  // Amount of rows selected and that are visible in the current page
  const currentSelectionSize = React.useMemo(() => filteredSelection.filter(id => selectionCandidates[id]).length, [filteredSelection, selectionCandidates]);
  const isIndeterminate = currentSelectionSize > 0 && currentSelectionSize < Object.keys(selectionCandidates).length;
  const isChecked = currentSelectionSize > 0;
  const handleChange = event => {
    const params = {
      value: event.target.checked
    };
    apiRef.current.publishEvent('headerSelectionCheckboxChange', params);
  };
  const tabIndex = tabIndexState !== null && tabIndexState.field === props.field ? 0 : -1;
  React.useLayoutEffect(() => {
    const element = apiRef.current.getColumnHeaderElement(props.field);
    if (tabIndex === 0 && element) {
      element.tabIndex = -1;
    }
  }, [tabIndex, apiRef, props.field]);
  const handleKeyDown = React.useCallback(event => {
    if (event.key === ' ') {
      // imperative toggle the checkbox because Space is disable by some preventDefault
      apiRef.current.publishEvent('headerSelectionCheckboxChange', {
        value: !isChecked
      });
    }
  }, [apiRef, isChecked]);
  const handleSelectionChange = React.useCallback(() => {
    forceUpdate(p => !p);
  }, []);
  React.useEffect(() => {
    return apiRef.current.subscribeEvent('rowSelectionChange', handleSelectionChange);
  }, [apiRef, handleSelectionChange]);
  const checked = rootProps.indeterminateCheckboxAction === 'select' ? isChecked && !isIndeterminate : isChecked;
  const label = apiRef.current.getLocaleText(checked ? 'checkboxSelectionUnselectAllRows' : 'checkboxSelectionSelectAllRows');
  return /*#__PURE__*/jsx(rootProps.slots.baseCheckbox, _extends({
    indeterminate: isIndeterminate,
    checked: checked,
    onChange: handleChange,
    className: classes.root,
    inputProps: {
      'aria-label': label,
      name: 'select_all_rows'
    },
    tabIndex: tabIndex,
    onKeyDown: handleKeyDown,
    disabled: !isMultipleRowSelectionEnabled(rootProps)
  }, rootProps.slotProps?.baseCheckbox, other, {
    ref: ref
  }));
});
process.env.NODE_ENV !== "production" ? GridHeaderCheckbox.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The column of the current header component.
   */
  colDef: PropTypes.object.isRequired,
  /**
   * The column field of the column that triggered the event
   */
  field: PropTypes.string.isRequired
} : undefined;

/**
 * Get the row id for a given row
 * @param state - The grid state
 * @param {GridRowModel} row - The row to get the id for
 * @returns {GridRowId} The row id
 */
const gridRowIdSelector = (state, row) => {
  if (GRID_ID_AUTOGENERATED in row) {
    return row[GRID_ID_AUTOGENERATED];
  }
  return state.props.getRowId ? state.props.getRowId(row) : row.id;
};

const GRID_CHECKBOX_SELECTION_FIELD = '__check__';
_extends({}, GRID_BOOLEAN_COL_DEF, {
  type: 'custom',
  field: GRID_CHECKBOX_SELECTION_FIELD,
  width: 50,
  resizable: false,
  sortable: false,
  filterable: false,
  // @ts-ignore
  aggregable: false,
  disableColumnMenu: true,
  disableReorder: true,
  disableExport: true,
  getApplyQuickFilterFn: undefined,
  display: 'flex',
  valueGetter: (value, row, column, apiRef) => {
    const selectionLookup = selectedIdsLookupSelector(apiRef);
    const rowId = gridRowIdSelector(apiRef.current.state, row);
    return selectionLookup[rowId] !== undefined;
  },
  renderHeader: params => /*#__PURE__*/jsx(GridHeaderCheckbox, _extends({}, params)),
  renderCell: params => /*#__PURE__*/jsx(GridCellCheckboxRenderer, _extends({}, params))
});

async function updateSavedInitialConfigOnStorage({ storageAccessors }, { currentGridState }) {
    try {
        const { columns, pinnedColumns, sorting, density } = currentGridState;
        const stateToSave = {
            version: 3,
            dataGridInitialStateToSave: {
                columns,
                pinnedColumns,
                sorting,
                density,
            },
        };
        await storageAccessors.update(() => stateToSave);
    }
    catch {
        return 'SET_ITEM_FAILED';
    }
    return undefined;
}
function DataGridWithSavedSettings({ storageDataGridId: _storageDataGridId, storageAccessors, initialStateFromStorage, apiRef, Component, remainingInitialState, ...restProps }) {
    const _gridApiRef = useGridApiRef();
    const gridApiRef = apiRef ?? _gridApiRef;
    useEffect(() => {
        // Update the storage with the latest GridInitialStatePro format since the storage might still be on an older version
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            currentGridState: gridApiRef.current.exportState(),
        });
    }, [gridApiRef, storageAccessors]);
    const handleColumnVisibilityChange = (model, details) => {
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            currentGridState: gridApiRef.current.exportState(),
        });
        restProps.onColumnVisibilityModelChange?.(model, details);
    };
    const handlePinnedColumnsChange = (pinnedColumns, details) => {
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            currentGridState: gridApiRef.current.exportState(),
        });
        restProps.onPinnedColumnsChange?.(pinnedColumns, details);
    };
    const handleColumnOrderChange = (...args) => {
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            currentGridState: gridApiRef.current.exportState(),
        });
        restProps.onColumnOrderChange?.(...args);
    };
    const handleSortModelChange = (model, details) => {
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            currentGridState: gridApiRef.current.exportState(),
        });
        restProps.onSortModelChange?.(model, details);
    };
    const handleDensityChange = (density) => {
        updateSavedInitialConfigOnStorage({ storageAccessors }, {
            // There is a bug currently in "@mui/x-data-grid": "7.0.0", that causes exportState to not return "density"
            // As a workaround, we do it ourselves
            currentGridState: { ...gridApiRef.current.exportState(), density },
        });
        restProps.onDensityChange?.(density);
    };
    const initialColumnsState = initialStateFromStorage?.columns == null
        ? initialStateFromStorage?.columns
        : R.omit(initialStateFromStorage.columns, ['dimensions']);
    const initialState = {
        ...remainingInitialState,
        ...initialStateFromStorage,
        columns: {
            ...initialColumnsState,
            columnVisibilityModel: initialStateFromStorage?.columns?.columnVisibilityModel ??
                /* Taken from docs https://mui.com/x/react-data-grid/state/#save-and-restore-the-state
        
                "⚠️ To avoid breaking changes, the grid only saves/exports the column visibility if you are using the new api. Make sure to initialize props.initialState.columns.columnVisibilityModel or to control props.columnVisibilityModel.
                  The easier way is to initialize the model with an empty object"
                 */
                {},
        },
    };
    return (jsx(Component, { ...restProps, apiRef: gridApiRef, onColumnVisibilityModelChange: handleColumnVisibilityChange, onColumnOrderChange: handleColumnOrderChange, onPinnedColumnsChange: handlePinnedColumnsChange, onSortModelChange: handleSortModelChange, onDensityChange: handleDensityChange, initialState: initialState }));
}

const getIDBDataGridStoreMethods = (key, store) => ({
    get() {
        return get(key, store);
    },
    update(updater) {
        return update(key, updater, store);
    },
});

// Important to update!
const LATEST_VALID_VERSION = 3;
const v1ToV2 = ({ version: _, ...v1Config }) => {
    return {
        version: 2,
        ...v1Config,
    };
};
const v2ToV3 = (v2Config) => {
    return {
        dataGridInitialStateToSave: {
            ...v2Config.dataGridInitialStateToSave,
            density: v2Config.density, // density moved to DataGrid state on v3. No longer on "root" level
        },
        version: 3,
    };
};
const migrateIfNeeded = (storeData) => {
    let migratedConfig = storeData;
    // Probably the only valid use case for a fallthrough case!
    switch (migratedConfig.version) {
        // @ts-expect-error ___
        case 1: {
            migratedConfig = v1ToV2(migratedConfig);
        }
        case 2: {
            migratedConfig = v2ToV3(migratedConfig);
        }
    }
    if (LATEST_VALID_VERSION !== migratedConfig.version) {
        throw new Error(`Invalid version`);
    }
    return migratedConfig;
};

function DataGridWithSavedSettingsOnIDB({ dataGridId, idbStore, initialState, ...restProps }) {
    const storageDataGridId = dataGridId;
    const storageAccessors = useMemo(() => getIDBDataGridStoreMethods(storageDataGridId, idbStore), [idbStore, storageDataGridId]);
    const [initialDataGridConfigFromStorage, setInitialDataGridConfigFromStorage] = useState('LOADING');
    useEffect(() => {
        async function getInitialConfigFromStorage() {
            const defaultConfig = {
                dataGridInitialStateToSave: initialState,
            };
            try {
                const storeState = await storageAccessors.get();
                if (storeState === undefined) {
                    throw new Error('No store state on indexedDB yet');
                }
                return migrateIfNeeded(storeState);
            }
            catch {
                return defaultConfig;
            }
        }
        async function setInitialConfig() {
            setInitialDataGridConfigFromStorage(await getInitialConfigFromStorage());
        }
        setInitialConfig();
        // We don't want to react to initialState because it is not updated
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [storageAccessors]);
    const initialStateFromStorage = useMemo(() => {
        if (initialDataGridConfigFromStorage === 'LOADING') {
            return 'LOADING';
        }
        if (initialDataGridConfigFromStorage.dataGridInitialStateToSave === undefined) {
            return initialState;
        }
        const initialStateFromStorage = initialDataGridConfigFromStorage.dataGridInitialStateToSave;
        const columnsFieldsFromProps = restProps.columns.map((c) => c.field);
        // If the table columns changes such as column renaming, addition, or deletion
        // we retrieve the values from the initial state to prevent the columns from becoming mismatched.
        // If the table has checkbox selection, tree data, or other selection fields while it isn't in the columns list, add them manually
        const selectionFieldsColumns = [];
        if (restProps.checkboxSelection) {
            selectionFieldsColumns.push(GRID_CHECKBOX_SELECTION_FIELD);
        }
        if (restProps.treeData) {
            selectionFieldsColumns.push(GRID_TREE_DATA_GROUPING_FIELD);
        }
        // The dev might have added selection fields manually (in case they want to style the checkbox column differently, for instance), so we need to make sure to remove duplicates
        const columnsFieldsWithSelectionFields = R.unique([
            ...columnsFieldsFromProps,
            ...selectionFieldsColumns,
        ]);
        if ([...(initialStateFromStorage.columns?.orderedFields ?? [])].sort().join(',') !==
            columnsFieldsWithSelectionFields.sort().join(',')) {
            return initialState;
        }
        const columns = {
            ...initialStateFromStorage.columns, // other properties are not relevant to merge right now
            orderedFields: getOrderedFieldsWithNewlyAddedColumns({
                columns: restProps.columns,
                orderedFieldsFromIDB: initialStateFromStorage.columns?.orderedFields,
                orderedFieldsFromInitialState: initialState?.columns?.orderedFields,
            }),
            columnVisibilityModel: {
                ...initialState?.columns?.columnVisibilityModel,
                ...initialStateFromStorage.columns?.columnVisibilityModel,
            },
        };
        const pinnedColumns = {
            left: (initialStateFromStorage.pinnedColumns?.left?.length ?? 0) > 0
                ? initialStateFromStorage.pinnedColumns?.left
                : initialState?.pinnedColumns?.left,
            right: (initialStateFromStorage.pinnedColumns?.right?.length ?? 0) > 0
                ? initialStateFromStorage.pinnedColumns?.right
                : initialState?.pinnedColumns?.right,
        };
        const sorting = {
            sortModel: (initialStateFromStorage.sorting?.sortModel?.length ?? 0) > 0
                ? initialStateFromStorage.sorting?.sortModel
                : initialState?.sorting?.sortModel,
        };
        const density = initialDataGridConfigFromStorage.dataGridInitialStateToSave.density ??
            initialState?.density;
        return { columns, pinnedColumns, sorting, density };
    }, [
        initialDataGridConfigFromStorage,
        initialState,
        restProps.checkboxSelection,
        restProps.columns,
    ]);
    const remainingInitialState = useMemo(() => {
        return initialState
            ? R.omit(initialState, arrayOfAllUnionTypes()([
                'columns',
                'pinnedColumns',
                'sorting',
                'density',
            ]))
            : initialState;
        // We don't want to react to initialState because it is not updated
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    if (initialDataGridConfigFromStorage === 'LOADING' ||
        initialStateFromStorage === 'LOADING') {
        return null;
    }
    return (jsx(DataGridWithSavedSettings, { storageDataGridId: storageDataGridId, storageAccessors: storageAccessors, initialStateFromStorage: initialStateFromStorage, remainingInitialState: remainingInitialState, ...restProps }));
}
const getOrderedFieldsWithNewlyAddedColumns = ({ columns, orderedFieldsFromIDB, orderedFieldsFromInitialState, }) => {
    if (orderedFieldsFromIDB === undefined || orderedFieldsFromIDB.length === 0) {
        return orderedFieldsFromInitialState;
    }
    if (columns.length === 0) {
        return orderedFieldsFromIDB;
    }
    const columnFields = columns.map((col) => col.field);
    const addedColumns = R.difference(columnFields, orderedFieldsFromIDB);
    const newOrderedFieldsFromIDB = [...orderedFieldsFromIDB];
    // If there are new columns added to the grid, add them to the orderedFieldsFromIDB at the same position as the columnFields.
    // If the position is not found, add them at the end of the array (splice handles this out of the box)
    if (addedColumns.length > 0) {
        for (const addedColumn of addedColumns) {
            const addedColumnPosition = columnFields.indexOf(addedColumn);
            newOrderedFieldsFromIDB.splice(addedColumnPosition, 0, addedColumn);
        }
    }
    return newOrderedFieldsFromIDB;
};

const DateField = forwardRefTyped(function DateField({ size = 'small', ...props }, ref) {
    return (jsx(DateField$1, { size: size, ref: ref, ...props }));
});

const DatePicker = forwardRefTyped(function DatePicker({ slotProps, readOnly: readOnlyProp, disabled: disabledProp, ...props }, ref) {
    const formState = useKarooFormStateContext();
    const readOnly = readOnlyProp ?? formState?.readOnly;
    const disabled = disabledProp ?? formState?.disabled;
    return (jsx(DatePicker$2, { ref: ref, readOnly: readOnly, disabled: disabled, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const DateRangeCalendar = forwardRefTyped(function DateRangeCalendar({ 
/** Our users prefer this behaviour and is less confusing */
disableAutoMonthSwitching = true, ...props }, ref) {
    return (jsx(DateRangeCalendar$1, { ref: ref, disableAutoMonthSwitching: disableAutoMonthSwitching, ...props }));
});

const DateRangePicker = forwardRefTyped(function DateRangePicker({ slotProps, 
/** Our users prefer this behaviour and is less confusing */
disableAutoMonthSwitching = true, ...props }, ref) {
    const fieldSeparatorProps = slotProps?.fieldSeparator;
    const fieldSeparatorSx = fieldSeparatorProps?.sx ?? [];
    const textFieldProps = slotProps?.textField;
    const textFieldSx = textFieldProps?.sx ?? [];
    return (jsx(DateRangePicker$2, { ref: ref, slotProps: {
            ...slotProps,
            fieldSeparator: {
                ...fieldSeparatorProps,
                sx: [
                    { marginLeft: '12px !important', marginRight: '12px !important' },
                    ...(R.isArray(fieldSeparatorSx) ? fieldSeparatorSx : [fieldSeparatorSx]),
                ],
            },
            textField: {
                size: 'small',
                ...textFieldProps,
                sx: [
                    { ml: '0px !important' },
                    ...(R.isArray(textFieldSx) ? textFieldSx : [textFieldSx]),
                ],
            },
        }, disableAutoMonthSwitching: disableAutoMonthSwitching, ...props }));
});
function isNonEmptyDateRange(dateRange) {
    return dateRange[0] != null && dateRange[1] != null;
}

const DateTimeField = forwardRefTyped(function DateTimeField({ slotProps, ...props }, ref) {
    return (jsx(DateTimeField$1, { ref: ref, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const DateTimePicker = forwardRefTyped(function DateTimePicker({ slotProps, timeSteps = { hours: 1, minutes: 1, seconds: 1 }, readOnly: readOnlyProp, disabled: disabledProp, ...props }, ref) {
    const formState = useKarooFormStateContext();
    const readOnly = readOnlyProp ?? formState?.readOnly;
    const disabled = disabledProp ?? formState?.disabled;
    return (jsx(DateTimePicker$2, { ref: ref, readOnly: readOnly, disabled: disabled, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, timeSteps: timeSteps, ...props }));
});

const DateTimeRangePicker = forwardRefTyped(function DateTimeRangePicker({ slotProps, 
/** Our users prefer this behaviour and is less confusing */
disableAutoMonthSwitching = true, timeSteps = { hours: 1, minutes: 1, seconds: 1 }, ...props }, ref) {
    const fieldSeparatorProps = slotProps?.fieldSeparator;
    const fieldSeparatorSx = fieldSeparatorProps?.sx ?? [];
    const textFieldProps = slotProps?.textField;
    const textFieldSx = textFieldProps?.sx ?? [];
    return (jsx(DateTimeRangePicker$1, { ref: ref, slotProps: {
            ...slotProps,
            fieldSeparator: {
                ...fieldSeparatorProps,
                sx: [
                    { marginLeft: '12px !important', marginRight: '12px !important' },
                    ...(R.isArray(fieldSeparatorSx) ? fieldSeparatorSx : [fieldSeparatorSx]),
                ],
            },
            textField: {
                size: 'small',
                ...textFieldProps,
                sx: [
                    { ml: '0px !important' },
                    ...(R.isArray(textFieldSx) ? textFieldSx : [textFieldSx]),
                ],
            },
        }, disableAutoMonthSwitching: disableAutoMonthSwitching, timeSteps: timeSteps, ...props }));
});

const Dialog = forwardRef$1(function Dialog({ TransitionComponent = DefaultTransition, ...props }, ref) {
    return (jsx(MuiDialog, { ref: ref, TransitionComponent: TransitionComponent, ...props }));
});
const DefaultTransition = forwardRefTyped(function Transition(props, ref) {
    return (jsx(Fade__default, { in: true, ref: ref, ...props }));
});

const Drawer = forwardRef$1(function Drawer({ SlideProps, ...props }, ref) {
    return (jsx(MuiDrawer, { ref: ref, SlideProps: {
            appear: true,
            ...SlideProps,
        }, ...props }));
});

const FormControlLabel = forwardRef$1(function FormControlLabel({ readOnly: readOnlyProp, disabled: disabledProp, sx = [], ...props }, ref) {
    const formState = useKarooFormStateContext();
    const readOnly = readOnlyProp ?? formState?.readOnly;
    const disabled = disabledProp ?? formState?.disabled;
    return (jsx(FormControlLabel__default, { ref: ref, ...props, disabled: disabled, sx: [
            ...(Array.isArray(sx) ? sx : [sx]),
            () => ({
                pointerEvents: readOnly ? 'none' : 'auto',
            }),
        ] }));
});

// Taken from figma
const blueLight200 = '#81D4FA';
const lightBlueLinkSx = {
    color: blueLight200,
    textDecorationColor: alpha(blueLight200, 0.55),
};

const MultiInputDateTimeRangeField = forwardRefTyped(function MultiInputDateTimeRangeField({ slotProps, ...props }, ref) {
    return (jsx(MultiInputDateTimeRangeField$1, { ref: ref, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const MultiInputTimeRangeField = forwardRefTyped(function MultiInputTimeRangeField({ slotProps, ...props }, ref) {
    return (jsx(MultiInputTimeRangeField$1, { ref: ref, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const PasswordField = forwardRef$1(function PasswordField({ showPassword, slotProps, ...props }, ref) {
    const [displayPassword, setDisplayPassword] = useState(showPassword);
    // Override state from showPassword prop
    useEffect(() => {
        setDisplayPassword(showPassword);
    }, [showPassword]);
    const slotPropsInput = slotProps?.input;
    return (jsx(TextField, { ref: ref, type: displayPassword ? 'text' : 'password', slotProps: {
            ...slotProps,
            input: {
                ...slotPropsInput,
                endAdornment: slotPropsInput?.endAdornment !== undefined ? (slotPropsInput.endAdornment) : (jsx(IconButton__default, { edge: "end", onClick: () => setDisplayPassword((prev) => !prev), children: displayPassword ? jsx(VisibilityOff, {}) : jsx(Visibility, {}) })),
            },
        }, ...props }));
});

const Select = forwardRefTyped(function Select({ disabled: disabledProp, readOnly: readOnlyProp, ...props }, ref) {
    const formState = useKarooFormStateContext();
    const disabled = disabledProp ?? formState?.disabled;
    const readOnly = readOnlyProp ?? formState?.readOnly;
    return (jsx(MUISelect__default, { ref: ref, disabled: disabled, readOnly: readOnly, ...props }));
});

const SingleInputDateTimeRangeField = forwardRefTyped(function SingleInputDateTimeRangeField({ slotProps, ...props }, ref) {
    return (jsx(SingleInputDateTimeRangeField$1, { ref: ref, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const SingleInputTimeRangeField = forwardRefTyped(function SingleInputTimeRangeField({ slotProps, ...props }, ref) {
    return (jsx(SingleInputTimeRangeField$1, { ref: ref, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const TimeField = forwardRefTyped(function TimeField({ slotProps, disabled: disabledProp, readOnly: readOnlyProp, ...props }, ref) {
    const formState = useKarooFormStateContext();
    const disabled = disabledProp ?? formState?.disabled;
    const readOnly = readOnlyProp ?? formState?.readOnly;
    return (jsx(TimeField$1, { ref: ref, disabled: disabled, readOnly: readOnly, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, ...props }));
});

const TimePicker = forwardRefTyped(function TimePicker({ slotProps, disabled: disabledProp, readOnly: readOnlyProp, ...props }, ref) {
    const formState = useKarooFormStateContext();
    const disabled = disabledProp ?? formState?.disabled;
    const readOnly = readOnlyProp ?? formState?.readOnly;
    return (jsx(TimePicker$1, { ref: ref, disabled: disabled, readOnly: readOnly, slotProps: {
            ...slotProps,
            textField: { size: 'small', ...slotProps?.textField },
        }, timeSteps: {
            hours: 1,
            minutes: 1,
            seconds: 1,
        }, ...props }));
});

const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
});

/**
 * This hook works like `useCallback` but can be used to enforce that a dev uses a stable callback for certain props.
 *
 */
// Just like useCallback, it's important to extend from __Function__ to avoid implicit any.
// See https://github.com/DefinitelyTyped/DefinitelyTyped/issues/52873#issuecomment-845806435 for a comparison between `Function` and more specific types.
// eslint-disable-next-line
// eslint-disable-next-line ban-types, no-unsafe-function-type
function useCallbackBranded(callback, deps) {
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useCallback(callback, deps);
}

/**
 * This hook works like `useMemo` but can be used to enforce that a dev uses a stable value for certain props.
 *
 */
function useMemoBranded(factory, deps) {
    return useMemo(factory, deps);
}

export { Autocomplete, BaseGridToolbarContainer, BaseGridToolbarContainerWithItems, Button, CircularProgressDelayed, CircularProgressDelayedAbsolute, CircularProgressDelayedCentered, ContainerWithTabsForDataGrid, DATAGRID_DATETIME_COLUMN_WIDTH, DataGrid, DataGridAsTabItem, DataGridBase, DataGridWithSavedSettingsOnIDB, DateField, DatePicker, DateRangeCalendar, DateRangePicker, DateTimeField, DateTimePicker, DateTimeRangePicker, Dialog, Drawer, FormControlLabel, GRID_CHECKBOX_SELECTION_COL_DEF, GridFilterDateRangeInput, GridToolbarColumnsButton, GridToolbarDensitySelector, GridToolbarExport, GridToolbarFilterButton, GridToolbarSearchButtonTextField, GridToolbarStandard, GridToolbarStandardOld, GridToolbarWithQuickFilter, KarooFormStateContextProvider, MultiInputDateTimeRangeField, MultiInputTimeRangeField, OverflowTypography, OverflowableElementHeadless, PasswordField, SearchTextField, Select, SingleInputDateTimeRangeField, SingleInputTimeRangeField, TextField, ThemeProvider, TimeField, TimePicker, ToolbarStandardContent, Tooltip, VisuallyHiddenInput, createDataGridBaseColumn, createDataGridColumnHelper, defaultCustomizableTheme, forwardRefTyped, getGridBooleanOperators$1 as getGridBooleanOperators, getGridNumericOperators, getGridSingleSelectOperators, getGridStringOperators$1 as getGridStringOperators, isNonEmptyDateRange, keyframes, lightBlueLinkSx, locale_arSD, locale_bgBG, locale_csCZ, locale_deDE, locale_elGR, locale_enNZ, locale_enUS, locale_esES, locale_faIR, locale_fiFI, locale_frFR, locale_heIL, locale_idID, locale_itIT, locale_jaJP, locale_khKH, locale_koKR, locale_msMS, locale_nlNL, locale_plPL, locale_ptPT, locale_ruRU, locale_skSK, locale_thTH, locale_trTR, locale_ukUA, locale_viVN, locale_zhCN, locale_zhHK, mergeSxValues, styled, useCallbackBranded, useDataGridColumnHelper, useDataGridDateColumns, useKarooFormStateContext, useMemoBranded, useOverflowableElement, usePickersAdapterContextUtils, useSearchTextField };
