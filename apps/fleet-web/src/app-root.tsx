/* NOTE no-unsanitized/method is not very useful on this file because of dynamic imports with variables. */
/* eslint-disable no-unsanitized/method  */
import 'assets/fonts/style.css'
import './styles/index.scss'
import { PureComponent } from 'react'
import { loadableWithSpinnerAndRetry } from 'src/util-functions/loadable'
import { connect } from 'react-redux'
import { Route, Switch, Redirect, withRouter, useLocation } from 'react-router'
import { IntlProvider } from 'react-intl'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.min.css'
import { getInternalLocalization } from 'src/modules/delivery/utils/helpers'
import { match } from 'ts-pattern'

import styled from 'styled-components'

import ProtectedRoute, {
  JwtProtectedRouteGuard,
} from './modules/app/components/protected-route'
import UserAppEffects from './modules/app/UserAppEffects'

import MapView from './modules/map-view/map-view'
import FullscreenMap from './modules/map-view/fullscreen-map'

import DashboardPage from './modules/mifleet/components/dashboard/dashboard'
import OnboardingPage from './modules/mifleet/components/dashboard/onboarding'
import LatestUpdatesPage from './modules/mifleet/components/dashboard/latestUpdates'
import DocumentsPage from './modules/mifleet/components/dashboard/documents'
import OverviewPage from './modules/mifleet/components/overview/overview'

import DashboardLite from './modules/mifleet/lite/dashboard'
import ImportDataLite from './modules/mifleet/lite/import-data'
import MiFleetImportsHistory from './modules/mifleet/lite/imports/imports-history'
import FraudValidation from './modules/mifleet/components/fraudValidation'
import Contracts from './modules/mifleet/components/contracts/contracts'
import FileStorage from './modules/mifleet/components/files/files'
import mifleetDocuments from './modules/mifleet/documents'
import mifleetDocumentsEdit from './modules/mifleet/DocumentsEdit/DocumentsEdit'
import mifleetDocumentsLineEdit from './modules/mifleet/documents-line-edit'
import mifleetCreateLine from './modules/mifleet/components/documents/line-create-form'
import mifleetSettings from './modules/mifleet/MFSettings/mifleet-settings'

import { getMessages, updateMessages } from 'duxs/locale'

import {
  fetchPreLoginData,
  getLocale,
  getEnableWelcomePageSetting,
  getPreLoginQuery,
} from 'duxs/user'
import { getAppMainUrl } from 'duxs/app-routes-selectors'
import { getDefaultDateTimeLocale } from 'duxs/user-sensitive-selectors'

import ErrorBoundary from 'src/util-components/error-boundary'
import Tooltip from 'src/util-components/tooltip'
import Spinner from 'src/util-components/spinner'
import IntlGlobalProvider from 'src/util-components/intl-global-provider'

import { ADMIN } from 'src/modules/app/components/routes/admin'
import { ALERT_CENTER } from 'src/modules/app/components/routes/alert-center'
import { ALERTS } from 'src/modules/app/components/routes/alerts'
import { CARPOOL } from 'src/modules/app/components/routes/carpool'
import { COACHING } from 'src/modules/app/components/routes/coaching'
import { COSTS } from 'src/modules/app/components/routes/costs'
import { DASHBOARD } from 'src/modules/app/components/routes/dashboard'
import { DELIVERY } from 'src/modules/app/components/routes/delivery'
import { ENGINE_ALERTS } from 'src/modules/app/components/routes/engineAlerts'
import { FIELD_SERVICE } from 'src/modules/app/components/routes/field-service'
import { HELP } from 'src/modules/app/components/routes/help'
import { KNOW_THE_DRIVER } from 'src/modules/app/components/routes/knowthedriver'
import { LIST } from 'src/modules/app/components/routes/list'
import { MAINTENANCE } from 'src/modules/app/components/routes/maintenance'
import { MAP } from 'src/modules/app/components/routes/map'
import { REPORTS } from 'src/modules/app/components/routes/reports'
import { RUC } from 'src/modules/app/components/routes/ruc'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { TACHOGRAPH } from 'src/modules/app/components/routes/tachograph'
import { VISION } from 'src/modules/app/components/routes/vision'
import { AI_CHAT } from 'src/modules/app/components/routes/ai-chat'
import { SCORECARDS } from 'src/modules/app/components/routes/scorecards'

import { createProtectedRoutePropsFrom } from 'src/modules/app/components/routes/utils'

import AuthenticationSwitcher from './modules/app/authentication/AuthenticationSwitcher'
import AppEffects from './modules/app/AppEffects'
import RouterRoot from './RouterRoot'
import type { AppState } from './root-reducer'
import type { RouteComponentProps } from 'react-router-dom'
import DashboardRoutes from './modules/dashboard/DashboardRoutes'
import { CircularProgressDelayedAbsolute, LocalizationProvider } from '@karoo-ui/core'
import { ContentContainerScrollable } from './modules/app/components/content-containers'
// eslint-disable-next-line no-restricted-imports
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon' // The only use case where we need to import directly from @mui/x-date-pickers/
import { Settings as LuxonSettings } from 'luxon'
import { getURLParameters } from 'src/util-functions/string-utils'
import { BaseSnackbarProvider, EnhancedThemeProvider } from './providers'
import type { CartrackLocale } from 'api/user/types'
import { AppRootEffects } from './AppRootEffects'
import { mapCartrackLocaleToLuxonLocale } from './util-functions/luxon/utils'
import type { LocaleFileKey } from '../scripts/translate'
import { MapFleetProvider } from './modules/map-view/MapFleetProvider'
import { useTypedSelector } from './redux-hooks'
import { getVehiclesHaveBeenLoadedAtLeastOnce } from 'duxs/vehicles'

const List = loadableWithSpinnerAndRetry(() => import('./modules/lists'))

const NewProfile = loadableWithSpinnerAndRetry(
  () => import('./modules/admin/profile-settings/new'),
)
const EditProfile = loadableWithSpinnerAndRetry(
  () => import('./modules/admin/profile-settings/edit'),
)

const Settings = loadableWithSpinnerAndRetry(() => import('./modules/settings'))

const Privacy = loadableWithSpinnerAndRetry(() => import('./modules/privacy/privacy'))

const DeliveryRevamp = loadableWithSpinnerAndRetry(
  () => import('./modules/deliveryRevamp'),
)

// TODO: DELIVERY REVAMP - Should we continue to support this page?
const TrackingPackagePage = loadableWithSpinnerAndRetry(
  () => import('./modules/deliveryRevamp/Pages/TrackingPackage'),
)

const Alerts = loadableWithSpinnerAndRetry(() => import('./modules/alerts'))

const Maintenance = loadableWithSpinnerAndRetry(() => import('./modules/maintenance'))

const AIChat = loadableWithSpinnerAndRetry(() => import('./modules/ai-chat'))

const Scorecards = loadableWithSpinnerAndRetry(() => import('./modules/scorecards'))

// Share Location
const SharedApp = loadableWithSpinnerAndRetry(() => import('./modules/app/SharedApp'))

const VehicleTripApp = loadableWithSpinnerAndRetry(
  () => import('./modules/app/VehicleTripApp'),
)

const EngineAlerts = loadableWithSpinnerAndRetry(() => import('./modules/engine'))

const DriversMapView = loadableWithSpinnerAndRetry(
  () => import('./modules/map-view/DriversMapView/Tachograph'),
)

const Tachograph = loadableWithSpinnerAndRetry(() => import('./modules/tachograph'))

const KnowTheDriver = loadableWithSpinnerAndRetry(
  () => import('./modules/know-the-driver'),
)

const VehicleDetailWithSensorsInUrl = loadableWithSpinnerAndRetry(
  () =>
    import('./modules/app/GlobalModals/VehicleDetails/VehicleDetailWithSensorsInUrl'),
)

const VehicleClustersMapPage = loadableWithSpinnerAndRetry(
  () => import('./modules/map-view/VehicleClustersMapPage'),
)

const Vision = loadableWithSpinnerAndRetry(() => import('./modules/vision'))

const Help = loadableWithSpinnerAndRetry(() => import('./modules/help'))

const Carpool = loadableWithSpinnerAndRetry(() => import('./modules/carpool'))

const Ruc = loadableWithSpinnerAndRetry(() => import('./modules/ruc'))

const AlertCenter = loadableWithSpinnerAndRetry(() => import('./modules/alert-center'))

const Coaching = loadableWithSpinnerAndRetry(() => import('./modules/coaching'))

const HeatMapsMapView = loadableWithSpinnerAndRetry(
  () => import('./modules/map-view/HeatMapsMapView'),
)

const TagsMapView = loadableWithSpinnerAndRetry(
  () => import('./modules/map-view/TagsMapView'),
)
const Admin = loadableWithSpinnerAndRetry(() => import('./modules/admin/reminders'))

const Reports = loadableWithSpinnerAndRetry(() => import('./modules/reports'))

type OwnProps = RouteComponentProps

type Props = ReturnType<typeof mapStateToProps> & typeof actionCreators & OwnProps

type State = {
  loadedLocale: CartrackLocale | undefined
}

class AppRouter extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      loadedLocale: undefined,
    }

    // Disable missing translation message as translations will be added later.
    // We can add a toggle for this later when we have most translations.
    if (ENV.NODE_ENV === 'development') {
      const consoleError = console.error.bind(console)
      console.error = (message, ...args) => {
        if (typeof message === 'string' && message.startsWith('[React Intl]')) {
          return
        }

        consoleError(message, ...args)
      }
    }
  }

  componentDidMount() {
    this.props.fetchPreLoginData({ history: this.props.history })
  }

  componentDidUpdate(prevProps: Props) {
    const { locale } = this.props
    const { loadedLocale } = this.state

    if (
      (locale && locale !== prevProps.locale) ||
      (locale && loadedLocale === undefined)
    ) {
      this.fetchLocaleData(locale)
    }
  }

  fetchLocaleData(locale: CartrackLocale) {
    let isValid = true
    const checkLocales = importCartrackLocaleJson(locale)
      .then(() => (isValid = true))
      .catch(() => (isValid = false))

    checkLocales.then(() => {
      if (isValid) {
        localStorage.setItem('locale', locale)
        this.loadLocaleData(locale)
      } else {
        const fallback = locale === 'en' ? 'en' : 'en-ZA'
        console.warn(
          `[Cartrack] - Locale "${locale}" not recognized. Using fallback language "${fallback}".`,
        )
        localStorage.setItem('locale', fallback)
        this.loadLocaleData(fallback)
      }
    })
  }

  loadLocaleData = (locale: CartrackLocale) => {
    importCartrackLocaleJson(locale).then((messages) => {
      this.props.updateMessages(messages.default as Record<string, string>)
      this.updateLoadedLocale(locale)
    })
  }

  updateLoadedLocale = (newLocale: CartrackLocale) => {
    LuxonSettings.defaultLocale =
      this.props.defaultDateTimeLocale ?? mapCartrackLocaleToLuxonLocale(newLocale)
    LuxonSettings.defaultOutputCalendar = 'iso8601'

    this.setState({ loadedLocale: newLocale })
  }

  render() {
    const { mainUrl, enableWelcomePageSetting, preLoginQuery } = this.props

    return (
      <AppRootEffects>
        {!this.state.loadedLocale ||
        preLoginQuery.fetchStatus === 'fetching' ||
        preLoginQuery.status === 'pending' ? (
          // Not all pre login data is available yet
          <Spinner absolute />
        ) : (
          <EnhancedThemeProvider locale={this.state.loadedLocale}>
            <LocalizationProvider
              adapterLocale={
                this.props.defaultDateTimeLocale ??
                mapCartrackLocaleToLuxonLocale(this.state.loadedLocale)
              }
              dateAdapter={AdapterLuxon}
            >
              <IntlProvider
                key={this.state.loadedLocale} // remounts every component from this point on so that translations that are NOT being done by reactive components, such as e.g: ctIntl.formatMessage(), are re-calculated
                locale={this.state.loadedLocale}
                messages={this.props.messages}
                defaultLocale={this.state.loadedLocale}
                textComponent="span" // To keep same behaviour as v2
              >
                {/* SnackbarProvider MUST be render as a child of IntlProvider so that we can use FormattedMessage on snackbars */}
                <BaseSnackbarProvider autoHideDuration={4000}>
                  <IntlGlobalProvider>
                    {/* Catches errors within nav that would otherwise unmount app */}
                    <ErrorBoundary>
                      <Switch>
                        <Route
                          exact
                          path={MAP.SHARED_APP.path}
                          component={SharedApp}
                        />
                        <Route
                          exact
                          path={MAP.VEHICLE_TRIP_APP.path}
                          component={VehicleTripApp}
                        />
                        <Route
                          exact
                          path="/tracking-page"
                          component={TrackingPackagePage}
                        />
                        <Route path="*">
                          <AppEffects />
                          <Switch>
                            <Route
                              /** Can and will be improved */
                              path={`/(${
                                enableWelcomePageSetting ? 'welcome|' : ''
                              }login|forgot-password|get-login-details|sub-user|third-party-user|verify-code|username|resetpassword|setpassword|signup|2fa|mobile-app)`}
                              component={AuthenticationSwitcher}
                            />
                            <Route path="*">
                              <RouterRoot>
                                <JwtProtectedRouteGuard>
                                  <UserAppEffects />
                                </JwtProtectedRouteGuard>
                                {/** DEPRECATED - Purposely NOT available in AuthenticationSwitcher to discourage it's usage */}
                                <Tooltip />
                                {/* Catches errors within routes, allows navigation to continue */}
                                <ErrorBoundary>
                                  <Switch>
                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(LIST.MAIN)}
                                      component={List}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(HELP.MAIN)}
                                      component={Help}
                                    />
                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(VISION.MAIN)}
                                      component={Vision}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(
                                        ALERT_CENTER.MAIN,
                                      )}
                                      component={AlertCenter}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(SETTINGS.MAIN)}
                                      component={Settings}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(ALERTS.MAIN)}
                                      component={Alerts}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(
                                        MAINTENANCE.MAIN,
                                      )}
                                      component={Maintenance}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(DASHBOARD.MAIN)}
                                      component={DashboardRoutes}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(
                                        ENGINE_ALERTS.MAIN,
                                      )}
                                      component={EngineAlerts}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(
                                        TACHOGRAPH.MAIN,
                                      )}
                                      component={Tachograph}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(CARPOOL.MAIN)}
                                      component={Carpool}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(RUC.MAIN)}
                                      component={Ruc}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(COACHING.MAIN)}
                                      component={Coaching}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(ADMIN.MAIN)}
                                      component={Admin}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(AI_CHAT.MAIN)}
                                      component={AIChat}
                                    />

                                    <ProtectedRoute
                                      {...createProtectedRoutePropsFrom(
                                        SCORECARDS.MAIN,
                                      )}
                                      component={Scorecards}
                                    />

                                    <Route path="*">
                                      {/* Content container must be here for routes that do not control the subnavbar render */}
                                      <ContentContainerScrollable>
                                        <Switch>
                                          {/* PRIVACY */}
                                          <ProtectedRoute
                                            // flag="privacy"
                                            path="/privacy"
                                            component={Privacy}
                                          />
                                          <Route
                                            exact
                                            {...createProtectedRoutePropsFrom(
                                              MAP.VEHICLE_CLUSTERS_MAP_PAGE,
                                            )}
                                            component={VehicleClustersMapPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              MAP.TACHOGRAPH_DRIVERS,
                                            )}
                                            component={DriversMapView}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              MAP.HEAT_MAPS,
                                            )}
                                            component={HeatMapsMapView}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(MAP.TAGS)}
                                            component={TagsMapView}
                                          />
                                          <ProtectedRoute
                                            selector={MAP.tab.selector}
                                            path="/map/" // map/fullscreen is included in this pattern
                                            component={MapViewSwitcher}
                                          />
                                          <ProtectedRoute
                                            flag="reportsProfiles"
                                            path="/reports/profiles/new"
                                            component={NewProfile}
                                          />
                                          <ProtectedRoute
                                            flag="reportsProfiles"
                                            path="/reports/profiles/edit/:id"
                                            component={EditProfile}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              REPORTS.MAIN,
                                            )}
                                            component={Reports}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.REPORTS,
                                            )}
                                            component={Reports}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DASHBOARD,
                                            )}
                                            component={DashboardPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DASHBOARD.subRoutes.ONBOARDING,
                                            )}
                                            component={OnboardingPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DASHBOARD.subRoutes.LATEST_UPDATES,
                                            )}
                                            component={LatestUpdatesPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DASHBOARD.subRoutes.DOCUMENTS,
                                            )}
                                            component={DocumentsPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.LITE_DASHBOARD,
                                            )}
                                            component={DashboardLite}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.LITE_IMPORT_DATA,
                                            )}
                                            component={ImportDataLite}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.LITE_IMPORT_DATA_HISTORY,
                                            )}
                                            component={MiFleetImportsHistory}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.OVERVIEW,
                                            )}
                                            component={OverviewPage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.FRAUD_VALIDATION,
                                            )}
                                            component={FraudValidation}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.CONTRACTS,
                                            )}
                                            component={Contracts}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.FILES,
                                            )}
                                            component={FileStorage}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DOCUMENTS,
                                            )}
                                            component={mifleetDocuments}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DOCUMENTS.subRoutes.DOCUMENTS_EDIT,
                                            )}
                                            component={mifleetDocumentsEdit}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DOCUMENTS_LINE_EDIT,
                                            )}
                                            component={mifleetDocumentsLineEdit}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.DOCUMENTS_LINE_CREATE,
                                            )}
                                            component={mifleetCreateLine}
                                          />

                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              DELIVERY.tab,
                                            )}
                                            component={DeliveryRevamp}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              FIELD_SERVICE.tab,
                                            )}
                                            component={DeliveryRevamp}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              COSTS.MIFLEET_SETTINGS,
                                            )}
                                            component={mifleetSettings}
                                          />
                                          <ProtectedRoute
                                            {...createProtectedRoutePropsFrom(
                                              KNOW_THE_DRIVER.MAIN,
                                            )}
                                            component={KnowTheDriver}
                                          />
                                          <ProtectedRoute
                                            path={'/vehicles/:vehicleId'}
                                            component={VehicleDetailWithSensorsInUrl}
                                          />
                                          <Redirect
                                            exact
                                            from="*"
                                            to={mainUrl}
                                          />
                                        </Switch>
                                      </ContentContainerScrollable>
                                    </Route>
                                  </Switch>
                                </ErrorBoundary>
                              </RouterRoot>
                            </Route>
                          </Switch>
                        </Route>
                      </Switch>
                      <StyledToastContainer
                        limit={3}
                        closeOnClick
                      />
                    </ErrorBoundary>
                  </IntlGlobalProvider>
                </BaseSnackbarProvider>
              </IntlProvider>
            </LocalizationProvider>
          </EnhancedThemeProvider>
        )}
      </AppRootEffects>
    )
  }
}

function mapStateToProps(state: AppState, { location }: OwnProps) {
  return {
    preLoginQuery: getPreLoginQuery(state),
    locale: extractLocaleFromURL({ location }) ?? getLocale(state),
    messages: getMessages(state),
    mainUrl: getAppMainUrl(state),
    enableWelcomePageSetting: getEnableWelcomePageSetting(state),
    defaultDateTimeLocale: getDefaultDateTimeLocale(state),
  }
}

const actionCreators = {
  updateMessages,
  fetchPreLoginData,
}

const AppRoot = withRouter(connect(mapStateToProps, actionCreators)(AppRouter))

export default AppRoot

const extractLocaleFromURL = ({ location }: Pick<OwnProps, 'location'>) => {
  const [lang, locales] = getURLParameters(location.search, ['lang', 'locales'])
  const locale = locales || lang

  if (locale) {
    return getInternalLocalization(locale) //Extracted locale for now as per requirement
  }

  return undefined
}

async function importCartrackLocaleJson(
  locale: CartrackLocale,
): Promise<Record<string, unknown>> {
  const localeJsonKey = match(locale)
    .returnType<LocaleFileKey>()
    .with(
      'ar',
      'en',
      'en-NZ',
      'en-ZA',
      'es',
      'es-MX',
      'fil',
      'fr',
      'id',
      'ja-JP',
      'km',
      'ms',
      'pl',
      'pt-MZ',
      'pt-PT',
      'th',
      'vi',
      'zh',
      'zh-Hans-HK',
      'en-SG',
      (locale) => locale,
    )
    .exhaustive()

  return import(`../locales/${localeJsonKey}.json`)
}

const StyledToastContainer = styled(ToastContainer)`
  width: 600px;
`

function MapViewSwitcher() {
  const vehiclesHaveBeenLoadedAtLeastOnce = useTypedSelector(
    getVehiclesHaveBeenLoadedAtLeastOnce,
  )
  const appMainUrl = useTypedSelector(getAppMainUrl)
  const location = useLocation()
  return (
    <MapFleetProvider>
      <Switch>
        <ProtectedRoute
          path="/map/fullscreen"
          component={FullscreenMap}
        />
        <ProtectedRoute
          path="/map/:type"
          component={MapView}
        />
        <Route path="*">
          {/* Make sure we wait for hardware types to be available before redirecting to the map */}
          {vehiclesHaveBeenLoadedAtLeastOnce ? (
            <Redirect
              to={{
                pathname: appMainUrl,
                // IMPORTANT: we keep search params so that urls that focus a vehicle on fleet from external systems like CAMS, using otp, work correctly:
                // e.g - ?otp=**********&account=BANANAS&sensors=true&ot_vehicle_id=************&roadspeed=true&skip=1
                search: location.search,
              }}
            />
          ) : (
            <CircularProgressDelayedAbsolute />
          )}
        </Route>
      </Switch>
    </MapFleetProvider>
  )
}
