import {
  configureStore,
  getDefaultMiddleware,
  type Store,
  type StateFromReducersMapObject,
} from '@reduxjs/toolkit'
import { P } from 'ts-pattern'
import { mount } from 'cypress/react'
import createSagaMiddleware from 'redux-saga'
import { createBrowserHistory, type History } from 'history'
import { Provider as ReduxProvider } from 'react-redux'
import { QueryClient } from '@tanstack/react-query'
import { createBaseRootReducer } from 'src/root-reducer-utils'
import { ConnectedRouter, routerMiddleware } from 'connected-react-router'
// eslint-disable-next-line no-restricted-imports
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'
import { IntlProvider } from 'react-intl'
import enZA_Messages from '../../locales/en-ZA.json'
import { LocalizationProvider } from '@karoo-ui/core'
import IntlGlobalProvider from '../util-components/intl-global-provider'
import {
  EnhancedThemeProvider,
  QueryClientProviderWithReduxSync,
  HistoryProviderWithReduxSync,
  BaseSnackbarProvider,
} from '../providers'
import { AppRootEffects } from '../AppRootEffects'
import { basicSaga } from 'src/sagas/root-saga'
import { initialState as mapInitialState } from 'duxs/map'
import { initialState as timelineInitialState } from 'duxs/timeline'
import type { CartrackLocale } from 'api/user/types'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Settings } from 'luxon'
import GlobalModals from 'src/modules/app/GlobalModals'
import { mapCartrackLocaleToLuxonLocale } from 'src/util-functions/luxon/utils'
import type { FixMeAny } from 'src/types'
import type { IfUnknown } from 'type-fest'
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { authUtils } from 'api/auth-utils'
import { getAIChatApiUrl, getJwtAccessToken } from 'duxs/user-sensitive-selectors'
import { setReduxStoreSync } from 'src/redux-store-sync'
import { UserAppEffectsTestableModals } from 'src/modules/app/UserAppEffectsTestableModals'
import type { AppState } from 'src/root-reducer'

type AvailableAppStateForTesting = StateFromReducersMapObject<
  ReturnType<typeof createBaseRootReducer> & {
    map: typeof mapMockReducer
    timeline: typeof timelineMockReducer
  }
>

type MountOptions = Parameters<typeof mount>[1]

type MountWithProvidersOptions = {
  reduxOptions?: {
    preloadedState?: Partial<AvailableAppStateForTesting>
    store?: Store<AvailableAppStateForTesting, any>
  }
  locale?: CartrackLocale
  options?: MountOptions
  history?: History<unknown>
}

// NOTE: manipulate the reducer of map and timeline, since we only need the selectors,
// return the initial state directly
const mapMockReducer = (state = mapInitialState) => state

const timelineMockReducer = (state = timelineInitialState) => state

export function mountWithProviders(
  jsx: React.ReactElement,
  {
    reduxOptions,
    options,
    locale = 'en-ZA',
    history = createBrowserHistory(),
  }: MountWithProvidersOptions = {},
) {
  const sagaMiddleware = createSagaMiddleware()

  const middleware = [routerMiddleware(history), sagaMiddleware]

  middleware.push(
    ...getDefaultMiddleware({
      thunk: false,
      serializableCheck: false,
      immutableCheck: false,
    }),
  )

  const queryClient = createTestQueryClient()

  const preloadedState = {
    ...reduxOptions?.preloadedState,
    providersSync: {
      historyInContext: history,
      queryClientInContext: queryClient,
      ...reduxOptions?.preloadedState?.providersSync,
    },
  } satisfies Partial<AvailableAppStateForTesting>

  const store =
    reduxOptions?.store ??
    configureStore({
      reducer: {
        ...createBaseRootReducer(history),
        map: mapMockReducer,
        timeline: timelineMockReducer,
      },
      preloadedState,
      middleware,
    })

  setReduxStoreSync(store as Store<AppState>)
  sagaMiddleware.run(basicSaga, { storeDispatch: store.dispatch, history })

  const luxonLocale = mapCartrackLocaleToLuxonLocale(locale)

  Settings.defaultLocale = luxonLocale

  authUtils.setJwtAccessTokenGetter(() => getJwtAccessToken(store.getState()))
  authUtils.setAIChatApiUrlGetter(() => getAIChatApiUrl(store.getState()))

  const initialReduxState = store.getState()

  const ui = (
    <ReduxProvider store={store}>
      <DndProvider backend={HTML5Backend}>
        <QueryClientProviderWithReduxSync client={queryClient}>
          <HistoryProviderWithReduxSync history={history}>
            <ConnectedRouter history={history}>
              <EnhancedThemeProvider locale={locale}>
                <LocalizationProvider
                  adapterLocale={luxonLocale}
                  dateAdapter={AdapterLuxon}
                >
                  <IntlProvider
                    key={locale} // remounts every component from this point on so that translations that are NOT being done by reactive components, such as e.g: ctIntl.formatMessage(), are re-calculated
                    locale={locale}
                    messages={enZA_Messages as FixMeAny}
                    defaultLocale={locale}
                    textComponent="span" // To keep same behaviour as v2
                  >
                    <AppRootEffects>
                      <BaseSnackbarProvider>
                        <IntlGlobalProvider>
                          <>
                            {initialReduxState.user.user ? (
                              <UserAppEffectsTestableModals />
                            ) : null}
                            {jsx}
                          </>
                        </IntlGlobalProvider>
                        <GlobalModals />
                      </BaseSnackbarProvider>
                    </AppRootEffects>
                  </IntlProvider>
                </LocalizationProvider>
              </EnhancedThemeProvider>
            </ConnectedRouter>
          </HistoryProviderWithReduxSync>
        </QueryClientProviderWithReduxSync>
      </DndProvider>
    </ReduxProvider>
  )

  return [mount(ui, { strict: false, ...options }), { store }] as const
}

// Based on https://tkdodo.eu/blog/testing-react-query and https://github.com/TanStack/query/blob/ead2e5dd5237f3d004b66316b5f36af718286d2d/src/react/tests/utils.tsx#L6-L17
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

export const exhaustiveEndpointCallCheck = (unexpectedBody: any): void => {
  throw new Error(
    '[Cartrack] - Missing handler for request body: ' + JSON.stringify(unexpectedBody),
  )
}

/**
 * @description The reason for this util is to run tests in order, with a single `it` command for __faster__ execution in certain tests.
 */
export const runTestsInOrderWithLoggingAndSetup = (
  testsName: string,
  {
    tests,
    setupIfFirstTest,
  }: {
    tests: Array<{
      /**
       * If `true` __and__ in cypress `open` mode, only this test will run
       */
      only?: true
      name: string
      fn: (args: {
        setupIfFirstTest: () => void
      }) => Cypress.Chainable<JQuery<HTMLElement>>
    }>
    setupIfFirstTest: () => void
  },
) => {
  const onlyTest = tests.find(({ only }) => only === true)
  if (onlyTest && ENV.CYPRESS_CT_ENV === 'cypress-component-test-open') {
    return it(`${testsName} -> ${onlyTest.name}`, () =>
      onlyTest.fn({ setupIfFirstTest: setupIfFirstTest }))
  }

  return it(testsName, () => {
    tests.reduce<Cypress.Chainable<JQuery<HTMLElement>> | null>(
      (acc, { name, fn }, i) => {
        const wrappedTestFn = () => {
          cy.log(` __************__ __TEST__ __************__ __${name}__`)
          return fn({ setupIfFirstTest: i === 0 ? setupIfFirstTest : () => {} })
        }
        return acc ? acc.then(wrappedTestFn) : wrappedTestFn()
      },
      null,
    )
  })
}

/**
 * @description The reason for this util is to run tests in order, with a single `it` command for __faster__ execution in certain tests.
 */
export const runTestsInOrderWithLoggingAndSetupOnce = (
  testsName: string,
  {
    tests,
    setupOnceBeforeTests,
  }: {
    tests: Array<{
      /**
       * If `true` __and__ in cypress `open` mode, only this test will run
       */
      only?: true
      name: string
      fn: () => Cypress.Chainable<JQuery<HTMLElement>>
    }>
    setupOnceBeforeTests: () => void
  },
) => {
  const onlyTest = tests.find(({ only }) => only === true)
  if (onlyTest && ENV.CYPRESS_CT_ENV === 'cypress-component-test-open') {
    return it(`${testsName} -> ${onlyTest.name}`, () => {
      setupOnceBeforeTests()
      return onlyTest.fn()
    })
  }

  return it(testsName, () => {
    setupOnceBeforeTests()
    tests.reduce<Cypress.Chainable<JQuery<HTMLElement>> | null>((acc, { name, fn }) => {
      const wrappedTestFn = () => {
        cy.log(` __************__ __TEST__ __************__ __${name}__`)
        return fn()
      }
      return acc ? acc.then(wrappedTestFn) : wrappedTestFn()
    }, null)
  })
}

export function matchWithMethod<Params>(
  req: CyHttpMessages.IncomingHttpRequest,
  method: string,
  { requestAlias = method }: { requestAlias?: string } = {},
) {
  return {
    method: P.when((m): m is typeof method => {
      if (m === method) {
        // eslint-disable-next-line no-param-reassign
        req.alias = requestAlias
        return true
      }
      return false
    }),
    params: P.when((p): p is Params => {
      // @ts-expect-error We need to "use" `p` variable so typescript does not consider the variable as unused
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, sonarjs/no-dead-store, sonarjs/sonar-no-unused-vars
      const a = p
      return true
    }),
  }
}

/**
 * Minimal typed version of cypress expect with generics
 */
export function cyExpect<Val>(val: Val, message?: string) {
  const expectedExp = expect(val, message)
  return {
    toDeepEqual: (
      equalValue: IfUnknown<
        Val,
        any, // If Val is unknown, we let the developer pass any value
        Val & { /* to help with some legacy methods */ x?: 'x' }
      >,
      message?: string,
    ) => expectedExp.to.deep.equal(equalValue, message),

    toEqual: (equalValue: Val, message?: string) =>
      expectedExp.to.equal(equalValue, message),

    toBeEqual: (equalValue: Val, message?: string) =>
      expectedExp.to.be.equal(equalValue, message),

    toEq: (equalValue: Val, message?: string) => expectedExp.to.eq(equalValue, message),

    toContain: (equalValue: Val, message?: string) =>
      expectedExp.to.contain(equalValue, message),

    toBeA: (
      type: 'string' | 'number' | 'null' | 'promise' | 'symbol',
      message?: string,
    ) => expectedExp.to.be.a(type, message),

    toBeAn: (type: 'error' | 'object' | 'array', message?: string) =>
      expectedExp.to.be.an(type, message),
  }
}
