export const selectDateOnDatePicker = (
  cyElement: Cypress.Chainable<JQuery>,
  {
    year,
    month,
    day,
  }: {
    year: number // 4 digits
    month: number
    day: number
  },
) => {
  // Always 2 digits
  const parsedDay = day.toString().padStart(2, '0')
  const parsedMonth = month.toString().padStart(2, '0')
  cyElement.type(`{leftArrow}{leftArrow}{leftArrow}${year}${parsedMonth}${parsedDay}`)
}
