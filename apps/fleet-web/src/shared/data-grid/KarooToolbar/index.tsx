import { useMemo, useContext } from 'react'
import { ctIntl } from 'src/util-components/ctIntl'
import type { DateTime } from 'luxon'
import { debounce } from 'lodash'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import {
  GridToolbarContainer,
  GridToolbarFilterButton,
  DateRangePicker,
  DatePicker,
  TextField,
  styled,
  Stack,
  SingleInputDateRangeField,
  InputAdornment,
  useGridApiContext,
  type DateRange,
  type PickersShortcutsItem,
  type GridApi,
  type GridToolbarFilterButtonProps,
  type DateRangePickerProps as MuiDateRangePickerProps,
  type DatePickerProps as MuiDatePickerProps,
  type KarooUiInternalTheme,
  type SxProps,
  type TextFieldProps,
  type GridStateColDef,
} from '@karoo-ui/core'
import SearchIcon from '@mui/icons-material/Search'
import type { Except, SetRequired } from 'type-fest'
import KarooToolbarSettings from './KarooToolbarSettingsButton'

export type DataContextType = {
  gridApi: React.MutableRefObject<GridApi> | null
  setGridApi: (api: React.MutableRefObject<GridApi> | null) => void
}

type DatePickerProps = SetRequired<MuiDatePickerProps<DateTime>, 'value' | 'onChange'>

type DateRangePickerProps = SetRequired<
  MuiDateRangePickerProps<DateTime>,
  'value' | 'onChange'
> & {
  resetDefaultShortcut?: PickersShortcutsItem<DateRange<DateTime>>
}

export type KarooToolbarProps = {
  gridApiContext: React.MutableRefObject<GridApi>
  slots?: {
    datePicker?: { show: true; props: DatePickerProps }
    dateRangePicker?: { show: true; props: DateRangePickerProps }
    searchFilter?: {
      show: true
      props?: {
        label?: string
        quickSearchDebounceMs?: number
      } & Partial<Pick<TextFieldProps, 'defaultValue'>>
    }
    filterButton?: { show: true; props?: GridToolbarFilterButtonProps }
    settingsButton?: {
      show: true
      props?: {
        getTogglableFields?: (
          columns: Array<GridStateColDef>,
        ) => Array<GridStateColDef['field']>
      }
    }
  }
  slotProps?: {
    right?: SxProps<KarooUiInternalTheme>
  }
  extraContent?: {
    left?: React.ReactNode
    middle?: React.ReactNode
    right?: React.ReactNode
    bottom?: React.ReactNode
  }
}

type ConditionalContextKaroo = Except<KarooToolbarProps, 'gridApiContext'> &
  (
    | {
        isOutsideGrid: true
        outsideContext: React.Context<DataContextType>
      }
    | {
        isOutsideGrid?: false
        outsideContext?: never
      }
  )

const AgnosticKarooToolbar = ({
  gridApiContext,
  slots,
  extraContent,
  slotProps,
}: KarooToolbarProps) => {
  const shortcuts = useDateRangeShortcutItems()

  const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    e.preventDefault()
  }

  const shortcutsItems = useMemo((): Array<
    PickersShortcutsItem<DateRange<DateTime>>
  > => {
    const {
      thisWeek,
      lastWeekTillDate,
      lastWeek,
      last2Weeks,
      lastMonthTillDate,
      thisMonth,
      lastMonth,
      reset,
    } = shortcuts

    const resetOption = slots?.dateRangePicker?.props?.resetDefaultShortcut
      ? {
          ...slots?.dateRangePicker?.props?.resetDefaultShortcut,
          label: reset.label,
        }
      : reset

    return slots?.dateRangePicker?.props?.slotProps?.shortcuts &&
      'items' in slots.dateRangePicker.props.slotProps.shortcuts
      ? (slots.dateRangePicker.props.slotProps.shortcuts.items as Array<
          PickersShortcutsItem<DateRange<DateTime>>
        >)
      : [
          thisWeek,
          lastWeekTillDate,
          lastWeek,
          last2Weeks,
          thisMonth,
          lastMonthTillDate,
          lastMonth,
          resetOption,
        ]
  }, [
    shortcuts,
    slots?.dateRangePicker?.props?.resetDefaultShortcut,
    slots?.dateRangePicker?.props?.slotProps?.shortcuts,
  ])

  const { quickSearchDebounceMs, quickSearchTextFieldProps } = useMemo(() => {
    const { quickSearchDebounceMs, ...quickSearchTextFieldProps } =
      slots?.searchFilter?.props || {}
    return { quickSearchDebounceMs, quickSearchTextFieldProps }
  }, [slots?.searchFilter?.props])

  const handleSearchFilterChange = debounce((e) => {
    gridApiContext.current.setQuickFilterValues([e.target.value])
  }, quickSearchDebounceMs || 0)

  return (
    <Stack
      sx={{
        p: 2,
        gap: 2,
      }}
    >
      <GridToolbarContainer
        sx={{ justifyContent: 'space-between', p: 0, height: 'fit-content' }}
      >
        <GridToolbarLeftContainer>
          {extraContent?.left}

          {slots?.dateRangePicker?.show && (
            <DateRangePicker
              {...slots.dateRangePicker.props}
              value={slots.dateRangePicker.props?.value}
              onChange={([start, end], tc) =>
                // FIXME: it's a mui's bug: https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/1198#note_714069
                // temporarily solve it by setting the date clock to the start of day
                slots.dateRangePicker?.props?.onChange(
                  [
                    start ? start.startOf('day') : null,
                    end ? end.startOf('day') : null,
                  ],
                  tc,
                )
              }
              label={
                slots.dateRangePicker.props?.label ??
                ctIntl.formatMessage({ id: 'Date Range' })
              }
              slots={{
                field: SingleInputDateRangeField,
              }}
              slotProps={{
                shortcuts: { items: shortcutsItems },
                textField: {
                  onKeyDown: onKeyDown,
                  sx: {
                    input: {
                      width: '224px',
                      cursor: 'pointer',
                    },
                  },
                },
              }}
            />
          )}

          {slots?.datePicker?.show && (
            <DatePicker
              {...slots.datePicker.props}
              label={
                slots.datePicker.props?.label ?? ctIntl.formatMessage({ id: 'Date' })
              }
              value={slots.datePicker.props?.value}
              onChange={slots.datePicker.props?.onChange}
            />
          )}

          {slots?.searchFilter?.show && (
            <TextField
              {...quickSearchTextFieldProps}
              label={
                slots.searchFilter.props?.label ??
                ctIntl.formatMessage({ id: 'Search' })
              }
              variant="outlined"
              onChange={handleSearchFilterChange}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                },
              }}
            />
          )}

          {slots?.filterButton?.show && (
            <GridToolbarFilterButton
              {...slots.filterButton.props}
              slotProps={{
                button: {
                  sx: {
                    color: 'action.active',
                    minWidth: 'unset',
                    fontSize: 0,

                    '.MuiButton-startIcon': {
                      margin: 0,
                    },
                  },
                },
              }}
            />
          )}

          {extraContent?.middle}
        </GridToolbarLeftContainer>
        <GridToolbarRightContainer sx={slotProps?.right}>
          {extraContent?.right}

          {slots?.settingsButton?.show && (
            <KarooToolbarSettings
              gridApiContext={gridApiContext}
              getTogglableFields={slots.settingsButton.props?.getTogglableFields}
            />
          )}
        </GridToolbarRightContainer>
      </GridToolbarContainer>

      {extraContent?.bottom}
    </Stack>
  )
}

const InsideGridContextKarooToolbar = (props: ConditionalContextKaroo) => {
  const gridApiContext = useGridApiContext()

  return (
    <AgnosticKarooToolbar
      {...props}
      gridApiContext={gridApiContext}
    />
  )
}

const OutsideGridContextKarooToolbar = (props: ConditionalContextKaroo) => {
  const { gridApi } = useContext(props.outsideContext as React.Context<DataContextType>)

  if (gridApi) {
    return (
      <AgnosticKarooToolbar
        {...props}
        gridApiContext={gridApi}
      />
    )
  }
  return null
}

const KarooToolbar = (props: ConditionalContextKaroo) => {
  if (props.isOutsideGrid && props.outsideContext) {
    return <OutsideGridContextKarooToolbar {...props} />
  }

  return <InsideGridContextKarooToolbar {...props} />
}

KarooToolbar.createProps = (props: ConditionalContextKaroo) => props

export default Object.assign(KarooToolbar, {
  createProps: (props: KarooToolbarProps) => props,
})

const BaseGridToolbarContainerWithItems = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    alignItems: 'center',
    gap: 1,
  }),
)

const GridToolbarLeftContainer = styled(BaseGridToolbarContainerWithItems)`
  display: flex;
  justify-content: flex-start;
`

const GridToolbarRightContainer = styled(BaseGridToolbarContainerWithItems)`
  display: flex;
  justify-content: flex-end;
`
