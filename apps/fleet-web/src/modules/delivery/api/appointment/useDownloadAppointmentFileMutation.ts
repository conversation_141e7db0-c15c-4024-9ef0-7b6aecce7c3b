import { useMutation } from '@tanstack/react-query'
import download from 'downloadjs'

import { makeMutationErrorHandlerWithSnackbar } from 'api/helpers'
import { apiCallerNoX } from 'src/api/api-caller'
import type { PromiseResolvedType } from 'src/types'

export declare namespace DownloadAppointmentFile {
  export type Params = {
    appointmentFileId: number
    appointmentFileName: string
    appointmentId?: number
    jobId?: number
  }

  export type ApiOutput = Blob
  export type Return = PromiseResolvedType<typeof downloadAppointmentFile>
}

const createKey = () => ['delivery', 'downloadAppointmentFile'] as const

const useDownloadAppointmentFileMutation = () =>
  useMutation({
    mutationKey: createKey(),
    mutationFn: (params: DownloadAppointmentFile.Params) =>
      downloadAppointmentFile(params),
    ...makeMutationErrorHandlerWithSnackbar(),
  })

const downloadAppointmentFile = async ({
  jobId,
  appointmentFileId,
  appointmentFileName,
}: DownloadAppointmentFile.Params): Promise<Blob> => {
  const res = await apiCallerNoX<Response>(
    'delivery_get_appointment_file',
    {
      data: { jobId, appointmentFileId },
    },
    { noParse: true },
  )

  const contentType = res.headers.get('content-type') ?? 'application/octet-stream'

  if (contentType.includes('application/json')) {
    const err = await res.json()
    throw new Error(err?.message || 'Failed to download file')
  }

  const blob = await res.blob()
  download(blob, appointmentFileName, contentType)
  return blob
}

export default Object.assign(useDownloadAppointmentFileMutation, {
  createKey,
})
