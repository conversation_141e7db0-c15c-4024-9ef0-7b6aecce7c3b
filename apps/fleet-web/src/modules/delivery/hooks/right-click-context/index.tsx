import { useCallback, useContext, useState } from 'react'
import { includes } from 'lodash'
import { Typography } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline'
import DoDisturbOnOutlinedIcon from '@mui/icons-material/DoDisturbOnOutlined'
import DownloadIcon from '@mui/icons-material/Download'
//SVG
import EditIcon from '@mui/icons-material/Edit'
import HistoryIcon from '@mui/icons-material/History'
import LinkIcon from '@mui/icons-material/Link'
import PersonRemoveIcon from '@mui/icons-material/PersonRemove'
import RestoreIcon from '@mui/icons-material/Restore'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router'
//types
import type { ValueOf } from 'type-fest'

import { buildQueryStringFromObject } from 'api/utils'
import { getDeliveryMainPath } from 'duxs/user'
import { getDeliveryAppointmentsSetting } from 'duxs/user-sensitive-selectors'
//context
import { DeliveryContext } from 'src/modules/delivery'
//api
import type { JobDataList } from 'src/modules/delivery/api/jobs/types'
import useDeliveryUnassignJob from 'src/modules/delivery/api/jobs/useDeliveryUnassignJob'
import useRemovePlanMutation from 'src/modules/delivery/api/plans/useDeliveryRemovePlanMutation'
//hooks
import useDeleteJobHook from 'src/modules/delivery/right-panel/Job/hooks/useDeleteJob'
import {
  clickDuplicateJob,
  clickedLeftPanelDeliveryJob,
  initializeDownloadJobReport,
  multipleSelectDeliveryJobIdsWithShiftKey,
} from 'src/modules/delivery/slice'
import {
  DELIVERY_PAGES,
  JOB_STATUS_TO_ID,
  REPORT_TYPES,
} from 'src/modules/delivery/utils/constants'
import Dialog from 'src/modules/delivery/utils/dialog/dialog-configurator'
import { ASSIGN_CARD_TABS } from 'src/modules/deliveryRevamp/components/AssignCard'
import DuplicateAssignPopover from 'src/modules/deliveryRevamp/components/DuplicateAssignPopover'
import { DriversAndRoutesProvider } from 'src/modules/deliveryRevamp/components/MapPanel/DriversAndRoutesProvider'
import { DeliveryMainPageContextProvider } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { useCreateDeliveryBulkJobMutation } from '../../api/jobs/useCreateDeliveryBulkJobMutation'
import CancelAppointmentConfirmDialog from '../../appointment/components/CancelAppointmentConfirmDialog'
import useRefresh from '../../middle-panel/hooks/useRefresh'
import useCancelJobHook from '../../right-panel/Job/hooks/useCancelJob'
import useRestoreJobHook from '../../right-panel/Job/hooks/useRestoreJob'
import { ForceReject } from './component/jobs/modals/force-reject'
import { JobActivityModal } from './component/jobs/modals/job-activity-modal'
import { WaybillReport } from './component/jobs/modals/waybill-report'

type Props = {
  job: JobDataList
  handleEditJob?: () => void
  withoutEdit?: boolean
}

enum MODAL_TYPE {
  Waybill = 'waybill',
  ForceReject = 'forceReject',
  DuplicateAssign = 'duplicateAssign',
  Activity = 'activity',
  CancelJob = 'cancelJob',
}

export type ModalType = ToUnion<typeof MODAL_TYPE> | null

export const useCreateJobContextItems = ({
  job,
  handleEditJob,
  withoutEdit,
}: Props) => {
  const canDelete = !job.deliveryDriverId && !job.planId
  const canEdit =
    (JOB_STATUS_TO_ID.COMPLETED !== job.jobStatusId &&
      JOB_STATUS_TO_ID.CANCELED !== job.jobStatusId &&
      !job.inProgress) ||
    JOB_STATUS_TO_ID.ASSIGN_LATER === job.jobStatusId
  const canUnassign = !!job.deliveryDriverId || !!job.planId
  const withoutRestore = JOB_STATUS_TO_ID.CANCELED !== job.jobStatusId

  const isJobCanceled = job.jobStatusId === JOB_STATUS_TO_ID.CANCELED

  const isGotoAppointmentVisible = Boolean(job.appointment)

  const dispatch = useDispatch()

  const { deliverySettings } = useContext(DeliveryContext)

  const { refresh } = useRefresh()

  const history = useHistory()

  const isWaybillVisible =
    includes(
      [JOB_STATUS_TO_ID.ASSIGNED, JOB_STATUS_TO_ID.COMPLETED],
      job.jobStatusId,
    ) &&
    !!deliverySettings.inetCustomReportTemplate &&
    !!job.deliveryDriverId

  const jobIds = {
    jobIds: [job.jobId],
  }

  const [modalType, setModalType] = useState<ModalType>(null)

  const deleteJob = useDeleteJobHook(jobIds)
  const cancelJob = useCancelJobHook(jobIds)
  const restoreJob = useRestoreJobHook(jobIds)
  const { mutate: unassignJob } = useDeliveryUnassignJob()
  const { mutate: unassignPlan } = useRemovePlanMutation()
  const createDeliveryBulkJobMutation = useCreateDeliveryBulkJobMutation()
  const deliveryAppointmentsSetting = useTypedSelector(getDeliveryAppointmentsSetting)

  const deliveryMainPath = useTypedSelector(getDeliveryMainPath)

  const handleCancel = () => cancelJob.mutate()

  const handleRestore = () => restoreJob.mutate()

  const handleUnassign = (job: JobDataList) => {
    if (job.planId) {
      handleUnassignPlan(job)
    } else {
      handleUnassignJob(job)
    }
  }

  const handleUnassignPlan = (job: JobDataList) =>
    unassignPlan({
      jobId: [job.jobId],
      planId: job.planId || 0,
    })

  const handleUnassignJob = (job: JobDataList) =>
    unassignJob({
      jobIds: [job.jobId],
    })

  const handleDelete = (job: JobDataList) =>
    deleteJob.mutate({
      jobIds: [job.jobId],
    })

  const handleCloseModal = useCallback(() => {
    setModalType(null)
  }, [])

  const handleOnDelete = () => {
    Dialog.alert({
      title: (
        <>
          <Typography variant="h6">
            {ctIntl.formatMessage({
              id: 'delivery.rightPanel.jobDelete.successful',
            })}
          </Typography>
          <Typography variant="h6">{job.referenceNumber || job.jobNumber}</Typography>
        </>
      ),
      content: ctIntl.formatMessage({
        id: 'delivery.rightPanel.jobDelete.successful.subtitle',
      }),
      onResult: () => handleDelete(job),
      confirmButtonLabel: ctIntl.formatMessage({
        id: 'Delete',
      }),
    })
  }

  const forceRejectItem = [
    {
      value: MODAL_TYPE.ForceReject,
      label: ctIntl.formatMessage({
        id: 'Force Reject',
      }),
      icon: <DoDisturbOnOutlinedIcon color="error" />,
      onClick: () => {
        setModalType(MODAL_TYPE.ForceReject)
      },
    },
  ]

  const waybillItem = [
    {
      value: MODAL_TYPE.Waybill,
      label: ctIntl.formatMessage({
        id: 'Waybill',
      }),
      icon: <DownloadIcon />,
      onClick: () => {
        setModalType(MODAL_TYPE.Waybill)
      },
    },
  ]

  const duplicateAssignItem = [
    {
      value: MODAL_TYPE.DuplicateAssign,
      label: ctIntl.formatMessage({
        id: 'delivery.actionsMenu.duplicateAndAssign',
      }),
      icon: <ContentCopyIcon />,
      onClick: () => {
        setModalType(MODAL_TYPE.DuplicateAssign)
      },
      disabled: isJobCanceled,
    },
  ]

  const editItem = !withoutEdit
    ? [
        {
          value: 'edit',
          label: ctIntl.formatMessage({
            id: 'delivery.rightPanel.jobForm.label.editJob',
          }),
          icon: <EditIcon />,
          onClick: () => {
            if (canEdit) {
              handleEditJob?.()
            }
          },
          disabled: !canEdit,
        },
      ]
    : []

  const gotoAppointmentItem = isGotoAppointmentVisible
    ? [
        {
          value: 'goToAppointment',
          label: ctIntl.formatMessage({
            id: 'Go to appointment',
          }),
          icon: <LinkIcon />,
          onClick: () => {
            if (job.appointment) {
              const query = buildQueryStringFromObject({
                appointmentId: job.appointment.appointmentId,
              })
              history.push(
                `/${deliveryMainPath}/${DELIVERY_PAGES.APPOINTMENT}?${query}`,
              )
            }
          },
        },
      ]
    : []

  const restoreItems = withoutRestore
    ? [...editItem]
    : [
        {
          value: 'restore',
          label: ctIntl.formatMessage({
            id: 'delivery.job.restore',
          }),
          icon: <RestoreIcon />,
          onClick: () => {
            handleRestore()
          },
        },
      ]

  const cancelUnassignItems = withoutRestore
    ? [
        {
          value: 'cancel',
          label: ctIntl.formatMessage({
            id: 'delivery.job.cancel',
          }),
          icon: <CloseIcon />,
          onClick: () => {
            if (!canEdit) return

            if (job.appointment) {
              setModalType(MODAL_TYPE.CancelJob)
            } else {
              handleCancel()
            }
          },
          disabled: !canEdit,
        },
        {
          value: 'jobReport',
          label: ctIntl.formatMessage({
            id: 'deliver.download.jobReport',
          }),
          icon: <DownloadIcon />,
          onClick: () => {
            dispatch(
              initializeDownloadJobReport({
                ids: [job.jobId],
                downloadType: REPORT_TYPES.JOB_REPORT as ValueOf<typeof REPORT_TYPES>,
              }),
            )
          },
        },
        ...(isWaybillVisible ? waybillItem : []),
        {
          value: 'shippingLabel',
          label: ctIntl.formatMessage({
            id: 'Shipping Label',
          }),
          icon: <DownloadIcon />,
          onClick: () => {
            dispatch(
              initializeDownloadJobReport({
                ids: [job.jobId],
                downloadType: REPORT_TYPES.SHIPPING_LABEL as ValueOf<
                  typeof REPORT_TYPES
                >,
              }),
            )
          },
        },
        {
          value: 'unassign',
          label: ctIntl.formatMessage({
            id: 'delivery.job.unassign',
          }),
          icon: <PersonRemoveIcon />,
          onClick: () => {
            if (canEdit && canUnassign) {
              handleUnassign(job)
            }
          },
          disabled: !canEdit || !canUnassign,
        },
      ]
    : [
        {
          value: 'jobReport',
          label: ctIntl.formatMessage({
            id: 'deliver.download.jobReport',
          }),
          icon: <DownloadIcon />,
          onClick: () => {
            dispatch(
              initializeDownloadJobReport({
                ids: [job.jobId],
                downloadType: REPORT_TYPES.JOB_REPORT as ValueOf<typeof REPORT_TYPES>,
              }),
            )
          },
        },
        ...(isWaybillVisible ? waybillItem : []),
        {
          value: 'shippingLabel',
          label: ctIntl.formatMessage({
            id: 'Shipping Label',
          }),
          icon: <DownloadIcon />,
          onClick: () => {
            dispatch(
              initializeDownloadJobReport({
                ids: [job.jobId],
                downloadType: REPORT_TYPES.SHIPPING_LABEL as ValueOf<
                  typeof REPORT_TYPES
                >,
              }),
            )
          },
        },
      ]

  const activityItem = deliveryAppointmentsSetting
    ? [
        {
          value: MODAL_TYPE.Activity,
          label: ctIntl.formatMessage({
            id: 'Activity',
          }),
          icon: <HistoryIcon />,
          onClick: () => {
            setModalType(MODAL_TYPE.Activity)
          },
        },
      ]
    : []

  const validForceRejectStatuses = [
    JOB_STATUS_TO_ID.COMPLETED,
    JOB_STATUS_TO_ID.UNASSIGNED,
    JOB_STATUS_TO_ID.REJECTED,
    JOB_STATUS_TO_ID.CANCELED,
  ]

  const otherContextItems = [
    ...restoreItems,
    {
      value: 'duplicate',
      label: ctIntl.formatMessage({
        id: 'delivery.actionsMenu.duplicateJob',
      }),
      icon: <ContentCopyIcon />,
      onClick: () => {
        dispatch(clickDuplicateJob(job.jobId))
      },
      disabled: isJobCanceled,
    },
    ...duplicateAssignItem,
    ...activityItem,
    ...cancelUnassignItems,
    ...(job.deliveryDriverId && !includes(validForceRejectStatuses, job.jobStatusId)
      ? forceRejectItem
      : []),
    ...gotoAppointmentItem,
    {
      value: 'delete',
      label: ctIntl.formatMessage({
        id: 'Delete',
      }),
      icon: <DeleteOutlineIcon color="error" />,
      onClick: () => {
        if (canDelete) {
          handleOnDelete()
        }
      },
      disabled: !canDelete,
    },
  ]

  return {
    contextItems: [...otherContextItems],
    cancelModal:
      job.appointment && modalType === MODAL_TYPE.CancelJob ? (
        <CancelAppointmentConfirmDialog
          open={modalType === MODAL_TYPE.CancelJob}
          onClose={handleCloseModal}
          appointmentId={job.appointment.appointmentId}
          jobId={job.jobId}
        />
      ) : null,
    forceRejectModal: (
      <ForceReject
        jobId={job.jobId}
        deliveryDriverId={job.deliveryDriverId}
        show={modalType === MODAL_TYPE.ForceReject}
        onClose={handleCloseModal}
      />
    ),
    waybillModal: (
      <WaybillReport
        jobId={job.jobId}
        show={modalType === MODAL_TYPE.Waybill}
        onClose={handleCloseModal}
      />
    ),
    activityModal:
      modalType === MODAL_TYPE.Activity ? (
        <JobActivityModal
          open={true}
          onClose={handleCloseModal}
          jobId={job.jobId}
        />
      ) : null,
    duplicateAssignModal:
      modalType === MODAL_TYPE.DuplicateAssign ? (
        <DeliveryMainPageContextProvider>
          <DriversAndRoutesProvider>
            <DuplicateAssignPopover
              keepExactScheduleDate
              tabs={[ASSIGN_CARD_TABS.DRIVERS]}
              jobId={job.jobId}
              isSubmitting={createDeliveryBulkJobMutation.isPending}
              onClose={handleCloseModal}
              onSubmit={(jobs) => {
                createDeliveryBulkJobMutation.mutate(
                  { jobs },
                  {
                    onSuccess: async (data) => {
                      handleCloseModal()
                      await refresh()
                      const jobIds = data.createdJobs.map((job) => job.job_id)
                      dispatch(
                        jobIds.length === 1
                          ? clickedLeftPanelDeliveryJob(jobIds[0])
                          : multipleSelectDeliveryJobIdsWithShiftKey(jobIds),
                      )
                    },
                  },
                )
              }}
            />
          </DriversAndRoutesProvider>
        </DeliveryMainPageContextProvider>
      ) : null,
  }
}
