import { DateTime } from 'luxon'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { ReadonlyDeep } from 'type-fest'

import { createBetterSetState } from 'src/util-functions/state-utils'

import { DATA_RESOURCE_ID_ATTRIBUTE } from './utils'

export type DriverAvailabilitySchedulerState = ReadonlyDeep<{
  selectionMeta: {
    value: TimelineSelectionRange
  }
  cursorStyle: string
  dragMeta: DragMeta | null
}>

export type TimelineSelectionRange = ReadonlyDeep<{
  start: DateTime | null
  end: DateTime | null
  resourceId: string | null
  rangeEditorAnchorEl: EventTarget | null
  remark?: string
}>

type DragMeta =
  | {
      dragHandle: 'start' | 'end' | 'new'
    }
  | {
      dragHandle: 'move'
      dragOffsetMinutes: number
      originalStart: DateTime
      originalEnd: DateTime
    }

type DragHandle = DragMeta['dragHandle']

type MouseDownPayload = {
  containerRect: DOMRect
  clientX: number
  clientY: number
  resourceId: string
  resizeHandle: DragHandle | null
}
type MouseMovePayload = {
  containerRect: DOMRect
  clientX: number
  clientY: number
}

export type DriverAvailabilitySchedulerEvent =
  | { type: 'timelineDragStart'; payload: MouseDownPayload }
  | { type: 'timelineDragMove'; payload: MouseMovePayload }
  | { type: 'timelineDragEnd' }
  | { type: 'windowMouseUp' }
  | {
      type: 'onTimeRangeEditorPopoverChange'
      payload: { newStart: DateTime; newEnd: DateTime; remark?: string }
    }
  | {
      type: 'onTimeRangeEditorDeleteIconClick'
    }
  | {
      type: 'onTimeRangeEditorSaveIconClick'
    }
  | {
      type: 'onTimeRangeEditorPopoverClose'
    }
  | {
      type: 'onTimeRangeEditorPopoverCloseButtonClick'
    }
  | {
      type: 'onTimelineSelectionClick'
      mouseEvent: React.MouseEvent<HTMLDivElement>
      selection: TimelineSelectionRange
    }

const EDGE_TOLERANCE_MINUTES = 6
const SCHEDULE_TYPE_ADHOC_EVENT = 'ADHOC-EVENT'

export type ExistingSchedule = {
  id: string | null
  start: DateTime
  end: DateTime
  driverId: string
  scheduleType: string
  remark?: string
}

const TIME_INCREMENT_MINUTES = 5

function ensureDateTimeNotInPast(dateTime: DateTime) {
  const now = DateTime.now()
  const isFromPreviousDay = dateTime.startOf('day') < now.startOf('day')
  return isFromPreviousDay ? now : dateTime
}

function getDateTimeFromX(
  rowTimelineWidth: number,
  xOverTimeline: number,
  startOfDay: DateTime,
  totalRangeMinutes: number,
) {
  const clampedX = R.clamp(xOverTimeline, { min: 0, max: rowTimelineWidth })
  const fraction = clampedX / rowTimelineWidth
  let offsetMin = fraction * totalRangeMinutes
  offsetMin = Math.round(offsetMin / TIME_INCREMENT_MINUTES) * TIME_INCREMENT_MINUTES
  return startOfDay.plus({ minutes: offsetMin })
}

function doesOverlap(
  aStart: DateTime,
  aEnd: DateTime,
  bStart: DateTime,
  bEnd: DateTime,
) {
  return (aStart >= bStart && aStart < bEnd) || (aEnd > bStart && aEnd <= bEnd)
}

/**
 * Clamps a selection range to avoid overlapping existing bookings.
 */
function clampSelectionToAvoidOverlap(
  resourceId: string,
  proposedStart: DateTime,
  proposedEnd: DateTime,
  schedulesMap: Map<string, Array<ExistingSchedule>>,
) {
  let start = ensureDateTimeNotInPast(proposedStart)
  let end = proposedEnd < start ? start : proposedEnd

  const schedules = schedulesMap.get(resourceId)
  if (!schedules) {
    return { start, end }
  }

  // TODO: Check if relevant
  const sameCatSchedules = schedules.sort(
    (a, b) => a.start.toMillis() - b.start.toMillis(),
  )

  for (const b of sameCatSchedules) {
    if (doesOverlap(start, end, b.start, b.end)) {
      if (start >= b.start && start < b.end) {
        const newStart = b.end
        start = newStart > end ? end : newStart
      } else if (end > b.start && end <= b.end) {
        const newEnd = b.start
        end = newEnd < start ? start : newEnd
      } else if (start <= b.start && end >= b.end) {
        const rangeCenter = start.plus({
          minutes: (end.diff(start, 'minutes').minutes || 0) / 2,
        })
        const bookingCenter = b.start.plus({
          minutes: (b.end.diff(b.start, 'minutes').minutes || 0) / 2,
        })
        if (rangeCenter < bookingCenter) {
          end = b.start
        } else {
          start = b.end
        }
        if (end < start) {
          end = start
        }
      }
    }
  }
  // TODO: End Check if relevant

  return { start, end }
}

const getRowTimelineWidth = (containerRect: DOMRect): number => containerRect.width

function processBookingSlots(
  resourceId: string | null,
  bookingSlots: Array<ExistingSchedule>,
  selection: TimelineSelectionRange,
  mode: 'delete' | 'save',
): { processedSlots: Array<ExistingSchedule> } {
  if (!resourceId || !selection.start || !selection.end) {
    return { processedSlots: bookingSlots }
  }

  const processedSlots: Array<ExistingSchedule> = []
  let hasOverlap = false

  for (const slot of bookingSlots) {
    if (
      slot.driverId === resourceId &&
      doesOverlap(selection.start, selection.end, slot.start, slot.end)
    ) {
      hasOverlap = true

      if (mode === 'save') {
        processedSlots.push({
          id: slot.id,
          start: selection.start,
          end: selection.end,
          driverId: slot.driverId,
          scheduleType: slot.scheduleType,
          remark: selection.remark ?? '',
        })
      }
    } else {
      processedSlots.push(slot)
    }
  }

  if (!hasOverlap && mode === 'save') {
    processedSlots.push({
      id: null,
      start: selection.start,
      end: selection.end,
      driverId: resourceId,
      scheduleType: SCHEDULE_TYPE_ADHOC_EVENT,
      remark: selection.remark,
    })
  }
  return { processedSlots }
}

export default function driverAvailabilitySchedulerEventHandler({
  state,
  setState: setState_,
  event,
  blockedExistingSchedules,
  startOfDay,
  totalRangeMinutes,
  isSchedulerDisabled,
  onBookingSlotsUpdated,
}: {
  state: DriverAvailabilitySchedulerState
  setState: React.Dispatch<React.SetStateAction<DriverAvailabilitySchedulerState>>
  event: DriverAvailabilitySchedulerEvent
  blockedExistingSchedules: Map<string, Array<ExistingSchedule>>
  startOfDay: DateTime
  totalRangeMinutes: number
  isSchedulerDisabled: boolean
  onBookingSlotsUpdated?: (
    updatedSlots: Array<ExistingSchedule>,
    resourceId: string | null,
  ) => void
}) {
  const setState = createBetterSetState(setState_)

  match(event)
    .with({ type: 'timelineDragStart' }, ({ payload }) => {
      const { containerRect, clientX, resourceId, resizeHandle } = payload

      if (resizeHandle === 'start' || resizeHandle === 'end') {
        setState({ dragMeta: { dragHandle: resizeHandle } })
        return
      }

      const localX = clientX - containerRect.left

      const rowTimelineWidth = getRowTimelineWidth(containerRect)

      const xOverTimeline = localX
      const clickedTime = getDateTimeFromX(
        rowTimelineWidth,
        xOverTimeline,
        startOfDay,
        totalRangeMinutes,
      )

      const adjustedClickedTime = ensureDateTimeNotInPast(clickedTime)

      const { selectionMeta } = state
      const selection = selectionMeta.value

      if (selection.start && selection.end && selection.resourceId === resourceId) {
        const distStart = Math.abs(
          selection.start.diff(adjustedClickedTime, 'minutes').minutes || 0,
        )
        const distEnd = Math.abs(
          selection.end.diff(adjustedClickedTime, 'minutes').minutes || 0,
        )
        if (distStart <= EDGE_TOLERANCE_MINUTES) {
          return setState({ dragMeta: { dragHandle: 'start' } })
        }
        if (distEnd <= EDGE_TOLERANCE_MINUTES) {
          return setState({ dragMeta: { dragHandle: 'end' } })
        }

        const olderEdge =
          selection.start < selection.end ? selection.start : selection.end
        const newerEdge =
          selection.start < selection.end ? selection.end : selection.start
        const isBetween =
          adjustedClickedTime >= olderEdge && adjustedClickedTime <= newerEdge
        if (isBetween) {
          return setState({
            dragMeta: {
              dragHandle: 'move',
              dragOffsetMinutes:
                adjustedClickedTime.diff(selection.start, 'minutes').minutes || 0,
              originalStart: selection.start,
              originalEnd: selection.end,
            },
            cursorStyle: 'grabbing',
          })
        }
      }

      setState({
        selectionMeta: {
          value: {
            start: adjustedClickedTime,
            end: adjustedClickedTime,
            resourceId: resourceId,
            rangeEditorAnchorEl: selection?.rangeEditorAnchorEl ?? null,
            remark: '',
          },
        },
        dragMeta: { dragHandle: 'new' },
      })
    })
    .with({ type: 'timelineDragMove' }, ({ payload }) => {
      const { containerRect, clientX, clientY } = payload
      const localX = clientX - containerRect.left
      const rowTimelineWidth = getRowTimelineWidth(containerRect)
      const xOverTimeline = localX
      const currentTime = getDateTimeFromX(
        rowTimelineWidth,
        xOverTimeline,
        startOfDay,
        totalRangeMinutes,
      )

      const now = DateTime.now()
      const isFromPreviousDay = currentTime.startOf('day') < now.startOf('day')

      const adjustedCurrentTime = isFromPreviousDay ? now : currentTime

      if (state.dragMeta) {
        const {
          selectionMeta: { value: selection },
          dragMeta,
        } = state
        if (!selection.end || !selection.resourceId || !selection.start) {
          return
        }

        if (dragMeta.dragHandle === 'move') {
          const { originalEnd, originalStart, dragOffsetMinutes } = dragMeta
          const lengthMin = originalEnd.diff(originalStart, 'minutes').minutes || 0
          let proposedStartMinutes =
            adjustedCurrentTime.diff(startOfDay, 'minutes').minutes - dragOffsetMinutes
          proposedStartMinutes =
            Math.round(proposedStartMinutes / TIME_INCREMENT_MINUTES) *
            TIME_INCREMENT_MINUTES
          proposedStartMinutes = R.clamp(proposedStartMinutes, {
            min: 0,
            max: totalRangeMinutes,
          })
          let newStartTime = startOfDay.plus({ minutes: proposedStartMinutes })

          // Ensure start time is not in the past
          newStartTime = ensureDateTimeNotInPast(newStartTime)

          let newEndTime = newStartTime.plus({ minutes: lengthMin })
          const endOfCalendarTime = startOfDay.plus({ minutes: totalRangeMinutes })

          if (newEndTime > endOfCalendarTime) {
            newEndTime = endOfCalendarTime
          }

          let newSelectionResourceId = selection.resourceId
          // check if we want to move it to different resource row
          const targetElement = document.elementFromPoint(
            clientX,
            clientY,
          ) as HTMLElement | null
          if (targetElement) {
            const rowElement = targetElement.closest(
              `[${DATA_RESOURCE_ID_ATTRIBUTE}]`,
            ) as HTMLElement | null
            if (rowElement) {
              const newResourceId = rowElement.getAttribute(DATA_RESOURCE_ID_ATTRIBUTE)
              if (newResourceId && newResourceId !== selection.resourceId) {
                newSelectionResourceId = newResourceId
              }
            }
          }

          const clamped = clampSelectionToAvoidOverlap(
            newSelectionResourceId,
            newStartTime,
            newEndTime,
            blockedExistingSchedules,
          )
          newStartTime = clamped.start
          newEndTime = clamped.end

          setState({
            cursorStyle: 'grabbing',
            selectionMeta: {
              value: {
                resourceId: newSelectionResourceId,
                start: newStartTime,
                end: newEndTime,
                rangeEditorAnchorEl: selection.rangeEditorAnchorEl,
                remark: selection.remark,
              },
            },
          })
          return
        }
        if (dragMeta.dragHandle === 'start') {
          const proposedStartTime =
            adjustedCurrentTime > selection.end ? selection.end : adjustedCurrentTime

          // Ensure start time is not in the past
          const notPastStartTime = ensureDateTimeNotInPast(proposedStartTime)

          const clamped = clampSelectionToAvoidOverlap(
            selection.resourceId,
            notPastStartTime,
            selection.end,
            blockedExistingSchedules,
          )
          const newStartTime = clamped.start
          const newEndTime = clamped.end

          setState({
            cursorStyle: 'ew-resize',
            selectionMeta: {
              value: {
                ...selection,
                start: newStartTime,
                end: newEndTime,
              },
            },
          })
          return
        }

        // End or new drag handle
        const proposedEndTime =
          adjustedCurrentTime < selection.start ? selection.start : adjustedCurrentTime
        const clamped = clampSelectionToAvoidOverlap(
          selection.resourceId,
          selection.start,
          proposedEndTime,
          blockedExistingSchedules,
        )
        const newStartTime = clamped.start
        const newEndTime = clamped.end

        setState({
          selectionMeta: {
            value: {
              resourceId: selection.resourceId,
              start: newStartTime,
              end: newEndTime,
              rangeEditorAnchorEl: selection.rangeEditorAnchorEl,
              remark: selection.remark,
            },
          },
          cursorStyle: dragMeta.dragHandle === 'end' ? 'ew-resize' : state.cursorStyle,
        })

        return
      }

      let newCursor = 'default'
      const { selectionMeta } = state
      const selection = selectionMeta.value
      if (selection.start && selection.end) {
        const distStart = Math.abs(
          selection.start.diff(adjustedCurrentTime, 'minutes').minutes || 0,
        )
        const distEnd = Math.abs(
          selection.end.diff(adjustedCurrentTime, 'minutes').minutes || 0,
        )
        const s = selection.start < selection.end ? selection.start : selection.end
        const eTime = selection.start < selection.end ? selection.end : selection.start
        const isBetween = adjustedCurrentTime >= s && adjustedCurrentTime <= eTime

        if (distStart <= EDGE_TOLERANCE_MINUTES || distEnd <= EDGE_TOLERANCE_MINUTES) {
          newCursor = 'ew-resize'
        } else if (isBetween) {
          newCursor = 'move'
        }
      }
      setState({ cursorStyle: newCursor })
    })
    .with({ type: 'timelineDragEnd' }, { type: 'windowMouseUp' }, () => {
      setState({
        cursorStyle: 'default',
        dragMeta: null,
      })
    })
    .with(
      { type: 'onTimeRangeEditorPopoverChange' },
      ({ payload: { newStart, newEnd, remark } }) => {
        if (!state.selectionMeta.value) {
          return
        }

        // Ensure times are not in the past
        const adjustedNewStart = ensureDateTimeNotInPast(newStart)
        const adjustedNewEnd = newEnd < adjustedNewStart ? adjustedNewStart : newEnd

        setState({
          selectionMeta: {
            value: {
              ...state.selectionMeta.value,
              start: adjustedNewStart,
              end: adjustedNewEnd,
              rangeEditorAnchorEl: state.selectionMeta.value.rangeEditorAnchorEl,
              remark,
            },
          },
        })
      },
    )
    .with({ type: 'onTimeRangeEditorDeleteIconClick' }, () => {
      const resourceId = state.selectionMeta.value.resourceId ?? ''
      const bookingSlots = blockedExistingSchedules.get(resourceId) ?? []

      const { processedSlots } = processBookingSlots(
        resourceId,
        bookingSlots,
        state.selectionMeta.value,
        'delete',
      )

      if (onBookingSlotsUpdated) {
        onBookingSlotsUpdated(processedSlots, resourceId)
      }

      setState({
        selectionMeta: {
          value: {
            ...state.selectionMeta.value,
            rangeEditorAnchorEl: null,
          },
        },
      })
    })
    .with({ type: 'onTimeRangeEditorSaveIconClick' }, () => {
      const resourceId = state.selectionMeta.value.resourceId ?? ''
      const bookingSlots = blockedExistingSchedules.get(resourceId) ?? []

      const { processedSlots } = processBookingSlots(
        resourceId,
        bookingSlots,
        state.selectionMeta.value,
        'save',
      )

      if (onBookingSlotsUpdated) {
        onBookingSlotsUpdated(processedSlots, resourceId)
      }

      setState({
        selectionMeta: {
          value: {
            ...state.selectionMeta.value,
            rangeEditorAnchorEl: null,
          },
        },
      })
    })
    .with(
      { type: 'onTimeRangeEditorPopoverClose' },
      { type: 'onTimeRangeEditorPopoverCloseButtonClick' },
      () => {
        if (!state.selectionMeta.value) {
          return
        }
        setState({
          selectionMeta: {
            value: {
              ...state.selectionMeta.value,
              rangeEditorAnchorEl: null,
            },
          },
        })
      },
    )
    .with({ type: 'onTimelineSelectionClick' }, ({ mouseEvent, selection }) => {
      if (isSchedulerDisabled) {
        return
      }

      const now = DateTime.now()
      const isFromPreviousDay =
        selection.start && selection.start.startOf('day') < now.startOf('day')

      if (isFromPreviousDay) {
        return
      }

      setState({
        selectionMeta: {
          value: {
            ...selection,
            rangeEditorAnchorEl: mouseEvent.target,
          },
        },
      })
    })
    .exhaustive()
}
