import { useEffect, useMemo, useState } from 'react'
import { isEqual } from 'lodash'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  IconButton,
  Paper,
  Stack,
  styled,
  TextField,
  Typography,
} from '@karoo-ui/core'
import { useControlledForm } from '@karoo-ui/core-rhf'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import CloseIcon from '@mui/icons-material/Close'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import { format } from 'date-fns'
import { useDropzone } from 'react-dropzone'
import { Controller } from 'react-hook-form'

import DiscardModal from 'src/components/_modals/Discard'
import useModal from 'src/hooks/use-modal'
import useAppointmentFormDetailsQuery, {
  type FetchAppointmentFormDetailsQuery,
} from 'src/modules/delivery/api/appointment/useAppointmentFormDetailsQuery'
import useCreateAppointmentMutation from 'src/modules/delivery/api/appointment/useCreateAppointmentMutation'
import useGetAppointmentDetailsQuery from 'src/modules/delivery/api/appointment/useGetAppointmentDetailsQuery'
import useGetAvailableAppointmentSlotsQuery from 'src/modules/delivery/api/appointment/useGetAvailableAppointmentSlotsQuery'
import useUpdateAppointmentMutation from 'src/modules/delivery/api/appointment/useUpdateAppointmentMutation'
import { useSpecialEquipmentsOptions } from 'src/modules/delivery/api/special-equipments/useFetchDeliverySpecialEquipments'
import Snackbar from 'src/modules/delivery/utils/snackbar-configuraor'
import { ctIntl } from 'src/util-components/ctIntl'
import { Array_forEach } from 'src/util-functions/performance-critical-utils'

import useDownloadAppointmentFileMutation from '../api/appointment/useDownloadAppointmentFileMutation'
import useGetAppointmentSettingsQuery from '../api/appointment/useGetAppointmentSettingsQuery'
import { renderAppointmentFormDynamicField } from './AppointmentFormRenderer'
import {
  convertServerFilesToDisplayFiles,
  prepareFilesForSubmission,
} from './file-utils'
import {
  extractKeysAndValuesFromField,
  formatDateTimeWithTimezone,
  populateDynamicFormField,
} from './helpers'

type Props = {
  onClose: () => void
  mode: 'create' | 'edit'
  appointmentId?: number
}

type TimeSlotData = {
  displayValue: string
  fullValue: string
}

type FormData = {
  // appointmentId: string
  // pickupLocation: {
  //   addressLine1: string
  //   addressLine2: string
  //   latitude: string
  //   longitude: string
  //   latLng: string
  //   postalCode: string
  //   countryId: string
  //   phone: {
  //     countryCode: string | null
  //     number: string | null
  //   }
  //   email: string | null
  //   saveToAddressBook: boolean
  // }
  // customer: {
  //   id: string | null
  //   name: string | null
  // }
  // notes: string
  files: Array<any> // items: Array<{
  //   person: string
  //   code: string
  //   sku: string
  //   upc: string
  // }>
}

const ContentContainer = styled('div')({
  display: 'flex',
  height: '100%',
  width: '100%',
})

const MainContent = styled('div')({
  flex: 1,
  borderRight: '1px solid #e0e0e0',
  padding: '24px',
  overflowY: 'auto',
})

const SideContent = styled(Paper)({
  width: '300px',
  padding: '24px',
  display: 'flex',
  flexDirection: 'column',
})

const StepIndicator = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(4),
  '& > *': {
    marginRight: theme.spacing(2),
  },
}))

const StepCircle = styled('div')<{ active?: boolean }>(({ theme, active }) => ({
  width: 24,
  height: 24,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: active ? theme.palette.primary.main : theme.palette.grey[300],
  color: active ? theme.palette.primary.contrastText : theme.palette.text.primary,
}))

const StepText = styled(Typography)<{ active?: boolean }>(({ theme, active }) => ({
  color: active ? theme.palette.text.primary : theme.palette.text.secondary,
}))

const CalendarGrid = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(7, 1fr)',
  gap: '8px',
  marginTop: '16px',
})

const CalendarCell = styled('div')<{ selected?: boolean; disabled?: boolean }>(
  ({ theme, selected, disabled }) => ({
    padding: '8px',
    textAlign: 'center',
    borderRadius: '4px',
    cursor: disabled ? 'default' : 'pointer',
    backgroundColor: selected ? theme.palette.primary.main : 'transparent',
    color: (selected ? theme.palette.primary.contrastText : disabled)
      ? theme.palette.text.disabled
      : theme.palette.text.primary,
    '&:hover': {
      backgroundColor: !disabled && !selected ? theme.palette.action.hover : undefined,
    },
  }),
)

const CalendarContainer = styled('div')({
  display: 'flex',
  gap: '32px',
  alignItems: 'flex-start',
})

const CalendarSection = styled('div')({
  flex: 1,
})

const TimeSlotSection = styled('div')({
  width: '250px',
})

const TimeSlotGrid = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(2, 1fr)',
  gap: '8px',
})

const TimeSlot = styled(Button)<{ selected?: boolean }>(({ theme, selected }) => ({
  backgroundColor: selected ? theme.palette.primary.main : undefined,
  color: selected ? theme.palette.primary.contrastText : undefined,
  '&:hover': {
    backgroundColor: selected ? theme.palette.primary.dark : undefined,
  },
}))

const StepCircleCompleted = styled('div')(({ theme }) => ({
  width: 24,
  height: 24,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.success.main,
  color: theme.palette.primary.contrastText,
}))

const StepConnector = styled('div')<{ active?: boolean }>(({ theme, active }) => ({
  flex: 1,
  height: 1,
  backgroundColor: active ? theme.palette.primary.main : theme.palette.grey[300],
  margin: '0 8px',
}))

const SummaryItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}))

const DropzoneBox = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  textAlign: 'center',
  cursor: 'pointer',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}))

const SelectedFileItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1, 2),
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  marginTop: theme.spacing(1),
}))

const FileLink = styled('span')(({ theme }) => ({
  cursor: 'pointer',
  '&:hover': {
    textDecoration: 'underline',
    color: theme.palette.primary.main,
  },
}))

const EquipmentContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(2),
}))

const BlockingOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: theme.palette.grey[300],
  zIndex: 9999,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  backdropFilter: 'blur(2px)',
}))

const AppointmentModal = ({ onClose, mode = 'create', appointmentId }: Props) => {
  const [discardModalOpen, discardModal] = useModal(false)
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlotData | null>(null)
  const [selectedCapabilityId, setSelectedCapabilityId] = useState('')
  const [isInitialized, setIsInitialized] = useState(false)
  const [downloadingFiles, setDownloadingFiles] = useState<Record<number, boolean>>({})
  const [savedFormData, setSavedFormData] = useState<FormData | null>(null)
  const [savedDynamicFormData, setSavedDynamicFormData] =
    useState<FetchAppointmentFormDetailsQuery.DynamicForm | null>(null)
  const [timeSlots, setTimeSlots] = useState<Array<TimeSlotData>>([])
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false)

  const { data: specialEquipments, isLoading: isLoadingEquipments } =
    useSpecialEquipmentsOptions()
  const createAppointmentMutation = useCreateAppointmentMutation()
  const updateAppointmentMutation = useUpdateAppointmentMutation()
  const isLoading =
    createAppointmentMutation.isPending || updateAppointmentMutation.isPending

  const appointmentDetailsQuery = useGetAppointmentDetailsQuery(
    mode === 'edit' ? appointmentId : undefined,
  )

  const appointmentSettings = useGetAppointmentSettingsQuery()
  const downloadMutation = useDownloadAppointmentFileMutation()

  const isAppointmentCancelled = useMemo(() => {
    if (mode === 'edit' && appointmentDetailsQuery.data) {
      return (
        appointmentDetailsQuery.data.appointmentCancelReasonId !== undefined &&
        appointmentDetailsQuery.data.appointmentCancelReasonId !== null
      )
    }
    return false
  }, [mode, appointmentDetailsQuery.data])

  const cancellationReason = useMemo(() => {
    if (isAppointmentCancelled && appointmentDetailsQuery.data?.cancelReason) {
      const cancelReason = appointmentDetailsQuery.data.cancelReason as any
      if (typeof cancelReason === 'object' && cancelReason?.description) {
        return cancelReason.description
      }
      if (typeof cancelReason === 'string') {
        return cancelReason
      }
    }
    return null
  }, [isAppointmentCancelled, appointmentDetailsQuery.data?.cancelReason])

  const equipmentOptions = useMemo(() => {
    if (!specialEquipments) return []
    return specialEquipments.data || []
  }, [specialEquipments])

  const dynamicForm = useControlledForm<FetchAppointmentFormDetailsQuery.DynamicForm>({
    defaultValues: {
      formFields: [],
    },
  })

  const appointmentFormDetailsQuery = useAppointmentFormDetailsQuery()

  useEffect(() => {
    if (
      appointmentFormDetailsQuery.data &&
      dynamicForm.getValues('formFields').length === 0
    ) {
      dynamicForm.setValue('formFields', appointmentFormDetailsQuery.data)
    }
  }, [appointmentFormDetailsQuery.data, dynamicForm])

  const formFields = useMemo(
    () => appointmentFormDetailsQuery.data ?? [],
    [appointmentFormDetailsQuery.data],
  )

  const form = useControlledForm<FormData>({
    defaultValues: {
      files: [],
    },
  })

  const populatedFormFields = useMemo(() => {
    if (appointmentDetailsQuery.data && formFields.length > 0) {
      return formFields.map((field) =>
        populateDynamicFormField(field, appointmentDetailsQuery.data.properties),
      )
    }
    return []
  }, [appointmentDetailsQuery.data, formFields])

  useEffect(() => {
    if (
      mode === 'edit' &&
      appointmentDetailsQuery.data &&
      !isInitialized &&
      formFields.length > 0
    ) {
      const appointmentData = appointmentDetailsQuery.data
      if (appointmentData.windowStartTime) {
        const appointmentDate = new Date(appointmentData.windowStartTime)
        setSelectedDate(appointmentDate)
        const hours = appointmentDate.getHours().toString()
        const minutes = appointmentDate.getMinutes().toString()
        const timeString = appointmentData.windowStartTime.toString()
        const timeZone: string =
          timeString.indexOf('+') >= 0
            ? timeString.substring(timeString.indexOf('+'))
            : timeString.substring(timeString.indexOf('-'))
        const timeSlot = `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`
        setSelectedTimeSlot({
          displayValue: timeSlot,
          fullValue: `${timeSlot}:00${timeZone}`,
        })
      }
      if (appointmentData.capabilityId) {
        setSelectedCapabilityId(appointmentData.capabilityId.toString())
      }
      if (populatedFormFields.length > 0) {
        dynamicForm.setValue('formFields', populatedFormFields)
      }
      if (appointmentData.files) {
        const displayFiles = convertServerFilesToDisplayFiles(appointmentData.files)
        form.setValue('files', displayFiles, {
          shouldValidate: true,
          shouldDirty: false,
        })
      }
      setCurrentStep(mode === 'edit' ? 2 : 1)
      setIsInitialized(true)
    }
  }, [
    mode,
    appointmentDetailsQuery.data,
    isInitialized,
    formFields,
    dynamicForm,
    form,
    populatedFormFields,
  ])

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (files) => {
      const currentFiles = form.getValues('files') || []
      const newFiles = [...currentFiles, ...files]
      form.setValue('files', newFiles, { shouldValidate: true, shouldDirty: true })
    },
    accept: {
      'image/*': [],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
        '.docx',
      ],
    },
    multiple: true,
  })

  const isDownloadingAnyFile = useMemo(
    () => Object.keys(downloadingFiles).length > 0,
    [downloadingFiles],
  )

  const getAvailableSlotsInput = useMemo(
    () => ({
      capabilityId: Number(selectedCapabilityId),
      selectedDate: format(selectedDate, 'yyyy-MM-dd'),
      appointmentId: mode === 'edit' ? (appointmentId as number) : null,
    }),
    [selectedDate, selectedCapabilityId, mode, appointmentId],
  )

  const getAvailableAppointmentSlotsQuery =
    useGetAvailableAppointmentSlotsQuery(getAvailableSlotsInput)

  useEffect(() => {
    setIsLoadingTimeSlots(getAvailableAppointmentSlotsQuery.isLoading)
    if (getAvailableAppointmentSlotsQuery.data) {
      const availableSlots = getAvailableAppointmentSlotsQuery.data as Array<string>
      const formattedSlots = availableSlots.map((slot) => ({
        displayValue: slot.substring(0, 5),
        fullValue: slot,
      }))
      setTimeSlots(formattedSlots)
    }
  }, [
    getAvailableAppointmentSlotsQuery.data,
    getAvailableAppointmentSlotsQuery.isLoading,
    mode,
    selectedTimeSlot,
  ])

  useEffect(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    if (selectedDate < today) {
      setSelectedDate(today)
    }
  }, [selectedDate])

  const trigger = dynamicForm.trigger

  useEffect(() => {
    trigger()
  }, [dynamicForm.formState.isValid, trigger])

  const closeDialog = () => {
    if (isDownloadingAnyFile) return
    discardModal.close()
    onClose()
  }

  const closeDialogWithDiscardConfirmation = () => {
    if (isDownloadingAnyFile) return

    const dynamicFormValues = dynamicForm.getValues()
    if (
      form.formState.isDirty ||
      !isEqual(dynamicFormValues.formFields, populatedFormFields)
    ) {
      discardModal.open()
    } else {
      closeDialog()
    }
  }

  const handleNext = () => {
    if (isDownloadingAnyFile) return
    if (savedFormData) {
      form.reset(savedFormData)
    }

    if (savedDynamicFormData) {
      dynamicForm.reset(savedDynamicFormData)
    }
    setCurrentStep(2)
  }

  const handleBack = () => {
    if (isDownloadingAnyFile) return
    const currentFormData = form.getValues()
    const currentDynamicFormData = dynamicForm.getValues()

    setSavedFormData(currentFormData)
    setSavedDynamicFormData(currentDynamicFormData)

    setCurrentStep(1)
  }

  const buildJsonFromDynamicForm = (
    dynamicFormData: Array<FetchAppointmentFormDetailsQuery.DynamicFormField>,
  ) => {
    let jsonObject: Record<string, any> = {}
    Array_forEach(dynamicFormData, (field) => {
      jsonObject = extractKeysAndValuesFromField(field, jsonObject)
    })
    return jsonObject
  }

  const handleBookAppointment = () => {
    if (isDownloadingAnyFile) return

    dynamicForm.trigger()

    if (!dynamicForm.formState.isValid) {
      Snackbar.error('Please fill in all required fields correctly')
      return
    }

    const formData = form.getValues()
    const dynamicFormData = dynamicForm.getValues().formFields
    const dynamicAppointmentFormData = buildJsonFromDynamicForm(dynamicFormData)

    const formattedTime = formatDateTimeWithTimezone(
      selectedDate,
      selectedTimeSlot?.fullValue,
    )

    const appointmentData = {
      windowStartTime: formattedTime,
      windowEndTime: formattedTime,
      durationInMinutes: 45,
      properties: dynamicAppointmentFormData,
      files: prepareFilesForSubmission(formData.files),
      capabilityId: Number(selectedCapabilityId),
    }

    if (mode === 'edit' && appointmentId) {
      const updateData = {
        ...appointmentData,
        appointmentId: appointmentId as number,
        capabilityId: Number(selectedCapabilityId),
      }

      updateAppointmentMutation.mutate(updateData, {
        onSuccess: () => {
          onClose()
        },
      })
    } else {
      createAppointmentMutation.mutate(appointmentData, {
        onSuccess: () => {
          onClose()
        },
      })
    }
  }

  const weekDays = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']

  const generateCalendarDays = (limitDaysAhead: number | undefined) => {
    const days = []
    const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
    const lastDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const maxSelectableDate = limitDaysAhead
      ? new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate() + limitDaysAhead,
        )
      : null

    for (let i = 0; i < firstDay.getDay(); i++) {
      days.push(
        <CalendarCell
          key={`empty-${i}`}
          disabled
        />,
      )
    }

    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), day)
      date.setHours(0, 0, 0, 0)

      const isBeforeToday = date < today
      const isAfterLimit = maxSelectableDate !== null && date > maxSelectableDate
      const isOutOfRange = isBeforeToday || isAfterLimit

      const isSelected = date.getTime() === selectedDate.setHours(0, 0, 0, 0)

      days.push(
        <CalendarCell
          key={day}
          selected={isSelected && !isOutOfRange}
          disabled={isOutOfRange}
          onClick={() => {
            if (!isOutOfRange) {
              setSelectedDate(date)
              setSelectedTimeSlot(null)
            }
          }}
        >
          {day}
        </CalendarCell>,
      )
    }

    return days
  }

  const handleDownloadFile = (fileId: number, fileName: string) => {
    setDownloadingFiles((prev) => ({ ...prev, [fileId]: true }))

    downloadMutation.mutate(
      {
        appointmentFileId: fileId,
        appointmentId: appointmentId,
        appointmentFileName: fileName,
      },
      {
        onSettled: () => {
          setDownloadingFiles((prev) => {
            const updated = { ...prev }
            delete updated[fileId]
            return updated
          })
        },
      },
    )
  }

  return (
    <>
      <Dialog
        open
        onClose={() =>
          isLoading || isDownloadingAnyFile
            ? null
            : closeDialogWithDiscardConfirmation()
        }
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: '80vh',
            maxHeight: '900px',
            position: 'relative',
          },
        }}
      >
        {isDownloadingAnyFile && (
          <BlockingOverlay>
            <Stack
              alignItems="center"
              spacing={2}
            >
              <CircularProgress />
              <Typography variant="body1">
                {ctIntl.formatMessage({ id: 'Downloading file...' })}
              </Typography>
            </Stack>
          </BlockingOverlay>
        )}
        <ContentContainer>
          <MainContent>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              mb={3}
            >
              <Typography variant="h5">
                {ctIntl.formatMessage({
                  id: mode === 'create' ? 'Add New Appointment' : 'Edit Appointment',
                })}
              </Typography>
              <IconButton
                onClick={closeDialogWithDiscardConfirmation}
                disabled={isDownloadingAnyFile}
              >
                <CloseIcon />
              </IconButton>
            </Stack>
            <StepIndicator>
              {currentStep === 1 ? (
                <StepCircle active>1</StepCircle>
              ) : (
                <StepCircleCompleted>✓</StepCircleCompleted>
              )}
              <StepText active={currentStep === 1}>
                {ctIntl.formatMessage({ id: 'Select time slot' })}
              </StepText>
              <StepConnector active={currentStep === 2} />
              <StepCircle active={currentStep === 2}>2</StepCircle>
              <StepText active={currentStep === 2}>
                {ctIntl.formatMessage({ id: 'Fill Appointment Details' })}
              </StepText>
            </StepIndicator>
            {currentStep === 1 && (
              <Stack spacing={3}>
                <Box>
                  <EquipmentContainer>
                    {isLoadingEquipments ? (
                      <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        p={1}
                        width="100%"
                      >
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <Autocomplete
                        fullWidth
                        size="small"
                        options={equipmentOptions}
                        getOptionLabel={(option) => option.label}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder={ctIntl.formatMessage({
                              id: 'Special Equipment',
                            })}
                          />
                        )}
                        value={
                          equipmentOptions.find(
                            (option) =>
                              option.value.toString() === selectedCapabilityId,
                          ) || null
                        }
                        onChange={(_, newValue) => {
                          setSelectedCapabilityId(
                            newValue ? newValue.value.toString() : '',
                          )
                          setSelectedTimeSlot(null)
                        }}
                        noOptionsText={ctIntl.formatMessage({
                          id: 'No equipment matches',
                        })}
                        filterOptions={(options, params) => {
                          if (!params.inputValue) return options
                          return options.filter((option) =>
                            option.label
                              .toLowerCase()
                              .includes(params.inputValue.toLowerCase()),
                          )
                        }}
                        disableListWrap
                      />
                    )}
                  </EquipmentContainer>
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    sx={{ mt: 1, display: 'block' }}
                  >
                    {ctIntl.formatMessage({
                      id: "The available timeslots are dependent on this field. Select a special equipment if it's appropriate.",
                    })}
                  </Typography>
                </Box>
                <CalendarContainer>
                  <CalendarSection>
                    <Stack
                      direction="row"
                      alignItems="center"
                      justifyContent="space-between"
                      mb={2}
                    >
                      <Typography variant="h6">
                        {format(selectedDate, 'MMMM yyyy')}
                      </Typography>
                      <Stack
                        direction="row"
                        spacing={1}
                      >
                        <IconButton
                          onClick={() =>
                            setSelectedDate(
                              new Date(
                                selectedDate.setMonth(selectedDate.getMonth() - 1),
                              ),
                            )
                          }
                        >
                          <ChevronLeftIcon />
                        </IconButton>
                        <IconButton
                          onClick={() =>
                            setSelectedDate(
                              new Date(
                                selectedDate.setMonth(selectedDate.getMonth() + 1),
                              ),
                            )
                          }
                        >
                          <ChevronRightIcon />
                        </IconButton>
                      </Stack>
                    </Stack>
                    {appointmentSettings.isLoading ? (
                      <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        p={1}
                        width="100%"
                      >
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <CalendarGrid>
                        {weekDays.map((day) => (
                          <Typography
                            key={day}
                            align="center"
                            color="textSecondary"
                          >
                            {day}
                          </Typography>
                        ))}
                        {generateCalendarDays(
                          appointmentSettings.data?.allowAdvanceBookingDays,
                        )}
                      </CalendarGrid>
                    )}
                  </CalendarSection>
                  <TimeSlotSection>
                    <Typography
                      variant="h6"
                      sx={{ mb: 2 }}
                    >
                      {format(selectedDate, 'EEE, dd MMM yyyy')}
                    </Typography>
                    {isLoadingTimeSlots ? (
                      <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        p={1}
                        width="100%"
                      >
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <TimeSlotGrid>
                        {timeSlots.map((slot) => (
                          <TimeSlot
                            key={slot.fullValue}
                            variant="outlined"
                            selected={selectedTimeSlot?.fullValue === slot.fullValue}
                            onClick={() => setSelectedTimeSlot(slot)}
                          >
                            {slot.displayValue}
                          </TimeSlot>
                        ))}
                      </TimeSlotGrid>
                    )}
                  </TimeSlotSection>
                </CalendarContainer>
              </Stack>
            )}
            {currentStep === 2 && (
              <Stack spacing={3}>
                {appointmentDetailsQuery.isLoading && mode === 'edit' ? (
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    p={3}
                  >
                    <CircularProgress />
                  </Box>
                ) : (
                  <>
                    {isAppointmentCancelled && (
                      <Box
                        sx={{
                          p: 2,
                          borderRadius: 1,
                          border: '1px solid',
                          borderColor: 'error.main',
                        }}
                      >
                        <Typography
                          variant="body2"
                          color="error.dark"
                          sx={{ fontWeight: 'medium', mb: 1 }}
                        >
                          {ctIntl.formatMessage({
                            id: 'This appointment has been cancelled and cannot be modified.',
                          })}
                        </Typography>
                        {cancellationReason && (
                          <Typography
                            variant="body2"
                            color="error.dark"
                            sx={{ fontStyle: 'italic' }}
                          >
                            {ctIntl.formatMessage({ id: 'Reason:' })}{' '}
                            {cancellationReason}
                          </Typography>
                        )}
                      </Box>
                    )}
                    {formFields
                      .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0))
                      .map((item, index) =>
                        renderAppointmentFormDynamicField(
                          item,
                          dynamicForm,
                          `formFields.${index}`,
                        ),
                      )}
                    <Box>
                      <Typography
                        variant="subtitle2"
                        gutterBottom
                      >
                        {ctIntl.formatMessage({ id: 'Upload Files' })}
                      </Typography>
                      <DropzoneBox {...getRootProps()}>
                        <input {...getInputProps()} />
                        <Stack
                          spacing={1}
                          alignItems="center"
                        >
                          <UploadFileIcon
                            fontSize="large"
                            color="action"
                          />
                          <Typography>
                            {ctIntl.formatMessage({
                              id: 'mifleet.imports.file.drop.title',
                            })}
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                          >
                            {ctIntl.formatMessage({
                              id: 'mifleet.imports.file.drop.browse',
                            })}
                          </Button>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                          >
                            {ctIntl.formatMessage(
                              { id: 'global.upload.supportedFormats' },
                              { values: { formats: 'Images, PDF, DOC, DOCX' } },
                            )}
                          </Typography>
                        </Stack>
                      </DropzoneBox>
                      <Controller
                        name="files"
                        control={form.control}
                        render={({ field: { value } }) => (
                          <Stack sx={{ mt: 2 }}>
                            {value?.map((file: any, index: number) => (
                              <SelectedFileItem key={`${file.name}-${index}`}>
                                <Stack
                                  direction="row"
                                  spacing={1}
                                  alignItems="center"
                                >
                                  <UploadFileIcon
                                    fontSize="small"
                                    color="action"
                                  />
                                  {file.isServerFile && file.appointment_file_id ? (
                                    <FileLink
                                      onClick={() =>
                                        handleDownloadFile(
                                          file.appointment_file_id,
                                          file.name,
                                        )
                                      }
                                    >
                                      <Typography
                                        variant="body2"
                                        noWrap
                                        sx={{ maxWidth: '300px' }}
                                      >
                                        {file.name}
                                      </Typography>
                                    </FileLink>
                                  ) : (
                                    <Typography
                                      variant="body2"
                                      noWrap
                                      sx={{ maxWidth: '300px' }}
                                    >
                                      {file.name}
                                    </Typography>
                                  )}
                                </Stack>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const newFiles = value.filter(
                                      (_: any, i: number) => i !== index,
                                    )
                                    form.setValue('files', newFiles, {
                                      shouldValidate: true,
                                      shouldDirty: true,
                                    })
                                  }}
                                >
                                  <CloseIcon fontSize="small" />
                                </IconButton>
                              </SelectedFileItem>
                            ))}
                          </Stack>
                        )}
                      />
                    </Box>
                  </>
                )}
              </Stack>
            )}
          </MainContent>
          <SideContent>
            <Typography
              variant="h6"
              sx={{ mb: 3 }}
            >
              {ctIntl.formatMessage({ id: 'Summary' })}
            </Typography>
            <Box sx={{ flex: 1 }}>
              <SummaryItem>
                <Typography
                  variant="subtitle2"
                  color="textSecondary"
                >
                  {ctIntl.formatMessage({ id: 'Duration' })}
                </Typography>
                <Typography variant="body1">
                  {ctIntl.formatMessage({ id: '45 minutes' })}
                </Typography>
              </SummaryItem>
              {selectedCapabilityId && (
                <SummaryItem>
                  <Typography
                    variant="subtitle2"
                    color="textSecondary"
                  >
                    {ctIntl.formatMessage({ id: 'Special Equipment' })}
                  </Typography>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={1}
                  >
                    <Typography variant="body1">
                      {equipmentOptions.find(
                        (eq) => eq.value.toString() === selectedCapabilityId,
                      )?.label || ''}
                    </Typography>
                  </Stack>
                </SummaryItem>
              )}
              {selectedTimeSlot && (
                <SummaryItem>
                  <Typography
                    variant="subtitle2"
                    color="textSecondary"
                  >
                    {ctIntl.formatMessage({ id: 'Scheduled Arrival' })}
                  </Typography>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={1}
                  >
                    <Typography variant="body1">
                      {selectedTimeSlot?.displayValue},{' '}
                      {format(selectedDate, 'EEE, dd MMM yyyy')}
                    </Typography>
                  </Stack>
                </SummaryItem>
              )}
              {currentStep === 2 && (
                <SummaryItem>
                  <Typography
                    variant="subtitle2"
                    color="textSecondary"
                  >
                    {ctIntl.formatMessage({ id: 'Form Status' })}
                  </Typography>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={1}
                  >
                    {dynamicForm.formState.isValid ? (
                      <Typography
                        variant="body1"
                        color="success.main"
                      >
                        {ctIntl.formatMessage({ id: 'Valid' })}
                      </Typography>
                    ) : (
                      <Typography
                        variant="body1"
                        color="error"
                      >
                        {ctIntl.formatMessage({ id: 'Incomplete' })}
                      </Typography>
                    )}
                  </Stack>
                </SummaryItem>
              )}
            </Box>
            <Stack
              direction="row"
              spacing={2}
              sx={{ mt: 'auto' }}
            >
              {currentStep === 2 ? (
                <>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleBack}
                    disabled={
                      isLoading || isDownloadingAnyFile || isAppointmentCancelled
                    }
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Back' })}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleBookAppointment}
                    disabled={
                      isLoading ||
                      !dynamicForm.formState.isValid ||
                      isDownloadingAnyFile ||
                      isAppointmentCancelled
                    }
                    loading={
                      createAppointmentMutation.isPending ||
                      updateAppointmentMutation.isPending
                    }
                    fullWidth
                  >
                    {ctIntl.formatMessage({
                      id: mode === 'create' ? 'Book Appointment' : 'Update Appointment',
                    })}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={closeDialogWithDiscardConfirmation}
                    disabled={isLoading || isDownloadingAnyFile}
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Close' })}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={!selectedTimeSlot || isDownloadingAnyFile}
                    fullWidth
                  >
                    {ctIntl.formatMessage({ id: 'Next' })}
                  </Button>
                </>
              )}
            </Stack>
          </SideContent>
        </ContentContainer>
      </Dialog>
      {discardModalOpen && (
        <DiscardModal
          open
          onClose={discardModal.close}
          onConfirm={closeDialog}
        />
      )}
    </>
  )
}

export default AppointmentModal
