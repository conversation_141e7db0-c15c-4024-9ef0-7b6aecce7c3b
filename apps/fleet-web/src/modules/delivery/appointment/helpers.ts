import * as R from 'remeda'

import type { FetchAppointmentFormDetailsQuery } from 'src/modules/delivery/api/appointment/useAppointmentFormDetailsQuery'

export const formatDateTimeWithTimezone = (
  date: Date,
  timeSlot: string | undefined,
) => {
  if (!timeSlot) return null

  const timeParts = timeSlot.split(':')
  const hours = timeParts[0].padStart(2, '0')
  const minutes = timeParts.length > 1 ? timeParts[1].padStart(2, '0') : '00'
  let timeZone: string =
    timeSlot.indexOf('+') >= 0
      ? timeSlot.substring(timeSlot.indexOf('+'))
      : timeSlot.substring(timeSlot.indexOf('-'))
  timeZone = timeZone.includes(':') ? timeZone : timeZone + ':00'

  const dateWithTime = new Date(date)
  dateWithTime.setHours(
    timeSlot?.includes('PM') && hours !== '12' ? parseInt(hours) + 12 : parseInt(hours),
    parseInt(minutes),
    0,
    0,
  )

  return `${dateWithTime.getFullYear()}-${String(dateWithTime.getMonth() + 1).padStart(
    2,
    '0',
  )}-${String(dateWithTime.getDate()).padStart(2, '0')}T${String(
    dateWithTime.getHours(),
  ).padStart(2, '0')}:${String(dateWithTime.getMinutes()).padStart(
    2,
    '0',
  )}:00${timeZone}`
}

export const populateDynamicFormField = (
  field: FetchAppointmentFormDetailsQuery.DynamicFormField,
  properties: Record<string, any>,
): FetchAppointmentFormDetailsQuery.DynamicFormField => {
  const fieldCopy = JSON.parse(JSON.stringify(field))

  switch (fieldCopy.type) {
    case 'text':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.TextFieldAttributes
        ).value = properties[fieldCopy.name]
      }
      break

    case 'auto-complete':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
        ).value = properties[fieldCopy.name]
      } else {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
        ).value = ''
      }
      break

    case 'date-picker':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.DatePickerFieldAttributes
        ).value = properties[fieldCopy.name]
      }
      break

    case 'customer-search':
      if (properties['customer']) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
        ).customer = properties['customer']
      }
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
        ).address = properties[fieldCopy.name]
      }
      break

    case 'grouped-fields':
      if (properties[fieldCopy.name] && R.isArray(properties[fieldCopy.name])) {
        const groupedFieldsAttr =
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.GroupedFieldsAttributes

        groupedFieldsAttr.groupedFieldsValues = properties[fieldCopy.name].map(
          (item: Record<string, any>, index: number) => {
            const groupedFieldValue = groupedFieldsAttr.groupedFieldsValues?.[
              index
            ] || {
              groupedFieldList: JSON.parse(
                JSON.stringify(groupedFieldsAttr.groupedFields),
              ),
            }

            groupedFieldValue.groupedFieldList = groupedFieldValue.groupedFieldList.map(
              (groupField: FetchAppointmentFormDetailsQuery.DynamicFormField) =>
                populateDynamicFormField(groupField, item),
            )

            return groupedFieldValue
          },
        )
      }
      break
  }

  return fieldCopy
}

export const extractKeysAndValuesFromField = (
  field: FetchAppointmentFormDetailsQuery.DynamicFormField,
  jsonObject: Record<string, any>,
) => {
  const obj: Record<string, any> = jsonObject
  switch (field.type) {
    case 'text':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.TextFieldAttributes
      ).value
      break
    case 'auto-complete':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
      ).value
      break
    case 'date-picker':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.DatePickerFieldAttributes
      ).value
      break
    case 'customer-search':
      obj['customer'] = (
        field.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
      ).customer
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
      ).address
      break
    case 'grouped-fields': {
      const groupedValues = (
        field.attributes as FetchAppointmentFormDetailsQuery.GroupedFieldsAttributes
      ).groupedFieldsValues

      const result: Array<Record<string, any>> = []

      for (const groupedFieldList of groupedValues) {
        const childJsonObject: Record<string, any> = {}

        for (const groupedField of groupedFieldList.groupedFieldList) {
          extractKeysAndValuesFromField(groupedField, childJsonObject)
        }

        result.push(childJsonObject)
      }

      obj[field.name] = result
      break
    }
  }
  return obj
}
