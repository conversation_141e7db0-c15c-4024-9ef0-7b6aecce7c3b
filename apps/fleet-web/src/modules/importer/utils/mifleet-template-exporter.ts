const ImportTemplateData = {
  'FUEL-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier*',
      'Date',
      'Vehicle*',
      'Description',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value*',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Fuel Card',
      'Driver',
      'Transaction Type',
      'Fuelling Station*',
      'Fuelling Date*',
      'Fuelling Time*',
      'Fuel Tank Full',
      'Odometer Reading at Fuelling',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note,Receipt"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'Fuel999',
        '4': 'Fuel Supplier',
        '5': '11/10/2021',
        '6': 'CTT_05-OG-40',
        '7': 'Truck fuel',
        '8': '1',
        '9': '1000',
        '10': '10000',
        '11': '0.15',
        '12': '0',
        '13': '1000',
        '14': '0',
        '15': '11000',
        '16': '****-****-****-1234',
        '17': 'Akile Mogoshoa',
        '18': 'Business',
        '19': 'Fuel Retail',
        '20': '17/11/2021',
        '21': '09:30',
        '22': 'Yes',
        '23': '140332',
      },
    ],
  },
  'FINE-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier*',
      'Date',
      'Driver*',
      'Vehicle*',
      'Description*',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Fine Type*',
      'Fine Location',
      'Infringement Date*',
      'Infringement Time*',
      'Infringement Number*',
      'Payment Due',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'Fine123',
        '4': 'Police Dep 6th Street',
        '5': '11/10/2021',
        '6': 'John',
        '7': 'CTT_05-OG-40',
        '8': 'Truck traffic fine',
        '9': '1',
        '10': '1000',
        '11': '10000',
        '12': '0.15',
        '13': '0',
        '14': '1000',
        '15': '0',
        '16': '11000',
        '17': 'Speeding',
        '18': 'Delhi Street, 8, Belfast',
        '19': '11/10/2021',
        '20': '09:30',
        '21': '123',
        '22': '12/10/2021',
      },
    ],
  },
  'ACCIDENTS-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier',
      'Date',
      'Vehicle*',
      'Detailed Accident Description',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Accident Type*',
      'Driver*',
      'Location of Accident',
      'Accident Date*',
      'Accident Time*',
      'Accident Loss Value',
      'Case/Process Number',
      'Insurance Claim Number',
      'Third Party Name 1',
      'Identification 1',
      'Contact 1',
      'Notes 1',
      'Third Party Name 2',
      'Identification 2',
      'Contact 2',
      'Notes 2',
      'Third Party Name 3',
      'Identification 3',
      'Contact 3',
      'Notes 3',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'AccGP12',
        '4': '',
        '5': '22/06/2021',
        '6': 'GBH 5822 X',
        '7': 'Front Smash Accident',
        '8': '1',
        '9': '1000',
        '10': '10000',
        '11': '0.15',
        '12': '0',
        '13': '100',
        '14': '0',
        '15': '11000',
        '16': 'Large Accident with major damage',
        '17': 'Akile Mogoshoa',
        '18': 'Randburg',
        '19': '21/07/2021',
        '20': '09:30',
        '21': '10000',
        '22': '12345',
        '23': 'ABC125D254',
        '24': 'Front axle',
        '25': '1254587458214580',
        '26': '821234569',
        '27': '',
        '28': '',
        '29': '',
        '30': '',
        '31': '',
        '32': '',
        '33': '',
        '34': '',
        '35': '',
      },
    ],
  },
  SUPPLIER: {
    headers: [
      'Supplier*',
      'VAT Number*',
      'Supplier Type',
      'Email',
      'Telephone',
      'Telefax',
      'Contact Person',
      'Address1',
      'Address2',
      'Address3',
      'Postal Code',
    ],
    data: [
      {
        '1': 'J and S Auto General',
        '2': '*********',
        '3': 'Auto Parts',
        '4': '<EMAIL>',
        '5': '*********',
        '6': '',
        '7': 'John Doe',
        '8': '',
        '9': '',
        '10': '',
        '11': '',
      },
    ],
  },
  'MAINTENANCE-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier',
      'Date',
      'Vehicle*',
      'General Ledger (GL) Code',
      'Description',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Odometer',
      'Maintenance Date*',
      'Maintenance Type*',
      'Maintenance Budget',
      'Vehicle MMV (Make/Model/Variant)',
      'Job Card Reference',
      'Driver',
      'Fleet Controller',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'Serv123',
        '4': 'J and S Auto General',
        '5': '10/11/2021',
        '6': 'GBH 5822 X',
        '7': '',
        '8': 'Painting of bumpers',
        '9': '1',
        '10': '1000',
        '11': '10000',
        '12': '0.15',
        '13': '0',
        '14': '1000',
        '15': '0',
        '16': '11000',
        '17': '0',
        '18': '10/11/2021',
        '19': 'Paiting',
        '20': '150',
        '21': 'Dodge Journey 3.6',
        '22': 'PO1234',
        '23': 'John Doe',
        '24': 'Susan Dow',
      },
    ],
  },
  'TOLL-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier',
      'Date',
      'Vehicle*',
      'Description',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Passage Name*',
      'Toll Entry Date',
      'Toll Entry Time',
      'Toll Exit Date',
      'Toll Exit Time',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'TollGP123',
        '4': 'De Hoek Plaza',
        '5': '11/10/2021',
        '6': 'GBH 5822 X',
        '7': 'Trip 1',
        '8': '1',
        '9': '1000',
        '10': '10000',
        '11': '0.15',
        '12': '0',
        '13': '1000',
        '14': '0',
        '15': '11000',
        '16': 'A1 Cascais',
        '17': '11/10/2021',
        '18': '09:30',
        '19': '11/10/2021',
        '20': '09:31',
      },
    ],
  },
  'FUEL-VALIDATION-GLOBAL': {
    headers: [
      'Vehicle*',
      'Driver',
      'Fuel Date*',
      'Fuel Time*',
      'Fuel Station Name',
      'Fuel Station Address',
    ],
    data: [
      {
        '1': 'CTT_05-OG-40',
        '2': 'John Doe',
        '3': '22/06/2021',
        '4': '10:00',
        '5': 'Test Merchant',
        '6': '2 Test Road, Portugal',
      },
    ],
  },
  'TYRES-GLOBAL': {
    headers: [
      'Document Type',
      'Document Status',
      'Document Number',
      'Supplier',
      'Date',
      'Vehicle*',
      'Description',
      'Quantity*',
      'Unit Price*',
      'Net Value*',
      'Tax Value',
      'mifleet.imports.mapping.match.field.tax_deductable_value',
      'mifleet.imports.mapping.match.field.tax_non_deductable_value',
      'Discount',
      'Total Value*',
      'Tyre Operation*',
      'Additional Notes',
      'Odometer',
      'Tyres Location 1',
      'Brand 1',
      'Size 1',
      'Code 1',
      'Tyres Location 2',
      'Brand 2',
      'Size 2',
      'Code 2',
    ],
    data: [
      {
        '1': ['"Invoice,Credit Note,Debit Note"'],
        '2': ['"Paid,Overdue Payment,Validated,Pending"'],
        '3': 'AccGP12',
        '4': '',
        '5': '22/06/2021',
        '6': 'GBH 5822 X',
        '7': 'Tyre Replacement',
        '8': '4',
        '9': '1000',
        '10': '40000',
        '11': '0.1',
        '12': '0',
        '13': '0',
        '14': '0',
        '15': '44000',
        '16': 'Exchange Tyres',
        '17': 'We had to replace all tyres',
        '18': '0',
        '19': 'Front Axle',
        '20': 'Bridgestone',
        '21': '16 inch',
        '22': 'BRDG123',
        '23': '',
        '24': '',
        '25': '',
        '26': '',
      },
    ],
  },
  'CONTRACT-INSURANCE-GLOBAL': {
    headers: [
      'Contract Description',
      'Creation Date*',
      'Vehicle*',
      'Supplier*',
      'Insurance Type*',
      'Contract Start Date*',
      'Contract End Date*',
      'Policy number',
      'Odometer',
      'Franchise Percentage',
      'Franchise Value',
      'NET Value*',
      'Tax Value*',
      'Total Value*',
    ],
    data: [
      {
        '1': 'Description',
        '2': '11/10/2024',
        '3': 'CTT_05-OG-40',
        '4': 'J and S Auto General',
        '5': 'Vehicle Insurance',
        '6': '17/10/2024',
        '7': '17/10/2025',
        '8': 'f3fa6d23-ab23-23d3-bca12-0a1111c1e11b',
        '9': '1000',
        '10': '0.05',
        '11': '1000',
        '12': '10000',
        '13': '0.05',
        '14': '1050',
      },
    ],
  },
  'CONTRACT-FUEL-CARD-GLOBAL': {
    headers: [
      'Contract Description',
      'Creation Date*',
      'Vehicle*',
      'Supplier*',
      'Card Number*',
      'Contract Start Date*',
      'Contract End Date*',
      'Odometer',
    ],
    data: [
      {
        '1': 'Description',
        '2': '11/10/2024',
        '3': 'CTT_05-OG-40',
        '4': 'J and S Auto General',
        '5': '123456',
        '6': '17/10/2024',
        '7': '17/10/2025',
        '8': '1000',
      },
    ],
  },
  'CONTRACT-FINANCING-GLOBAL': {
    headers: [
      'Contract Description',
      'Creation Date*',
      'Vehicle*',
      'Supplier*',
      'Financing Type*',
      'Contract Start Date*',
      'Contract End Date*',
      'Residual Value',
      'Interest',
      'Odometer',
      'Odometer Limit',
      'NET Value*',
      'Tax Value*',
      'GL Code',
      'Total Value*',
    ],
    data: [
      {
        '1': 'Description',
        '2': '11/10/2024',
        '3': 'CTT_05-OG-40',
        '4': 'J and S Auto General',
        '5': 'Fleet financing',
        '6': '17/10/2024',
        '7': '17/10/2025',
        '8': '100',
        '9': '2',
        '10': '1000',
        '11': '2000',
        '12': '1000',
        '13': '0.05',
        '14': '123',
        '15': '1050',
      },
    ],
  },
  'CONTRACT-MAINTENANCE-GLOBAL': {
    headers: [
      'Contract Description',
      'Creation Date*',
      'Vehicle*',
      'Supplier*',
      'Maintenance Type*',
      'Contract Start Date*',
      'Contract End Date*',
      'Waranty Date*',
      'Waranty Odometer',
      'Service Interval Months',
      'Service Interval Odometer',
      'NET Value*',
      'Tax Value*',
      'Total Value*',
    ],
    data: [
      {
        '1': 'Description',
        '2': '11/10/2024',
        '3': 'CTT_05-OG-40',
        '4': 'J and S Auto General',
        '5': 'Engine',
        '6': '17/10/2024',
        '7': '17/10/2025',
        '8': '17/10/2024',
        '9': '10000',
        '10': '2',
        '11': '5000',
        '12': '1000',
        '13': '0.05',
        '14': '1050',
      },
    ],
  },
}

export { ImportTemplateData }
