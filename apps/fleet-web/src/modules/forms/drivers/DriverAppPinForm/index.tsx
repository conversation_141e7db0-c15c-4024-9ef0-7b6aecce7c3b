import { Box, KarooFormStateContextProvider, Tooltip } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { useWatch, type UseFormReturn } from 'react-hook-form'
import { z } from 'zod/v4'

import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'

const driverAppPinSchema = z
  .string()
  .refine((val) => val === '' || /^\d+$/.test(val), {
    message: 'Driver App PIN must only contain digits',
  })
  .refine((val) => val === '' || val.length >= 4, {
    message: 'PIN must contain at least 4 digits',
  })
  .refine((val) => val === '' || val.length <= 8, {
    message: 'PIN must contain at most 8 digits',
  })

export const getDriverAppPinSchema = ({
  isRequired = false,
}: {
  isRequired?: boolean
} = {}) =>
  z
    .object({
      driverAppPin: driverAppPinSchema.nullable(),
      confirmDriverAppPin: driverAppPinSchema.nullable(),
    })
    .refine(
      (value) => {
        if (isRequired) {
          return value.driverAppPin !== null && value.driverAppPin !== ''
        }

        return true
      },
      {
        message: messages.required,
        path: ['driverAppPin'],
      },
    )
    .refine((data) => data.driverAppPin === data.confirmDriverAppPin, {
      message: 'Driver App PINs do not match',
      path: ['confirmDriverAppPin'],
    })

export type DriverAppPinSchema = z.infer<ReturnType<typeof getDriverAppPinSchema>>

type Props = {
  form: UseFormReturn<DriverAppPinSchema>
  hasDriverAppPin?: boolean
  isRequired?: boolean
  isEditing: boolean
}

const DriverAppPinForm = ({
  form: { control, trigger },
  hasDriverAppPin = false,
  isRequired = false,
  isEditing,
}: Props) => {
  const driverAppPin = useWatch({ control, name: 'driverAppPin' })
  const confirmDriverAppPin = useWatch({ control, name: 'confirmDriverAppPin' })

  return (
    <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
      <Box
        sx={{
          gap: 2,
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
        }}
      >
        <TextFieldControlled
          required={isRequired}
          ControllerProps={{
            name: 'driverAppPin',
            control: control,
          }}
          {...(hasDriverAppPin ? { value: driverAppPin ?? '****' } : {})}
          onChange={(e, { controller }) => {
            controller.onChange(e.target.value.replace(/\*/g, ''))
            trigger('confirmDriverAppPin')
          }}
          label={
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'delivery.driverAppPinForm.tooltip',
              })}
            >
              <Box sx={{ display: 'inline-flex', alignItems: 'center', gap: 0.5 }}>
                <span>
                  {ctIntl.formatMessage({ id: 'delivery.driverAppPinForm.pinField' })}
                </span>
                <InfoOutlinedIcon sx={{ fontSize: '16px' }} />
              </Box>
            </Tooltip>
          }
          type="password"
          autoComplete="new-password"
        />
        <TextFieldControlled
          required={isRequired}
          ControllerProps={{
            name: 'confirmDriverAppPin',
            control: control,
          }}
          {...(hasDriverAppPin ? { value: confirmDriverAppPin ?? '****' } : {})}
          onChange={(e, { controller }) => {
            controller.onChange(e.target.value.replace(/\*/g, ''))
            trigger('driverAppPin')
          }}
          label={ctIntl.formatMessage({
            id: 'delivery.driverAppPinForm.confirmPinField',
          })}
          type="password"
          autoComplete="new-password"
        />
      </Box>
    </KarooFormStateContextProvider>
  )
}

export default DriverAppPinForm
