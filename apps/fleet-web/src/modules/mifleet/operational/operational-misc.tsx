import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import type { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router'

import {
  actions,
  miFleetOperationalSelectors,
  type CostMisc,
} from 'duxs/mifleet/operational'
import { useModal } from 'src/hooks'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  fetchDocumentArrayTypes,
  getArrayTypes,
} from 'src/modules/mifleet/DocumentsEdit/slice'
import { useDeleteDocumentMutation } from 'src/modules/mifleet/lite/api/useMiFleetCost'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'

import { useMifleetFormattedNumber } from 'cartrack-ui-kit'
import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_OPTIONS } from '../components/documents/concept-types'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const { getOperationalLoading, allMisc } = miFleetOperationalSelectors

const { fetchOperationalMisc } = actions

type FuellingProps = {
  isLoading: boolean
  automationId?: string
  operationalMisc: Array<FixMeAny>
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Misc = (props: FuellingProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsMiscFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <MiscContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const MiscContent = ({
  isLoading,
  operationalMisc,
  dispatch,
  arrayTypesAllDrivers,
  costsFilters,
  setCostsFilters,
}: FuellingProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper = useDataGridColumnHelper<CostMisc>({
    filterMode: 'client',
  })

  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal<any>(false)
  const [isEditCostModalOpen, editCostModal] = useModal<any>(false)
  const [selectedRow, setSelectedRow] = useState<FixMeAny>(undefined)
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null

    dispatch(fetchDocumentArrayTypes())
    dispatch(fetchOperationalMisc({ payload: dateObject }))
  }, [dateRangeFilter, dispatch])

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null

    dispatch(fetchOperationalMisc({ payload: dateObject }))
  }, [dateRangeFilter, dispatch])

  const columns = useMemo((): Array<GridColDef<CostMisc>> => {
    const filterOptions = DOCUMENT_CONCEPT_OPTIONS.map((option) => ({
      ...option,
      label: ctIntl.formatMessage({ id: option.label }),
    })).sort((a, b) => a.label.localeCompare(b.label))

    return [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        field: 'document_date',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.document_date),
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        minWidth: 140,
        flex: 1,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id],
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driver = arrayTypesAllDrivers.find(
            (o: FixMeAny) => o.driver_id === row.driver_id,
          )
          return driver ? driver.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driver_id',
          flex: 1,
          minWidth: 140,
        },
      ),
      columnHelper.string((_, row) => row.supplier, {
        headerName: ctIntl.formatMessage({ id: 'Supplier' }),
        field: 'supplier',
        flex: 1,
        minWidth: 120,
      }),
      columnHelper.singleSelect((_, row) => row.document_concept_id, {
        headerName: ctIntl.formatMessage({ id: 'Concept Type' }),
        field: 'document_concept',
        valueOptions: filterOptions,
        flex: 1,
        minWidth: 150,
      }),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
        minWidth: 120,
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        valueFormatter: (_, row) => formatNumber(row.total_value),
        headerAlign: 'left',
        align: 'right',
        width: 150,
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View Transaction' })}
              arrow
            >
              <IconButton
                color="secondary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedRow(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize={'small'} />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ]
  }, [arrayTypesAllDrivers, columnHelper, editCostModal, formatNumber])

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array: Array<Record<string, FixMeAny>> = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const driver = arrayTypesAllDrivers.find(
        (o: FixMeAny) => o.driver_id === d.driver_id,
      )?.name
      const {
        document_date: date,
        plate: vehicle,
        document_status,
        source_id,
        supplier,
        document_concept: concept,
        total_value: grossTotal,
        description,
      } = d

      return {
        date,
        vehicle,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        driver,
        supplier,
        concept: concept
          ? ctIntl.formatMessage({
              id: concept,
            })
          : '',
        description,
        grossTotal,
      }
    })

    const header = [
      'Date',
      'Vehicle',
      'Document Status',
      'Source',
      'Driver',
      'Supplier',
      'Concept Type',
      'Description',
      'Gross Total',
    ]

    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('OtherCosts', dateRangeFilter),
      'OtherCosts',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        sx={{
          '.MuiDataGrid-row .MuiDataGrid-cell': {
            span: {
              overflow: 'hidden !important',
              textOverflow: 'ellipsis !important',
            },
          },
        }}
        Component={DataGrid}
        dataGridId="operationalMisc"
        disableRowSelectionOnClick
        loading={!operationalMisc || isLoading}
        rowSelectionModel={selectedRow ? selectedRow.document_line_id : []}
        pagination
        rows={operationalMisc}
        getRowId={useCallbackBranded(
          (row: (typeof operationalMisc)[number]) => row.document_line_id,
          [],
        )}
        columns={columns}
        onRowClick={({ row }) => {
          setSelectedRow(row)
          editCostModal.open()
        }}
        filterModel={filterModel}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Multi Cost',
          }}
        />
      )}
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Multi Cost',
              id: 'multicost',
            },
            {
              name: 'Contracts',
              id: 'contracts',
            },
          ]}
        />
      )}

      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            editCostModal.close()
          }}
          detailsCost={selectedRow}
          forceMenu={{
            name: 'Multi',
            id: 'multicost',
          }}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            editCostModal.close()
            getCostsList()
          }}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  isLoading: getOperationalLoading(state),
  operationalMisc: allMisc(state),
  arrayTypesAllDrivers: getArrayTypes(state).allDrivers,
})

export default withRouter(connect(mapStateToProps)(Misc))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
