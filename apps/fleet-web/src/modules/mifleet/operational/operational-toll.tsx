import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useC<PERSON>backBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { with<PERSON>out<PERSON>, type RouteComponentProps } from 'react-router'
import { useParams } from 'react-router-dom'

import type { MifleetReportReferredName } from 'api/types'
import { actions, miFleetOperationalSelectors } from 'duxs/mifleet/operational'
import { useModal } from 'src/hooks'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  FRAUD_STATUS_FILTER_OPTIONS,
  FRAUD_STATUS_PENDING_VALUE,
  getValidationStatusLabel,
} from 'src/modules/mifleet/components/fraudValidation/shared/helpers'
import { useDeleteDocumentMutation } from 'src/modules/mifleet/lite/api/useMiFleetCost'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { isTrue } from 'src/util-functions/validation'

import { useMifleetFormattedNumber } from 'cartrack-ui-kit'
import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_TOLL } from '../components/documents/concept-types'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import DeleteSettingsDialog from '../shared/DeleteSettingsDialog'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import type { OperationalTollTypes } from './shared/type'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const {
  allOperationalToll,
  allOperationalTollTotals,
  isOperationalTollLoading,
  allTollFraud,
  getOperationalLoading,
} = miFleetOperationalSelectors

const { fetchOperationalToll, fetchOperationalTollFraud, updateOperationalTollFraud } =
  actions

type TollProps = {
  automationId?: string
  operationalToll: Array<OperationalTollTypes.TollList>
  viewReportClick: (key: MifleetReportReferredName) => void
  isLoading: boolean
  allTollFraud: any
  detailsLoading: boolean
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Toll = (props: TollProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsTollFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <TollContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const TollContent = ({
  automationId = undefined,
  operationalToll,
  viewReportClick,
  dispatch,
  isLoading,
  costsFilters,
  setCostsFilters,
  allTollFraud: detailedFraud,
  detailsLoading,
}: TollProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper = useDataGridColumnHelper<OperationalTollTypes.TollList>({
    filterMode: 'client',
  })

  const [activeFraudDetails, setActiveFraudDetails] = useState<FixMeAny>(undefined)
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal<any>(false)
  const [isEditCostModalOpen, editCostModal] = useModal<any>(false)
  const [itemToDelete, setItemToDelete] = useState<string | undefined>(undefined)

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const { vehicleId } = useParams() as { vehicleId: FixMeAny }
  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )
  const dateObject = useMemo(
    () =>
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId },
    [dateRangeFilter, vehicleId],
  )

  useEffect(() => {
    dispatch(fetchOperationalToll({ payload: dateObject }))
  }, [dateObject, dispatch])

  const TOLL_FRAUD_STATUS_FILTER_OPTIONS = useMemo(
    () =>
      FRAUD_STATUS_FILTER_OPTIONS.map((c) => ({
        ...c,
        label: ctIntl.formatMessage({ id: c.label }),
      })).sort((a, b) => a.label.localeCompare(b.label)),
    [],
  )

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array: Array<Record<string, FixMeAny>> = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        toll_entry,
        plate,
        document_status,
        source_id,
        passage_name,
        toll_validation_status_id,
        total_value,
        supplier,
      } = d

      return {
        toll_entry: toll_entry.includes('T')
          ? DateTime.fromSQL(
              ctIntl.removeServerDateStringTimezone(toll_entry) as string,
            ).toFormat('D t')
          : DateTime.fromSQL(toll_entry).toFormat('D t'),
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        supplier,
        passage_name,
        total_value,
        status: ctIntl.formatMessage({
          id: getValidationStatusLabel(toll_validation_status_id || '') || '',
        }),
      }
    })

    const header = [
      'Toll Entry',
      'Vehicle',
      'Document Status',
      'Source',
      'Provider',
      'Location',
      'Gross Total',
      'Status',
    ]

    exportCostsToXLSX(
      header,
      data,
      getExportFileNameArray('Tolls', dateRangeFilter),
      'Tolls',
      getInitialExtraLineArray(dateRangeFilter),
    )
  }

  const handleDeleteItem = () => {
    if (itemToDelete) {
      deleteDocumentMutate(
        { document_id: itemToDelete },
        {
          onSuccess: () => {
            getCostsList()
          },
        },
      )
      setItemToDelete(undefined)
    }
  }
  const getCostsList = useCallback(() => {
    dispatch(fetchOperationalToll({ payload: dateObject }))
  }, [dateObject, dispatch])
  const columns = useMemo((): Array<GridColDef<OperationalTollTypes.TollList>> => {
    const customMaximumColumnWidth = (header: string, accessor: string): number => {
      const maxWidth = 600
      const magicSpacing = 12
      const headerText = ctIntl.formatMessage({ id: header })
      const cellLength = Math.max(
        ...operationalToll.map((row: FixMeAny) =>
          row[accessor]
            ? ctIntl.formatMessage({
                id: getValidationStatusLabel(row[accessor]) || '',
              }).length
            : 0,
        ),
        headerText.length,
      )

      return Math.min(maxWidth, cellLength * magicSpacing)
    }

    return [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Toll Entry' }),
        field: 'toll_entry',
        flex: 1,
        minWidth: 140,
        filterable: false,
        valueFormatter: (_: any, row) =>
          row.toll_entry.includes('T')
            ? DateTime.fromISO(
                row.toll_entry,
                row.toll_entry.endsWith('Z') ? { zone: 'utc' } : {},
              ).toFormat('D t')
            : DateTime.fromSQL(row.toll_entry).toFormat('D t'),
        valueGetter: (_, row) => row.toll_entry,
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id],
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.string((_, row) => row.supplier, {
        headerName: ctIntl.formatMessage({ id: 'Provider' }),
        field: 'supplier',
        flex: 1,
        minWidth: 150,
      }),
      columnHelper.string((_, row) => row.passage_name, {
        headerName: ctIntl.formatMessage({ id: 'Location' }),
        field: 'Location',
        flex: 1,
        minWidth: 150,
      }),

      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        width: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value),
        align: 'right',
        headerAlign: 'left',
      }),
      columnHelper.singleSelect(
        (_, row) =>
          row.toll_validation_status_id
            ? row.toll_validation_status_id.toString()
            : FRAUD_STATUS_PENDING_VALUE,
        {
          headerName: ctIntl.formatMessage({ id: 'Fraud Status' }),
          field: 'toll_validation_status_id',
          valueOptions: TOLL_FRAUD_STATUS_FILTER_OPTIONS,
          width: customMaximumColumnWidth('Status', 'toll_validation_status_id'),
          renderCell: ({ row }) => (
            <StatusValidation
              statusId={Number(
                row.toll_validation_status_id
                  ? row.toll_validation_status_id.toString()
                  : FRAUD_STATUS_PENDING_VALUE,
              )}
              source="operational"
            />
          ),
        },
      ),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'actions',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={`${ctIntl.formatMessage({
                id: 'Validate Transaction',
              })}, ${ctIntl.formatMessage({ id: 'View Transaction' })}`}
              arrow
            >
              <IconButton
                size={'small'}
                id={automationId ? `view-doc-${automationId}-btn` : undefined}
                color="secondary"
                onClick={(e) => {
                  e.stopPropagation()
                  dispatch(
                    fetchOperationalTollFraud({
                      document_line_id: row.document_line_id,
                    }),
                  )
                  setActiveFraudDetails(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize={'small'} />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => setItemToDelete(String(row.document_id))}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ]
  }, [
    columnHelper,
    TOLL_FRAUD_STATUS_FILTER_OPTIONS,
    operationalToll,
    formatNumber,
    automationId,
    dispatch,
    editCostModal,
  ])

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        sx={{
          '.MuiDataGrid-row .MuiDataGrid-cell': {
            span: {
              overflow: 'hidden !important',
              textOverflow: 'ellipsis !important',
            },
          },
        }}
        rowSelectionModel={
          activeFraudDetails ? activeFraudDetails.document_line_id : []
        }
        Component={DataGrid}
        dataGridId="operationalToll"
        disableRowSelectionOnClick
        loading={!operationalToll || isLoading}
        pagination
        rows={operationalToll}
        getRowId={useCallbackBranded(
          (row: (typeof operationalToll)[number]) => row.document_line_id,
          [],
        )}
        columns={columns}
        filterModel={filterModel}
        onRowClick={({ row }) => {
          dispatch(
            fetchOperationalTollFraud({
              document_line_id: row.document_line_id,
            }),
          )
          setActiveFraudDetails(row)
          editCostModal.open()
        }}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      viewReportClick('REPORT_TOLLS' as MifleetReportReferredName)
                    }
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Report',
                    })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    size="small"
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    size="small"
                    color="inherit"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Tolls Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {itemToDelete && (
        <DeleteSettingsDialog
          onClose={() => setItemToDelete(undefined)}
          onDelete={handleDeleteItem}
          labels={{
            titleLabel: 'Tolls',
          }}
        />
      )}
      {/* Edit and fraud */}
      {activeFraudDetails && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setActiveFraudDetails(undefined)
            editCostModal.close()
          }}
          detailsCost={activeFraudDetails}
          forceMenu={{
            name: 'Tolls',
            id: DOCUMENT_CONCEPT_TOLL,
          }}
          detailedFraud={detailedFraud}
          updateFraud={(validated) => {
            dispatch(
              updateOperationalTollFraud({
                document_line_id: activeFraudDetails.document_line_id,
                validated,
                fleet_check: isTrue(activeFraudDetails.fleet_check),
                dateObject,
              }),
            )
          }}
          isSuccessUpdating={() => {
            setActiveFraudDetails(undefined)
            editCostModal.close()
            getCostsList()
          }}
          fraudIsLoading={detailsLoading}
        />
      )}
      {/* Add and Import */}
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={[
            {
              name: 'Tolls',
              id: DOCUMENT_CONCEPT_TOLL,
            },
          ]}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  operationalToll: allOperationalToll(state),
  operationalTollTotals: allOperationalTollTotals(state),
  isLoading: isOperationalTollLoading(state),
  allTollFraud: allTollFraud(state),
  detailsLoading: getOperationalLoading(state),
})

export default withRouter(connect(mapStateToProps)(Toll))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
