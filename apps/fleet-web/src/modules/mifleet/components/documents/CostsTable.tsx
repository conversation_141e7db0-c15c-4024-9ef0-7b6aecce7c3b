import type React from 'react'
import { useEffect, useMemo, useState, type Dispatch, type SetStateAction } from 'react'
import { isEmpty } from 'lodash'
import {
  Box,
  Button,
  DataGrid,
  DateRangePicker,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  GridLegacy,
  GridToolbarStandardOld,
  LinearProgress,
  Radio,
  RadioGroup,
  Stack,
  styled,
  Typography,
  useCallbackBranded,
  useDataGridDateColumns,
  useSearchTextField,
  type DateRange,
  type GridColDef,
  type GridColumnVisibilityModel,
  type GridPaginationModel,
  type GridSortDirection,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import FileDownloadIcon from '@mui/icons-material/FileDownload'
import FileUploadIcon from '@mui/icons-material/FileUpload'
import type { History } from 'history'
import { DateTime } from 'luxon'
import { Controller, useForm } from 'react-hook-form'
import { generatePath, withRouter, type RouteComponentProps } from 'react-router'
import { z } from 'zod/v4'

import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import { useModal } from 'src/hooks'
import { COSTS } from 'src/modules/app/components/routes/costs'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { messages } from 'src/shared/forms/messages'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import { useMifleetFormattedNumber } from 'cartrack-ui-kit'
import trashIcon from '../../../../../assets/svg/trash_icon.svg'
import type { FetchDocuments } from '../../api/costInput/shared/types'
import useDocumentMutation from '../../api/costInput/useDocumentMutation'
import useDocuments from '../../api/costInput/useDocuments'
import setting from '../../data/documents'
import { exportCostsToXLSX } from '../../operational/shared/utils'
import { SourceFromSourceId, StatusValidation } from '../../shared/utils'

type Props = {
  tableParams: FetchDocuments.QueryParams
  setTableParams: Dispatch<SetStateAction<FetchDocuments.QueryParams>>
  isLoading: boolean
  data: FetchDocuments.Return | undefined
  history: History
  resultCount: string
} & RouteComponentProps

const formatOptions: Array<{ label: string; value: 'xlsx' | 'csv' }> = [
  {
    label: 'Export in Excel',
    value: 'xlsx',
  },
  {
    label: 'Export in CSV',
    value: 'csv',
  },
]

const style = {
  textField: {
    width: '180px',
    label: {
      lineHeight: 0.8,
    },
    input: {
      paddingTop: '3.5px',
      paddingBottom: '3.5px',
    },
    '@media (max-width: 1370px)': {
      width: '130px',
    },
  },
}
const documentSchema = z.object({
  cancelled_reason: z.string().trim().min(1, { message: messages.required }),
})
const formikSchema = z.object({
  exportType: z.string().min(1, { message: messages.required }),
})

const initialFormValues: { exportType: 'xlsx' | 'csv' | '' } = {
  exportType: 'xlsx',
}
const initialCencelForm = {
  cancelled_reason: '',
}

const CostsTable = ({
  tableParams,
  setTableParams,
  isLoading,
  data,
  history,
  resultCount,
}: Props) => {
  const { createDateColumn } = useDataGridDateColumns({
    filterMode: 'client',
  })
  const { mutate: updateDocument } = useDocumentMutation()
  const [stateRow, setStateRow] = useState<FetchDocuments.Document>()
  const [rowsCount, setRowsCount] = useState<number>(0)
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false)
  const [tblParamsExport, setTblParamsExport] =
    useState<FetchDocuments.QueryParams>(tableParams)
  const [colVisibility, setColVisibility] = useState<GridColumnVisibilityModel>({
    document_date: true,
    document_type: true,
    document_status: true,
    source_id: true,
    supplier: true,
    document_number: true,
    notes: true,
    gross_total: true,
    actions: true,
  })
  const [dateFilter, setDateFilter] = useState<DateRange<FixMeAny>>([null, null])
  const [isModalOpen, { open: openModal, close: closeModal }] = useModal()

  const searchProps = useSearchTextField('')
  const allData = useDocuments(tblParamsExport).data

  const formatNumber = useMifleetFormattedNumber()

  useEffect(() => {
    if (data && data.resultCount) {
      setRowsCount(Number(data.resultCount))
    }
  }, [data])

  useEffect(() => {
    if (dateFilter) {
      setTableParams({
        ...tableParams,
        start_date: dateFilter[0]?.toFormat('D'),
        end_date: dateFilter[1]?.toFormat('D'),
      })
    }
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateFilter])

  useEffect(() => {
    if (resultCount) {
      setTblParamsExport({
        ...tableParams,
        start: 0,
        limit: Number.parseInt(resultCount),
      })
    }
  }, [resultCount, tableParams])

  const makeTableData = useMemo(() => {
    const item = data?.documents
    setTableParams({
      ...tableParams,
      search: searchProps.value,
    })
    return item
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.documents, searchProps.value])

  const columns: Array<GridColDef<FetchDocuments.Document>> = [
    createDateColumn({
      headerName: ctIntl.formatMessage({ id: 'Date' }),
      field: 'document_date',
      minWidth: 120,
      valueGetter: (_, row) => new Date(row.document_date),
    }),
    {
      headerName: ctIntl.formatMessage({ id: 'Document Type' }),
      field: 'document_type',
      valueGetter: (_, row) => ctIntl.formatMessage({ id: row.document_type || '' }),
      flex: 1,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Status' }),
      field: 'document_status',
      valueGetter: (_, row) => row.document_status,
      renderCell: ({ row }) => (
        <StatusValidation
          statusId={row.document_status_id}
          statusName={row.document_status}
          source="costs"
        />
      ),
      minWidth: 140,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Source' }),
      field: 'source_id',
      valueGetter: (_, row) =>
        ctIntl.formatMessage({
          id: SourceFromSourceId[row.source_id],
        }),
      flex: 1,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Supplier' }),
      field: 'supplier',
      valueGetter: (_, row) => row.supplier,
      renderCell: ({ row }) => <StyledCellNoWrap>{row.supplier}</StyledCellNoWrap>,
      flex: 1,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Document Number' }),
      field: 'document_number',
      valueGetter: (_, row) => row.document_number,
      flex: 1,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Description' }),
      field: 'notes',
      valueGetter: (_, row) => row.notes,
      minWidth: 200,
      renderCell: ({ value }) => (
        <OverflowableTextTooltip>{value}</OverflowableTextTooltip>
      ),
      flex: 1,
    },
    {
      headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
      field: 'gross_total',
      valueGetter: (_, row) => Number.parseFloat(row.gross_total),
      renderCell: ({ row }) => formatNumber(row.gross_total),
      align: 'right',
      flex: 1,
    },
    {
      field: 'cancel',
      type: 'actions',
      headerName: ctIntl.formatMessage({ id: 'Cancel' }),
      width: 120,
      align: 'right',
      valueGetter: (_, row) => row.document_status_id,
      renderCell: ({ row }) => (
        <DisableHoverButton
          onClick={(
            e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
          ) => handleToggleModal(e, row)}
          disabled={row.document_status_id !== '1'}
          startIcon={<CloseIcon />}
          color="inherit"
        />
      ),
    },
  ]

  // Table Action Handlers

  const handlePaginationModelChange = (model: GridPaginationModel) => {
    const { page, pageSize } = model
    const newStart = page * tableParams.limit

    setTableParams({ ...tableParams, start: newStart, limit: pageSize })
  }

  const handleRowClick = (row: FetchDocuments.Document) => {
    const id = row.document_id
    history.push({
      pathname: generatePath(COSTS.DOCUMENTS.subRoutes.DOCUMENTS_EDIT.path, { id }),
      state: {
        originalPath: history.location?.pathname || '',
        filterValues: tableParams,
      },
    })
  }

  const handleOpenCreateForm = () => {
    history.push({
      pathname: generatePath(COSTS.LITE_IMPORT_DATA.path),
      state: {
        forceNavigation: 'capture',
        forceMenu: 'miscConcept',
      },
    })
    /*
    resetItem()

    history.push({
      pathname: COSTS.DOCUMENTS.subRoutes.DOCUMENTS_NEW.path,
      state: {
        originalPath: history?.location?.pathname || '',
        filterValues: tableParams,
      },
    })*/
  }

  type SortProps = ReadonlyArray<{
    field: string
    sort: GridSortDirection
  }>

  // Set the column to be sorted by
  const handleSort = (sortItems: SortProps) => {
    if (isEmpty(sortItems)) {
      setTableParams({
        ...tableParams,
        sort_by: [{ field: 'document_date', sort: 'desc' as const }],
        start: 0,
      })
    } else {
      setTableParams({ ...tableParams, sort_by: sortItems, start: 0 })
    }
  }

  // Open the status change modal
  const handleToggleModal = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
    row: FetchDocuments.Document,
  ) => {
    e.preventDefault()
    e.stopPropagation()
    setStateRow(row)
    openModal()
  }

  //export data
  const handleDataExport = async (values: { exportType: 'xlsx' | 'csv' | '' }) => {
    if (allData && colVisibility && columns) {
      const visibleColumns: FixMeAny = Object.keys(colVisibility).filter(
        (item) => colVisibility[item] && item !== 'cancel',
      )
      const exportedData = await allData.documents.map(
        (d: Record<string, FixMeAny>) => {
          const {
            document_date,
            document_type,
            document_status,
            source_id,
            supplier,
            document_number,
            notes,
            gross_total,
          } = d
          let data = {}

          if (visibleColumns.includes('document_date')) {
            data = {
              ...data,
              document_date: document_date
                ? DateTime.fromSQL(document_date).toFormat('D')
                : '',
            }
          }

          if (visibleColumns.includes('document_type')) {
            data = {
              ...data,
              document_type: ctIntl.formatMessage({
                id: document_type || '',
              }),
            }
          }
          if (visibleColumns.includes('document_status')) {
            data = {
              ...data,
              document_status: ctIntl.formatMessage({
                id: document_status || '',
              }),
            }
          }
          if (visibleColumns.includes('source_id')) {
            data = {
              ...data,
              source_id: ctIntl.formatMessage({
                id: SourceFromSourceId[source_id] || '',
              }),
            }
          }
          if (visibleColumns.includes('supplier')) {
            data = {
              ...data,
              supplier: supplier,
            }
          }
          if (visibleColumns.includes('document_number')) {
            data = { ...data, document_number: document_number }
          }
          if (visibleColumns.includes('notes')) {
            data = { ...data, notes: notes }
          }

          if (visibleColumns.includes('gross_total')) {
            data = { ...data, gross_total: gross_total }
          }

          return data
        },
      )

      const VisibleColumnsName: Array<string> = []
      visibleColumns.map((item: string) =>
        columns.map((field: GridColDef<FetchDocuments.Document>) => {
          if (field.field === item) {
            VisibleColumnsName.push(field.headerName as string)
          }
        }),
      )
      exportCostsToXLSX(
        VisibleColumnsName,
        exportedData,
        'Documents',
        'Documents',
        undefined,
        values.exportType,
      )
      handleDialogChange()
    }
  }

  const handleDialogChange = () => {
    setIsDialogOpen(!isDialogOpen)
  }
  const { control, handleSubmit, formState, reset } = useForm({
    resolver: zodResolverV4(documentSchema),
    mode: 'all',
    defaultValues: initialCencelForm,
  })

  // Update the document status
  const handleModalDelete = handleSubmit(({ cancelled_reason }) => {
    handleModalClose()
    if (stateRow) {
      updateDocument({ ...stateRow, cancelled_reason: cancelled_reason })
    }
  })

  // Close the status change modal
  const handleModalClose = () => {
    closeModal()
    reset()
  }
  const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    e.preventDefault()
  }

  const {
    control: controlExported,
    handleSubmit: handleSubmitExported,
    setValue: setExportedValue,
  } = useForm({
    resolver: zodResolverV4(formikSchema),
    mode: 'all',
    defaultValues: initialFormValues,
  })

  const rows = makeTableData || []

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="select-docs-row"
        loading={isLoading}
        pagination
        autoPageSize
        onPaginationModelChange={handlePaginationModelChange}
        rows={rows}
        paginationMode="server"
        rowCount={rowsCount}
        onRowClick={({ row }) => handleRowClick(row)}
        columns={columns}
        sortModel={tableParams.sort_by}
        onSortModelChange={handleSort}
        getRowId={useCallbackBranded(
          (row: (typeof rows)[number]) => row.document_id,
          [],
        )}
        disableColumnFilter
        columnVisibilityModel={colVisibility}
        disableColumnReorder
        onColumnVisibilityModelChange={(newModel) => setColVisibility(newModel)}
        slots={{ toolbar: GridToolbarStandardOld, loadingOverlay: LinearProgress }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: GridToolbarStandardOld.createProps({
            SearchTextFieldProps: searchProps,
            gridToolbarLeftContent: (
              <DateRangePicker
                value={dateFilter}
                onChange={(newValue) => {
                  setDateFilter(newValue)
                }}
                slotProps={{
                  textField: {
                    onKeyDown: onKeyDown,
                    sx: style.textField,
                  },
                }}
              />
            ),
            gridToolbarRightContent: (
              <RightToolbar>
                <TotalLabel item>
                  {resultCount && (
                    <span>
                      {resultCount} {ctIntl.formatMessage({ id: 'Total' })}
                    </span>
                  )}
                </TotalLabel>
                <Button
                  variant="outlined"
                  startIcon={<FileDownloadIcon />}
                  onClick={handleDialogChange}
                  color="inherit"
                  size="small"
                >
                  {ctIntl.formatMessage({ id: 'Export' })}
                </Button>
                <Button
                  color="inherit"
                  variant="outlined"
                  startIcon={<FileUploadIcon />}
                  size="small"
                  onClick={() =>
                    history.push({
                      pathname: generatePath(COSTS.LITE_IMPORT_DATA.path),
                      state: {
                        forceNavigation: 'import',
                        forceMenu: 'miscImport',
                      },
                    })
                  }
                >
                  {ctIntl.formatMessage({
                    id: 'Import',
                  })}
                </Button>
                <Button
                  color="inherit"
                  variant="outlined"
                  startIcon={<AddIcon />}
                  disabled={!setting.schema.editable}
                  onClick={handleOpenCreateForm}
                  size="small"
                >
                  {ctIntl.formatMessage({
                    id: 'Add Cost',
                  })}
                </Button>
              </RightToolbar>
            ),
          }),
        }}
      />
      {isDialogOpen && (
        <Dialog
          open
          onClose={handleDialogChange}
          fullWidth
          maxWidth="xs"
        >
          <DialogTitle>
            {ctIntl.formatMessage({ id: 'Select your file type' })}
          </DialogTitle>
          <DialogContent>
            <StyleForm>
              <Controller
                control={controlExported}
                name="exportType"
                render={({ field }) => (
                  <RadioGroup
                    value={field.value}
                    onChange={(event) => {
                      setExportedValue(
                        'exportType',
                        event.target.value as typeof field.value,
                        { shouldValidate: false },
                      )
                    }}
                  >
                    {formatOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        control={
                          <Radio
                            value={option.value}
                            name={option.label}
                            color="secondary"
                          />
                        }
                        label={ctIntl.formatMessage({ id: option.label })}
                      />
                    ))}
                  </RadioGroup>
                )}
              />
              <StyledActionsWrapper>
                <Button
                  variant="outlined"
                  onClick={handleDialogChange}
                  color="inherit"
                  size="large"
                  sx={{ marginRight: '5px' }}
                >
                  {ctIntl.formatMessage({ id: 'Cancel' })}
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  type={'submit'}
                  onClick={handleSubmitExported((_values) => {
                    handleDataExport(_values)
                  })}
                  size="large"
                >
                  {ctIntl.formatMessage({ id: 'Download' })}
                </Button>
              </StyledActionsWrapper>
            </StyleForm>
          </DialogContent>
        </Dialog>
      )}

      {isModalOpen && (
        <Dialog
          open
          onClose={handleModalClose}
          sx={{
            '.MuiPaper-root': {
              paddingBottom: '30px',
              maxHeight: '400px',
              maxWidth: '400px',
              width: '400px',
              height: '400px',
            },
            '.MuiFormControl-root': {
              marginTp: 0,
            },
          }}
          maxWidth="xs"
        >
          <StyledCloseButton
            variant="text"
            color="inherit"
            startIcon={<CloseIcon />}
            onClick={handleModalClose}
            size="medium"
          />

          <StyledTrash {...makeSanitizedInnerHtmlProp({ dirtyHtml: trashIcon })} />
          <DialogContentStyle>
            <Stack sx={{ gap: 2 }}>
              <StyleTypography variant="h2">
                {ctIntl.formatMessage({
                  id: "Warning! You're about to cancel a document",
                })}
              </StyleTypography>

              <Box>
                {ctIntl.formatMessage({
                  id: 'How would you like to proceed?',
                })}
              </Box>

              <TextFieldControlled
                ControllerProps={{
                  name: 'cancelled_reason',
                  control,
                }}
                label={ctIntl.formatMessage({ id: 'Cancelled Reason' })}
                autoFocus
                required
                inputProps={{
                  maxLength: 125,
                }}
              />
            </Stack>
          </DialogContentStyle>
          <DialogActionsStyle>
            <Button
              onClick={handleModalDelete}
              disabled={!formState.isValid}
              color="primary"
              variant="contained"
              sx={{
                minWidth: '154px',
              }}
            >
              {ctIntl.formatMessage({
                id: 'Cancel Document',
              })}
            </Button>
          </DialogActionsStyle>
        </Dialog>
      )}
    </>
  )
}

export default withRouter(CostsTable)

const TotalLabel = styled(GridLegacy)({
  alignItems: 'center',
  display: 'grid',
})

const StyledCellNoWrap = styled('span')({
  '&:hover': {
    textDecoration: 'underline',
  },
  a: {
    color: '#333333',
  },
  fontSize: '14px',
  width: '250px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
})

const StyleTypography = styled(Typography)({
  fontSize: '28px',
  margin: '0',
  fontWeight: 500,
  letterSpacing: 0,
  lineHeight: '33px',
})

const DialogActionsStyle = styled(DialogActions)({
  display: 'flex',
  flexDirection: 'row',
  width: '100%',
  justifyContent: 'space-around',
})

const StyledCloseButton = styled(Button)({
  position: 'absolute',
  right: '0',
  top: '8px',
  '&:hover': {
    backgroundColor: 'transparent',
  },
})

const StyledTrash = styled('div')({
  width: '80px',
  display: 'block',
  margin: '0 auto',
  paddingTop: '24px',
})

const StyleForm = styled('div')`
  margin: ${spacing[2]} 0 0 ${spacing[3]};
`
const StyledActionsWrapper = styled('div')({
  display: 'flex !important',
  width: '100%',
  justifyContent: 'flex-end !important',
})

const DisableHoverButton = styled(Button)({
  '&:hover': {
    backgroundColor: 'transparent',
  },
})

const DialogContentStyle = styled(DialogContent)({
  textAlign: 'center',
})

const RightToolbar = styled(Box)({
  display: 'flex',
  gap: '10px',
})
