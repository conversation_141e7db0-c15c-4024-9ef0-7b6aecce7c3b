import { useMemo } from 'react'
import { DateTime } from 'luxon'

import { ctIntl } from 'src/util-components/ctIntl'

import { DOCUMENT_STATUS_OPTIONS } from '../api/costInput/shared/types'
import {
  DOCUMENT_CONCEPT_FINE,
  DOCUMENT_CONCEPT_FUELLING,
  DOCUMENT_CONCEPT_INCIDENT,
  DOCUMENT_CONCEPT_MAINTENANCE,
  DOCUMENT_CONCEPT_TIRE,
  DOCUMENT_CONCEPT_TOLL,
} from '../components/documents/concept-types'

export const costCategory = [
  {
    name: 'Fuel',
    id: DOCUMENT_CONCEPT_FUELLING,
  },
  {
    name: 'Tolls',
    id: DOCUMENT_CONCEPT_TOLL,
  },
  {
    name: 'Fines',
    id: DOCUMENT_CONCEPT_FINE,
  },
  {
    name: 'Tyres',
    id: DOCUMENT_CONCEPT_TIRE,
  },
  {
    name: 'Maintenances',
    id: DOCUMENT_CONCEPT_MAINTENANCE,
  },
  {
    name: 'Accidents',
    id: DOCUMENT_CONCEPT_INCIDENT,
  },
  {
    name: 'Multi Cost',
    id: 'multicost',
  },
  {
    name: 'Contracts',
    id: 'contracts',
  },
]

// We generate this in hook instead of a normal const export to make sure luxon has been initialized with the correct timezone and locale already
export const useDefaultRangeFilterInNumber = (): [number, number] =>
  useMemo(
    () => [
      DateTime.local().minus({ days: 60 }).toSeconds(),
      DateTime.local().toSeconds(),
    ],
    [],
  )

export const DocumentStatusOptions = () =>
  DOCUMENT_STATUS_OPTIONS.map((option) => ({
    value: option.document_status_id,
    label: ctIntl.formatMessage({ id: option.label }),
  }))
