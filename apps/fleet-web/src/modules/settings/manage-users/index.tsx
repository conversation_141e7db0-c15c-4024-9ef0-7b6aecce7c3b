import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Chip,
  DataGrid,
  FormControl,
  GridActionsCellItem,
  GridLogicOperator,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Stack,
  Typography,
  useDataGridColumnHelper,
  type ChipProps,
  type GridColDef,
  type GridFilterModel,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/Edit'
import LockOutlinedIcon from '@mui/icons-material/LockOutlined'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { actions, getIsUsersListLoading, getUsersWithoutCurrentUser } from 'duxs/admin'
import { getSettings } from 'duxs/user'
import type { FetchUsers, UserNormalizedStatus } from 'src/api/admin/types'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import { updateFirstWhere } from 'src/util-functions/functional-utils'

import AddUser from './AddUser'
import EditUser, { usersModalParamsSchema } from './EditUser'

const StatusFilterOptions = [
  { label: 'All Users', value: 'all' },
  { label: 'Active Users', value: 'active' },
  { label: 'Inactive Users', value: 'deactivated' },
  { label: 'Blocked Users', value: 'blocked' },
] as const satisfies ReadonlyArray<{ label: string; value: StatusColumnValue | 'all' }>

type StatusFilterOptionValue = (typeof StatusFilterOptions)[number]['value']

const { fetchUsers } = actions

type User = FetchUsers.Return[0]

const columnsIds = {
  status: 'status',
} as const

const chipStatus = (status: UserNormalizedStatus) =>
  match(status)
    .returnType<{
      label: string
      color: ChipProps['color']
    }>()
    .with('active', () => ({ label: 'Active', color: 'success' }))
    .with('blocked', () => ({ label: 'Blocked', color: 'error' }))
    .with('deactivated', () => ({ label: 'Inactive', color: 'secondary' }))
    .exhaustive()

type StatusColumnValue = UserNormalizedStatus

type StatusFilterModelValue = StatusColumnValue | undefined

type TableFilterModel = Except<GridFilterModel, 'items'> & {
  items: Array<
    | GridFilterModel['items'][number]
    | {
        field: typeof columnsIds.status
        operator: 'is'
        value: StatusFilterModelValue
      }
  >
}

function ManageUsers() {
  const history = useHistory()
  const dispatch = useDispatch()
  const columnHelper = useDataGridColumnHelper<User>({ filterMode: 'client' })
  const users = useTypedSelector(getUsersWithoutCurrentUser)
  const loading = useTypedSelector(getIsUsersListLoading)
  const { adminUserSettings } = useTypedSelector(getSettings)
  const canCreateEdit = adminUserSettings as boolean

  const [filterModel, setFilterModel] = useState(
    (): TableFilterModel => ({
      items: [
        {
          field: columnsIds.status,
          operator: 'is',
          value: 'active' satisfies StatusColumnValue,
        },
      ],
      logicOperator: GridLogicOperator.And,
    }),
  )

  const statusFilterValue = useMemo(
    () =>
      (filterModel.items.find((item) => item.field === columnsIds.status)?.value as
        | StatusColumnValue
        | undefined) ?? ('all' as const),
    [filterModel.items],
  )

  //TODO: Circular deps for rolesModalParamsSchema
  const navigateToDetailsPage = useCallback(
    (id: string) => {
      if (canCreateEdit) {
        history.push(
          `${
            SETTINGS.subMenusRoutes.MANAGE_USERS.path
          }?${buildRouteQueryStringKeepingExistingSearchParams({
            location: history.location,
            schema: usersModalParamsSchema,
            searchParams: {
              usersModal: { type: 'profile', id: id },
            },
            options: { shouldJsonStringify: true },
          })}`,
        )
      }
    },
    [canCreateEdit, history],
  )

  useEffect(() => {
    dispatch(fetchUsers())
  }, [dispatch])

  const columnsGetters = useMemo(
    () =>
      ({
        username: (u) => u.username,
        email: (u) => u.email,
        cellPhone: (u) => u.cellPhone,
        status: (u) => u.status,
      }) satisfies Record<string, (o: User) => unknown>,
    [],
  )

  const onFilterModelChange = (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel)
  }

  const columns = useMemo(
    (): Array<GridColDef<User>> => [
      columnHelper.string((_, row) => row.username, {
        headerName: ctIntl.formatMessage({ id: 'Username' }),
        field: 'username',
        renderCell: ({ row, value }) => (
          <Stack
            direction="row"
            gap={1}
            alignItems="center"
          >
            {row.status === 'blocked' && <LockOutlinedIcon color="error" />}
            <Typography>{value}</Typography>
          </Stack>
        ),
        flex: 2,
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Phone Number' }),
        field: 'cellPhone',
        valueGetter: (_, row) => columnsGetters.cellPhone(row),
        flex: 2,
      },
      columnHelper.string((_, row) => row.email, {
        headerName: ctIntl.formatMessage({ id: 'Email Address' }),
        field: 'email',
        flex: 2,
      }),
      columnHelper.singleSelect((_, row): StatusColumnValue => row.status, {
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        field: columnsIds.status,
        filterable: false,
        renderCell: ({ row }) => {
          const { label, color } = chipStatus(row.status)
          return (
            <Chip
              sx={{
                height: '24px',
              }}
              label={ctIntl.formatMessage({ id: label })}
              variant="outlined"
              color={color}
            />
          )
        },
        valueOptions: [
          { value: 'active', label: ctIntl.formatMessage({ id: 'Active' }) },
          { value: 'deactivated', label: ctIntl.formatMessage({ id: 'Inactive' }) },
          { value: 'blocked', label: ctIntl.formatMessage({ id: 'Blocked' }) },
        ],
        flex: 1,
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => [
          <GridActionsCellItem
            key="edit"
            disabled={!canCreateEdit}
            icon={<EditIcon />}
            label={ctIntl.formatMessage({ id: 'Edit' })}
            onClick={(event) => {
              event.stopPropagation()
              navigateToDetailsPage(row.id)
            }}
          />,
        ],
      },
    ],
    [canCreateEdit, columnHelper, columnsGetters, navigateToDetailsPage],
  )

  return (
    <>
      <PageWithMainTableContainer>
        <PageHeader>
          <PageHeader.Title>
            {ctIntl.formatMessage({ id: 'Manage Users' })}
          </PageHeader.Title>
          <PageHeader.ButtonsContainer>
            {canCreateEdit && <AddUser navigateToDetailsPage={navigateToDetailsPage} />}
          </PageHeader.ButtonsContainer>
        </PageHeader>
        <UserDataGridWithSavedSettingsOnIDB
          disableRowSelectionOnClick={!canCreateEdit}
          filterModel={filterModel}
          onFilterModelChange={onFilterModelChange}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25, page: 0 },
            },
          }}
          pageSizeOptions={[25, 50, 100]}
          pagination
          sx={{
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            '& .MuiDataGrid-overlayWrapperInner': {
              // Allows user to click buttons within the table while loading is true
              pointerEvents: 'none',
            },
          }}
          Component={DataGrid}
          dataGridId="ManagerUsers"
          editMode="row"
          rows={users}
          columns={columns}
          loading={loading}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
          }}
          slotProps={{
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
                filterButton: { show: true },
              },
              extraContent: {
                left: (
                  <FormControl>
                    <InputLabel>
                      {ctIntl.formatMessage({ id: 'Status Filter' })}
                    </InputLabel>
                    <Select
                      size="small"
                      value={statusFilterValue satisfies StatusFilterOptionValue}
                      label={ctIntl.formatMessage({
                        id: ctIntl.formatMessage({ id: 'Status Filter' }),
                      })}
                      onChange={(e) => {
                        const newValue = e.target.value as StatusFilterOptionValue
                        setFilterModel((prev): typeof filterModel => ({
                          ...prev,
                          items: updateFirstWhere(
                            prev.items,
                            (item) => item.field === columnsIds.status,
                            (item): (typeof prev.items)[number] => ({
                              ...item,
                              value: (newValue === 'all'
                                ? undefined
                                : newValue) satisfies StatusFilterModelValue,
                            }),
                          ),
                        }))
                      }}
                    >
                      {StatusFilterOptions.map((option) => (
                        <MenuItem
                          key={option.value}
                          value={option.value}
                        >
                          {ctIntl.formatMessage({ id: option.label })}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                ),
              },
            }),
            pagination: { showFirstButton: true, showLastButton: true },
          }}
          onRowClick={(params) => navigateToDetailsPage(params.row.id)}
        />
      </PageWithMainTableContainer>
      <EditUser />
    </>
  )
}

export default ManageUsers
