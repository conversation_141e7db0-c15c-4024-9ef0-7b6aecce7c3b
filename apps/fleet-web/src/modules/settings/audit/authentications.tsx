import { useEffect, useMemo, useState } from 'react'
import {
  DataGrid,
  DateRangePicker,
  GridLogicOperator,
  LinearProgress,
  SingleInputDateRangeField,
  Typography,
  useDataGridColumnHelper,
  type DateRange,
  type GridColDef,
  type GridFilterModel,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'

import type { FetchAuthentications } from 'api/mifleet/audit/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import type { AppState } from 'src/root-reducer'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import type { Filters } from 'src/util-functions/search-utils'

import { authenticationFilters } from '../../admin/shared/audit/authentications/utils'
import useFilteredAuditedData from '../../admin/shared/audit/useFilteredAuditedData'
import { actions, getAuthentications, getIsLoadingAuthentications } from './slice'

const { fetchAuthentications } = actions

type Authentication = FetchAuthentications.Authentication

type Props = ReturnType<typeof mapStateToProps> & DispatchProp

const columnsIds = {
  date: 'eventTimeDate',
  user: 'username',
  sourceIp: 'sourceIpAddress',
  origin: 'origin',
  success: 'success',
} as const

const Authentications = ({ authentications, isLoading, dispatch }: Props) => {
  const columnHelper = useDataGridColumnHelper<Authentication>({ filterMode: 'client' })
  const filters = useMemo(
    (): Filters<Authentication> => ({
      search: [(a) => a.username, ...authenticationFilters.search],
    }),
    [],
  )

  const defaultDateFilter = useMemo(
    () =>
      ({
        field: columnsIds.date,
        operator: 'range',
        value: [DateTime.now(), DateTime.now()],
      }) as const,
    [],
  )

  const [filterModel, setFilterModel] = useState<GridFilterModel>(() => ({
    items: [defaultDateFilter],
    logicOperator: GridLogicOperator.Or,
    quickFilterValues: [],
  }))

  const dateRangeFilterValue = useMemo((): DateRange<DateTime> => {
    for (const item of filterModel.items) {
      if (item.field === columnsIds.date && item.operator === 'range') {
        return item.value ?? [null, null]
      }
    }

    return [null, null]
  }, [filterModel.items])

  const newColumns = useMemo(
    () =>
      [
        columnHelper.string((_, row) => row.username, {
          field: columnsIds.user,
          headerName: ctIntl.formatMessage({ id: 'User' }),
          flex: 1,
        }),
        columnHelper.dateTime({
          field: columnsIds.date,
          headerName: ctIntl.formatMessage({ id: 'Date' }),
          valueGetter: (_, row) =>
            row.eventTimeDate ? row.eventTimeDate.date : row.eventTimeDate,
          valueFormatter: (_, row) =>
            row.eventTimeDate
              ? row.eventTimeDate.formatted
              : ctIntl.formatMessage({ id: 'Invalid date' }),
          flex: 1,
          resizable: true,
        }),
        columnHelper.string((_, row) => row.sourceIpAddress, {
          field: columnsIds.sourceIp,
          headerName: ctIntl.formatMessage({ id: 'Source IP' }),
          flex: 1,
        }),
        columnHelper.string((_, row) => row.origin, {
          field: columnsIds.origin,
          headerName: ctIntl.formatMessage({ id: 'Origin' }),
          flex: 1,
        }),
        columnHelper.singleSelect((_, row) => row.success, {
          field: columnsIds.success,
          headerName: ctIntl.formatMessage({ id: 'Success' }),
          flex: 1,
          valueOptions: [
            {
              label: ctIntl.formatMessage({ id: 'Succeeded' }),
              value: true,
            },
            {
              label: ctIntl.formatMessage({ id: 'Failed' }),
              value: false,
            },
          ],
          renderCell: ({ row }) => (
            <Typography
              sx={(theme) => ({
                fontSize: 'inherit',
                color: row.success
                  ? theme.palette.success.main
                  : theme.palette.error.main,
              })}
            >
              {ctIntl.formatMessage({ id: row.success ? 'Succeeded' : 'Failed' })}
            </Typography>
          ),
        }),
      ] satisfies Array<GridColDef<Authentication>>,
    [columnHelper],
  )

  const { filteredData } = useFilteredAuditedData({
    data: authentications,
    filters,
  })

  const convertedDateRange = useMemo(
    () => ({
      from: dateRangeFilterValue[0] ? dateRangeFilterValue[0].toJSDate() : new Date(),
      to: dateRangeFilterValue[1] ? dateRangeFilterValue[1].toJSDate() : new Date(),
    }),
    [dateRangeFilterValue],
  )

  useEffect(() => {
    dispatch(fetchAuthentications({ dateRange: convertedDateRange }))
  }, [convertedDateRange, dispatch])

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      dataGridId="audit-authentications"
      columns={newColumns}
      rows={filteredData}
      pagination
      pageSizeOptions={[25, 50, 100]}
      initialState={{
        pagination: {
          paginationModel: { pageSize: 25, page: 0 },
        },
      }}
      loading={isLoading}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
      }}
      filterModel={filterModel}
      onFilterModelChange={(newFilterModel) => {
        if (
          newFilterModel.items.some(
            (item) =>
              item.field === columnsIds.date &&
              item.operator === 'range' &&
              item.value[0] !== null &&
              item.value[1] !== null,
          )
        ) {
          setFilterModel(newFilterModel)
        } else {
          // need to prevent user clear the date range filter
          // data grid haven't supported required filters yet https://github.com/mui/mui-x/issues/12790
          const currentDateRangeFilter = filterModel.items.find(
            (item) =>
              item.field === columnsIds.date &&
              item.operator === 'range' &&
              item.value[0] !== null &&
              item.value[1] !== null,
          )
          setFilterModel({
            ...newFilterModel,
            items: [
              ...newFilterModel.items,
              currentDateRangeFilter ?? defaultDateFilter,
            ],
          })
        }
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            left: (
              <DateRangePicker
                value={dateRangeFilterValue}
                label={ctIntl.formatMessage({
                  id: 'Date Range',
                })}
                onAccept={(value) => {
                  const hasDateFilterAlready = filterModel.items.some(
                    (item) => item.field === columnsIds.date,
                  )

                  setFilterModel({
                    ...filterModel,
                    items: hasDateFilterAlready
                      ? filterModel.items.map((item) =>
                          item.field === columnsIds.date
                            ? ({
                                ...item,
                                value,
                              } satisfies typeof item)
                            : item,
                        )
                      : [
                          {
                            field: columnsIds.date,
                            operator: 'range',
                            value,
                          },
                          ...filterModel.items,
                        ],
                  })
                }}
                disableFuture
                slots={{ field: SingleInputDateRangeField }}
                slotProps={{
                  textField: {
                    sx: { minWidth: '224px' },
                  },
                }}
              />
            ),
          },
        }),
        pagination: { showFirstButton: true, showLastButton: true },
      }}
    />
  )
}

const mapStateToProps = (state: AppState) => ({
  authentications: getAuthentications(state),
  isLoading: getIsLoadingAuthentications(state),
})

export default connect(mapStateToProps)(Authentications)
