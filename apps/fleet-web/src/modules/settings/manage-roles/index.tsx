import { useC<PERSON>back, useMemo, useState } from 'react'
import {
  Chip,
  DataGrid,
  FormControl,
  getGridSingleSelectOperators,
  GridActionsCellItem,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  type GridColDef,
  type GridSingleSelectColDef,
} from '@karoo-ui/core'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { useHistory } from 'react-router-dom'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { getSettings } from 'duxs/user'
import type { FetchRoles } from 'src/api/role/types'
import PageHeader from 'src/components/_containers/PageHeader'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { createDataGridTextColumn } from 'src/shared/data-grid/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import EditRole from '../../admin/edit-role'
import AddRole from './AddRole'
import { useRolesQuery } from './api/queries'
import { rolesModalParamsSchema } from './shared/utils'

type Role = FetchRoles.Return[0]

const columnsIds = {
  status: 'status',
} as const

const StatusFilterOptions = [
  { label: 'Active Roles', value: 'active' },
  { label: 'Inactive Roles', value: 'inactive' },
] as const

type StatusFilterValue = (typeof StatusFilterOptions)[number]['value']

function ManageRoles() {
  const history = useHistory()
  const rolesQuery = useRolesQuery()
  const canCreateEdit = useTypedSelector(getSettings).adminUserSettings as boolean
  const [status, setStatus] = useState<StatusFilterValue>(StatusFilterOptions[0].value)

  const navigateToDetailsPage = useCallback(
    (id: string) => {
      if (canCreateEdit) {
        history.push(
          `${
            SETTINGS.subMenusRoutes.MANAGE_ROLES.path
          }?${buildRouteQueryStringKeepingExistingSearchParams({
            location: history.location,
            schema: rolesModalParamsSchema,
            searchParams: {
              rolesModal: { type: 'profile', id: id },
            },
            options: { shouldJsonStringify: true },
          })}`,
        )
      }
    },
    [canCreateEdit, history],
  )

  const columnsGetters = useMemo(
    () =>
      ({
        username: (u) => u.username,
        status: (u) => u.status,
      }) satisfies Record<string, (o: Role) => unknown>,
    [],
  )
  type StatusColumnValue = ReturnType<(typeof columnsGetters)['status']>

  const filteredProperRoles = useMemo(() => {
    if (rolesQuery.data) {
      const filters: Filters<Role> = {
        search: [columnsGetters.status],
      }

      const { itemMatchesWithTextAndFilters } =
        generateItemMatchesWithTextAndFilters(status)

      return rolesQuery.data.filter((u) => itemMatchesWithTextAndFilters(u, filters))
    }
    return []
  }, [rolesQuery.data, columnsGetters, status])

  const columns = useMemo(
    (): Array<GridColDef<Role>> => [
      createDataGridTextColumn({
        headerNameMsg: { id: 'Role' },
        field: 'username',
        valueGetter: (_, row) => columnsGetters.username(row),
        flex: 2,
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        field: columnsIds.status,
        type: 'singleSelect',
        valueGetter: (_, row) => columnsGetters.status(row),
        valueOptions: [
          { value: 'active', label: ctIntl.formatMessage({ id: 'Active' }) },
          { value: 'deactivated', label: ctIntl.formatMessage({ id: 'Inactive' }) },
        ],
        renderCell: ({ formattedValue, value }) => (
          <Chip
            sx={{
              height: '24px',
            }}
            label={ctIntl.formatMessage({ id: formattedValue ?? 'Inactive' })}
            variant="outlined"
            color={value === 'active' ? 'success' : 'error'}
          />
        ),
        filterOperators: getGridSingleSelectOperators<Role, StatusColumnValue>().map(
          (operator) => ({
            ...operator,
            getValueAsString: (value: Array<StatusColumnValue> | StatusColumnValue) => {
              // Handle anyOf operator
              if (R.isArray(value)) {
                return value.map((v) => getStatusLabel(v)).join(', ')
              }

              return getStatusLabel(value)
            },
          }),
        ),
        flex: 1,
      } satisfies GridSingleSelectColDef<
        {
          label: string
          value: StatusColumnValue
        },
        Role,
        StatusColumnValue
      >,
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        getActions: ({ row }) => [
          <GridActionsCellItem
            key="edit"
            disabled={!canCreateEdit}
            icon={<EditOutlinedIcon />}
            label={ctIntl.formatMessage({ id: 'Edit' })}
            onClick={(event) => {
              event.stopPropagation()
              navigateToDetailsPage(row.id)
            }}
          />,
        ],
      },
    ],
    [canCreateEdit, columnsGetters, navigateToDetailsPage],
  )

  return (
    <>
      <PageWithMainTableContainer>
        <PageHeader>
          <PageHeader.Title>
            {ctIntl.formatMessage({ id: 'Manage Roles' })}
          </PageHeader.Title>
          <PageHeader.ButtonsContainer>
            {canCreateEdit && <AddRole />}
          </PageHeader.ButtonsContainer>
        </PageHeader>
        <UserDataGridWithSavedSettingsOnIDB
          disableRowSelectionOnClick={!canCreateEdit}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25, page: 0 },
            },
          }}
          pagination
          pageSizeOptions={[25, 50, 100]}
          sx={{
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
          }}
          Component={DataGrid}
          dataGridId="ManagerRoles"
          editMode="row"
          rows={filteredProperRoles}
          columns={columns}
          loading={rolesQuery.isFetching}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => <DataStatePlaceholder label={'No data available'} />,
          }}
          slotProps={{
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
                filterButton: { show: true },
              },
              extraContent: {
                left: (
                  <FormControl>
                    <InputLabel>
                      {ctIntl.formatMessage({ id: 'Status Filter' })}
                    </InputLabel>
                    <Select
                      size="small"
                      value={status}
                      label={ctIntl.formatMessage({
                        id: ctIntl.formatMessage({ id: 'Status Filter' }),
                      })}
                      onChange={(e) => setStatus(e.target.value as StatusFilterValue)}
                    >
                      {StatusFilterOptions.map((option) => (
                        <MenuItem
                          key={option.value}
                          value={option.value}
                        >
                          {ctIntl.formatMessage({ id: option.label })}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                ),
              },
            }),
            pagination: { showFirstButton: true, showLastButton: true },
          }}
          onRowClick={(params) => navigateToDetailsPage(params.row.id)}
        />
      </PageWithMainTableContainer>
      <EditRole />
    </>
  )
}

export default ManageRoles

const getStatusLabel = (status: FetchRoles.Status) =>
  match(status)
    .with('active', () => ctIntl.formatMessage({ id: 'Active' }))
    .with('deactivated', () => ctIntl.formatMessage({ id: 'Inactive' }))
    .exhaustive()
