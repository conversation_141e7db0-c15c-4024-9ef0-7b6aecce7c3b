import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import { parsePermissions, type PermissionLookupValueOfType } from 'api/admin'
import { apiCallerNoX } from 'api/api-caller'
import type { DriverId } from 'api/types'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import {
  getSubuserError,
  SUBUSER_ASSIGNMENT_ERROR,
} from 'src/modules/delivery/utils/error-message-translation-key'
import { ctIntl } from 'src/util-components/ctIntl'
import { toImmutable, toMutable } from 'src/util-functions/functional-utils'
import { createQuery } from 'src/util-functions/react-query-utils'

export declare namespace FetchUserDriverPermissions {
  type ApiInput = {
    client_user_id: string
  }

  type ApiOutput = Array<{
    client_driver_id: DriverId
    driver_name: string | null
    driver_surname: string | null
    permission_id: string
    permissions: {
      view: boolean
      edit: boolean
      remove: boolean
    }
    groupNames: Array<string>
  }>

  type Return = Array<{
    id: string
    name: string
    driverGroups: Array<string>
    permissions: {
      view: boolean
      edit: boolean
      remove: boolean
    }
  }>
}

export declare namespace UpdateUserDriverPermissions {
  type ApiInput = {
    client_user_id: string
    permissions: Array<{
      driver_id: string
      permission_id: PermissionLookupValueOfType
    }>
  }
}

const parseUserDriverPermissions = (data: FetchUserDriverPermissions.ApiOutput) =>
  data.map((driver) => ({
    id: driver.client_driver_id,
    name:
      driver.driver_surname && driver.driver_name
        ? `${driver.driver_name} ${driver.driver_surname}`
        : driver.driver_name ?? '',
    permissions: driver.permissions,
    driverGroups: driver.groupNames,
  }))

const fetchUserDriverPermissions = async (
  params: FetchUserDriverPermissions.ApiInput,
): Promise<FetchUserDriverPermissions.Return> =>
  apiCallerNoX<FetchUserDriverPermissions.ApiOutput>(
    'ct_fleet_get_client_user_driver_permission',
    params,
  ).then((res) => parseUserDriverPermissions(res))

const userDriverPermissionsKey = (params: FetchUserDriverPermissions.ApiInput) =>
  ['role/userDriverPermissions', params] as const

const userDriverPermissionsQuery = (params: FetchUserDriverPermissions.ApiInput) =>
  createQuery({
    queryKey: userDriverPermissionsKey(params),
    queryFn: () => fetchUserDriverPermissions(params),
  })

export function useUserDriverPermissionsQuery(
  params: FetchUserDriverPermissions.ApiInput,
) {
  return useQuery(userDriverPermissionsQuery(params))
}

function updateUserDriverPermissionsMutation(
  params: UpdateUserDriverPermissions.ApiInput,
) {
  return apiCallerNoX('ct_fleet_update_client_user_driver_permission', params)
}

export function useUpdateUserDriverPermissionsMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    onMutate: async ({ client_user_id, permissions }) => {
      const query = userDriverPermissionsQuery({
        client_user_id,
      })
      await queryClient.cancelQueries(query)

      const previousQueryData = query.getData(queryClient)
      const permissionsToUpdateMap = new Map(
        permissions.map((permission) => [permission.driver_id, permission] as const),
      )
      query.setData(queryClient, {
        updater: (oldData) => {
          if (oldData === undefined) {
            return oldData
          }

          const newData = toMutable(R.clone(oldData))
          for (const vehicleGroup of newData) {
            const vehicleGroupToUpdate = permissionsToUpdateMap.get(vehicleGroup.id)
            if (vehicleGroupToUpdate) {
              vehicleGroup.permissions = parsePermissions(
                vehicleGroupToUpdate.permission_id,
              )
            }
          }
          return toImmutable(newData)
        },
      })

      // Return a context with the previous data
      return {
        previousUserDriverPermissions: previousQueryData,
        userDriverPermissionKey: query.queryKey,
      }
    },
    mutationFn: (params: UpdateUserDriverPermissions.ApiInput) =>
      updateUserDriverPermissionsMutation(params),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: userDriverPermissionsKey(variables) })
    },
    onError: (error, _vars, context) => {
      if (context !== undefined) {
        queryClient.setQueryData(
          context?.userDriverPermissionKey,
          context?.previousUserDriverPermissions,
        )
      }

      const errorMessage = match(error)
        .with({ message: SUBUSER_ASSIGNMENT_ERROR }, () =>
          getSubuserError(SUBUSER_ASSIGNMENT_ERROR),
        )
        .otherwise(() => 'There was an error updating the driver permissions.')

      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: errorMessage,
        }),
        { variant: 'error' },
      )
    },
  })
}
