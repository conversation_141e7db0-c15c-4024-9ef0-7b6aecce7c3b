/* eslint-disable no-param-reassign */
import { createMemoryHistory } from 'history'
import { DateTime } from 'luxon'
import { Route } from 'react-router'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { endpointsMocks } from 'src/cypress-ct/mocks/endpoints'
import { remindersEndpointMocks } from 'src/cypress-ct/mocks/endpoints/reminders'
import { mountWithProviders } from 'src/cypress-ct/utils'

import AdminRouter from '.'

const globalHistory = createMemoryHistory({
  initialEntries: ['/admin'],
})

const mountComponent = () =>
  mountWithProviders(
    <Route path={'/admin'}>
      <AdminRouter />
    </Route>,
    {
      history: globalHistory,
      reduxOptions: {
        preloadedState: {
          user: duxsMocks.user().mockState,
          admin: duxsMocks.admin,
        },
      },
    },
  )

beforeEach(() => {
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    match(req.body)
      .with({ method: 'ct_fleet_get_prelogin_data' }, () => {
        req.reply({ delay: 50, body: { id: 10, result: {} } })
      })
      .with({ method: 'ct_fleet_get_reminder_prerequisites' }, () => {
        req.alias = 'getReminders'
        req.reply(remindersEndpointMocks.ct_fleet_get_reminder_prerequisites())
      })
      .with({ method: 'ct_fleet_get_vehicles_reminders' }, () => {
        req.reply(remindersEndpointMocks.ct_fleet_get_vehicles_reminders())
      })
      .with({ method: 'ct_fleet_add_reminder' }, () => {
        req.reply(remindersEndpointMocks.ct_fleet_add_reminder())
      })
      .with({ method: 'ct_fleet_get_fleet_reminders_events' }, () => {
        req.reply(remindersEndpointMocks.ct_fleet_get_fleet_reminders_events())
      })
      .with({ method: 'ct_fleet_get_user' }, () => {
        req.reply(endpointsMocks.ct_fleet_get_user())
      })
      .with({ method: 'ct_fleet_get_drivers_v2' }, () => {
        req.reply(endpointsMocks.ct_fleet_get_drivers_v2())
      })
      .with({ method: 'ct_fleet_get_vehiclelist_v3' }, () => {
        req.reply(endpointsMocks.ct_fleet_get_vehiclelist_v3())
      })
      .run()
  })
  mountComponent()
})

const OverviewTable = () => cy.findByTestId('RemindersOverviewTable')
const ToolbarDeleteButton = () => cy.findByTestId('datagrid-delete-button-with-counter')
const AddReminderButton = () =>
  cy.findByTestId('RemindersOverviewHeaderButton-AddReminder')
const DownloadButton = () => cy.findByTestId('RemindersOverviewHeaderButton-Download')
const SingleRemindersDrawerContentForm = () =>
  cy.findByTestId('RemindersDrawer-SingleContent').get('form>.MuiStack-root')

describe('Reminders', () => {
  it('Show fleet overview table', () => {
    cy.wait('@getReminders')

    //Fleet tab is selected
    cy.findAllByTestId('RemindersOverviewTable-GridContainerTab-fleet').should(
      'have.class',
      'Mui-selected',
    )

    OverviewTable().should('exist')

    // Toolbar right buttons
    DownloadButton().should('not.be.disabled')
    ToolbarDeleteButton().should('be.disabled')
    AddReminderButton().should('not.be.disabled')

    // Rows count: 2
    OverviewTable()
      .get('.MuiDataGrid-virtualScrollerRenderZone>.MuiDataGrid-row')
      .should('have.length', 2)
  })

  it('Create valid single fleet reminder', () => {
    cy.wait('@getReminders')

    AddReminderButton().click()
    SingleRemindersDrawerContentForm().should('exist')

    // CATEGORY SECTION
    // Open dropdown
    cy.findByTestId('RemindersDrawer-CategoryInput').should('exist').click()
    // Should have 3 options, 2 categories and 'Add New'. Select first option
    cy.get('#RemindersDrawer-CategoryList-Options')
      .children()
      .should('have.length', 3)
      .first()
      .click()

    // APPLY TO SECTION
    const ApplyToSection = () => cy.findByTestId('RemindersDrawer-ApplyToSection')
    // Click on the vehicle option
    ApplyToSection().find('input[type="radio"]').should('have.length', 2).eq(1).click()
    //Open vehicle auto complete
    ApplyToSection().find('.MuiAutocomplete-root').click()
    // Select first vehicle option
    cy.get('#RemindersDrawer-VehicleAutocomplete-Options').find('li').eq(1).click()
    //Open close auto complete
    ApplyToSection().find('.MuiAutocomplete-root').click()

    // CRITERIA SECTION
    const CriteriaSection = () => cy.findByTestId('RemindersDrawer-CriteriaSection')
    const CriteriaSectionAddButton = () =>
      CriteriaSection().findByTestId('RemindersDrawer-CriteriaSection-AddCriteria')

    const CriteriaSectionCriteria = (index: number) =>
      CriteriaSection().findByTestId(
        `RemindersDrawer-CriteriaSection-Criteria_${index}`,
      )
    const CriteriaSectionCriteriaInput = (index: number) =>
      CriteriaSectionCriteria(index).findByTestId(
        `RemindersDrawer-CriteriaSection-Criteria_${index}-CriteriaInput`,
      )
    const CriteriaSectionCriteriaOptions = (index: number) =>
      cy.get(`#RemindersDrawer-CriteriaSection-CriteriaInput-Options_${index}`)

    // Select criteria Distance
    CriteriaSectionCriteriaInput(0).click()
    CriteriaSectionCriteriaOptions(0)
      .children(':visible')
      .should('have.length', 4)
      .eq(0)
      .click()
    // Type value for first input
    CriteriaSectionCriteria(0).find('input').eq(1).type('1000')

    // Check if add criteria button is enabled and add new criteria
    CriteriaSectionAddButton().should('not.be.disabled').click()
    // Select criteria Hours of Operation
    CriteriaSectionCriteriaInput(1).click()
    CriteriaSectionCriteriaOptions(1)
      .children(':visible')
      .should('have.length', 3)
      .eq(0)
      .click()
    // Type value for first input
    CriteriaSectionCriteria(1).find('input').eq(1).type('1000')

    // Check if add criteria button is enabled and add new criteria
    CriteriaSectionAddButton().should('not.be.disabled').click()

    // Select criteria Hours of Operation
    CriteriaSectionCriteriaInput(2).click()
    CriteriaSectionCriteriaOptions(2)
      .children(':visible')
      .should('have.length', 2)
      .eq(0)
      .click()
    // Type value for first input
    CriteriaSectionCriteria(2)
      .find('input')
      .eq(1)
      .type(DateTime.local().plus({ days: 1 }).toString())
      .click()

    // Check if add criteria button is enabled and add new criteria
    CriteriaSectionAddButton().should('not.be.disabled').click()

    // Select criteria PTO
    CriteriaSectionCriteriaInput(3).click()
    CriteriaSectionCriteriaOptions(3)
      .children(':visible')
      .should('have.length', 1)
      .eq(0)
      .click()
    // Type value for first input
    CriteriaSectionCriteria(3).find('input').eq(1).type('1000')

    // Check if add criteria button is disabled when maximum number of criteria are displayed
    CriteriaSectionAddButton().should('be.disabled')

    // Notification SECTION
    const NotificationSection = () =>
      cy.findByTestId('RemindersDrawer-NotificationSection')
    // Open Email Autocomplete
    NotificationSection().findByTestId('RemindersDrawer-EmailAutocomplete').click()
    // Select first email option
    cy.get('#RemindersDrawer-EmailAutocomplete-Options').find('li').first().click()

    // Submit form and close
    SingleRemindersDrawerContentForm().find('button[type="submit"]').click()

    SingleRemindersDrawerContentForm().should('not.exist')
  })

  it('Check tab change', () => {
    const driverTab = () =>
      cy.findAllByTestId('RemindersOverviewTable-GridContainerTab-driver')
    const listTab = () =>
      cy.findAllByTestId('RemindersOverviewTable-GridContainerTab-list')

    cy.wait('@getReminders')

    driverTab().click()
    driverTab().should('have.class', 'Mui-selected')

    listTab().click()
    listTab().should('have.class', 'Mui-selected')
  })
})
