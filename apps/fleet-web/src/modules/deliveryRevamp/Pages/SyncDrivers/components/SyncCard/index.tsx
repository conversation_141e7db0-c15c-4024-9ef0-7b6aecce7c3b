import { useState } from 'react'
import {
  But<PERSON>,
  Chip,
  Divider,
  Menu,
  MenuItem,
  OverflowTypography,
  Stack,
  Typography,
  type ChipProps,
} from '@karoo-ui/core'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import MailIcon from '@mui/icons-material/MailOutline'
import PhoneIcon from '@mui/icons-material/Phone'
import { FormattedMessage } from 'react-intl'
import { useHistory, useLocation } from 'react-router'
import { match } from 'ts-pattern'

import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import type { FetchDeliveryDriversToSync } from 'src/modules/deliveryRevamp/api/driversMigration/useDeliveryDriversToSyncQuery'
import { useDriverMigrationDialogsContext } from 'src/modules/deliveryRevamp/contexts/DeliveryDriverMigrationDialogsContext'
import { useDeliverySettingsContext } from 'src/modules/deliveryRevamp/contexts/DeliverySettingsContext'
import { ctIntl } from 'src/util-components/ctIntl'

type SyncCardProps = {
  deliveryDriver: FetchDeliveryDriversToSync.Return[number]['deliveryDriver']
  matches: FetchDeliveryDriversToSync.Return[number]['matches']
}

export const SyncCard = ({ deliveryDriver, matches }: SyncCardProps) => {
  const { setOpenedDialog } = useDriverMigrationDialogsContext()

  const handleActionClick: MoreActionsButtonProps['onActionClick'] = (action) => {
    setOpenedDialog(
      match(action)
        .with('SYNC_WITH_ANOTHER_FLEET_DRIVER', (action) => {
          const fleetDriverBestMatch = (() => {
            // First try to find an EXACT match
            const exactMatch = matches.find((match) => match.type === 'EXACT')
            if (exactMatch) {
              return exactMatch.fleetDriver
            }

            // If no EXACT match, keep track of best match for each level
            const bestMatches = {
              GOOD: null as (typeof matches)[number]['fleetDriver'] | null,
              PARTIAL: null as (typeof matches)[number]['fleetDriver'] | null,
            }

            // Find best match for each level
            for (const match of matches) {
              if (match.type === 'GOOD' && !bestMatches.GOOD) {
                bestMatches.GOOD = match.fleetDriver
              } else if (match.type === 'PARTIAL' && !bestMatches.PARTIAL) {
                bestMatches.PARTIAL = match.fleetDriver
              }
            }

            // Return the highest priority match we found
            return bestMatches.GOOD || bestMatches.PARTIAL || null
          })()

          return {
            type: action,
            data: { deliveryDriver, fleetDriverBestMatch },
          }
        })
        .with('ADD_AS_NEW_FLEET_DRIVER', (action) => ({
          type: action,
          data: { deliveryDriver },
        }))
        .with('DELETE_DELIVERY_DRIVER', (action) => ({
          type: action,
          data: { deliveryDriver },
        }))
        .exhaustive(),
    )
  }

  return (
    <Stack
      gap={1.5}
      sx={({ palette }) => ({
        border: 1,
        borderColor: 'divider',
        borderRadius: 2,
        bgcolor: 'background.paper',
        '&:hover': {
          bgcolor: palette.states.primary.hover,
          border: `1px solid ${palette.states.secondary.focus}`,
        },
      })}
    >
      <Stack
        direction="row"
        alignItems="center"
        gap={3}
        p={2}
        pb={1}
      >
        <Stack
          flex={1}
          divider={<Divider />}
          gap={2}
        >
          {matches.length === 0 ? (
            <Stack
              flex={1}
              gap={1}
            >
              <Stack
                direction="row"
                gap={2}
                flex={1}
                alignItems="center"
              >
                <Stack
                  flex={1}
                  alignSelf="start"
                >
                  <DriverInfo
                    type="delivery"
                    driver={deliveryDriver}
                  />
                </Stack>

                <MatchLevel matchType="NONE" />

                <Stack flex={1}>
                  <DriverInfo
                    type="fleet"
                    driver={null}
                  />
                </Stack>
              </Stack>

              <Stack
                direction="row"
                justifyContent="flex-end"
                gap={2}
                px={1.5}
              >
                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={() => {
                    handleActionClick('ADD_AS_NEW_FLEET_DRIVER')
                  }}
                >
                  {ctIntl.formatMessage({
                    id: 'delivery.syncDrivers.button.addAsFleetDriver',
                  })}
                </Button>

                <MoreActionsButton onActionClick={handleActionClick} />
              </Stack>
            </Stack>
          ) : (
            matches.map((match, index) => (
              <Stack
                key={match.fleetDriver.id}
                flex={1}
                gap={1}
              >
                <Stack
                  direction="row"
                  gap={2}
                  flex={1}
                  alignItems="center"
                >
                  <Stack
                    flex={1}
                    alignSelf="start"
                  >
                    {/* Only show the delivery driver info once */}
                    {index === 0 && (
                      <DriverInfo
                        type="delivery"
                        driver={deliveryDriver}
                      />
                    )}
                  </Stack>
                  <MatchLevel matchType={match.type} />

                  <Stack flex={1}>
                    <DriverInfo
                      type="fleet"
                      driver={match.fleetDriver}
                    />
                  </Stack>
                </Stack>

                <Stack
                  direction="row"
                  justifyContent="flex-end"
                  gap={2}
                  px={1.5}
                >
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => {
                      setOpenedDialog({
                        type: 'SYNC_DRIVER_DATA',
                        data: {
                          deliveryDriver,
                          fleetDriver: match.fleetDriver,
                        },
                      })
                    }}
                  >
                    {ctIntl.formatMessage({ id: 'delivery.button.syncDriverData' })}
                  </Button>

                  <MoreActionsButton onActionClick={handleActionClick} />
                </Stack>
              </Stack>
            ))
          )}
        </Stack>
      </Stack>
    </Stack>
  )
}

type DriverInfoProps = {
  driver: FetchDeliveryDriversToSync.Return[number]['deliveryDriver'] | null
  type: 'delivery' | 'fleet'
}

const DriverInfo = ({ driver, type }: DriverInfoProps) => {
  const {
    deliverySettings: { driverLabel },
  } = useDeliverySettingsContext()

  const history = useHistory()
  const location = useLocation()

  const driverInfoTitle =
    type === 'delivery'
      ? ctIntl.formatMessage(
          { id: 'delivery.syncDrivers.driverCard.label.delivery' },
          { values: { driverLabel } },
        )
      : ctIntl.formatMessage({ id: 'delivery.syncDrivers.driverCard.label.fleet' })

  return (
    <Stack position="relative">
      {/* Absolute border to hide the border of the parent */}
      <Stack
        sx={{
          position: 'absolute',
          top: 0,
          left: 9,
          px: 1,
          borderTop: 1,
          borderColor: 'background.paper',
          zIndex: 1,
        }}
      >
        <Typography
          variant="caption"
          sx={{ visibility: 'hidden', opacity: 0 }}
        >
          {driverInfoTitle}
        </Typography>
      </Stack>

      <Typography
        variant="caption"
        color="text.secondary"
        mb={1}
        sx={{
          position: 'absolute',
          top: -9,
          left: 9,
          px: 1,
          zIndex: 2,
        }}
      >
        {driverInfoTitle}
      </Typography>

      <Stack
        gap={1}
        sx={{
          p: 1.5,
          bgcolor: driver ? 'background.paper' : 'grey.50',
          color: 'text.primary',
          border: 1,
          borderColor: 'divider',
          borderRadius: 2,
          position: 'relative',
          opacity: driver ? 1 : 0.5,
        }}
      >
        <Stack
          direction="row"
          gap={1}
          justifyContent="space-between"
        >
          <OverflowTypography typographyProps={{ variant: 'h6' }}>
            {driver?.name ?? 'N/A'}
          </OverflowTypography>

          {driver && type === 'fleet' && (
            <Button
              variant="outlined"
              color="secondary"
              size="small"
              onClick={() => {
                history.push(getDriverDetailsModalMainPath(location, driver.id))
              }}
            >
              {ctIntl.formatMessage({
                id: 'delivery.syncDrivers.driverCard.button.seeProfile',
              })}
            </Button>
          )}
        </Stack>

        <Stack
          gap={1}
          direction="row"
        >
          <Chip
            size="small"
            label={driver?.formattedPhoneNumber ?? 'N/A'}
            icon={<PhoneIcon />}
            sx={{
              '& .MuiChip-icon': {
                color: 'text.primary',
                ml: 1,
              },
            }}
          />

          <Chip
            size="small"
            label={driver?.email ?? 'N/A'}
            icon={<MailIcon />}
            sx={{
              '& .MuiChip-icon': {
                color: 'text.primary',
                ml: 1,
              },
            }}
          />
        </Stack>
      </Stack>
    </Stack>
  )
}

const MatchLevel = ({
  matchType,
}: {
  matchType: FetchDeliveryDriversToSync.MatchType | 'NONE'
}) => {
  const { color, label } = match(matchType)
    .with('EXACT', () => ({
      color: 'success' as const,
      label: 'delivery.driverMigration.match.exact',
    }))
    .with('GOOD', () => ({
      color: 'warning' as const,
      label: 'delivery.driverMigration.match.good',
    }))
    .with('PARTIAL', () => ({
      color: 'primary' as const,
      label: 'delivery.driverMigration.match.partial',
    }))
    .otherwise(() => ({
      color: 'default' as const,
      label: 'delivery.driverMigration.match.none',
    })) satisfies { color: ChipProps['color']; label: string }

  return (
    <Typography
      variant="caption"
      align="center"
    >
      {matchType === 'NONE' ? (
        ctIntl.formatMessage({ id: label })
      ) : (
        <FormattedMessage
          id={label}
          values={{
            chip: (chunks) => (
              <Chip
                size="small"
                color={color}
                label={chunks}
                sx={{ mx: 0.5 }}
              />
            ),
          }}
        />
      )}
    </Typography>
  )
}

type MoreActionsButtonProps = {
  onActionClick: (
    action:
      | 'SYNC_WITH_ANOTHER_FLEET_DRIVER'
      | 'ADD_AS_NEW_FLEET_DRIVER'
      | 'DELETE_DELIVERY_DRIVER',
  ) => void
}

const MoreActionsButton = ({ onActionClick }: MoreActionsButtonProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleActionClick: MoreActionsButtonProps['onActionClick'] = (action) => {
    onActionClick(action)
    handleClose()
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <>
      <Button
        variant="outlined"
        color="secondary"
        size="small"
        endIcon={<ArrowDropDownIcon />}
        onClick={handleClick}
      >
        {ctIntl.formatMessage({ id: 'delivery.moreActions' })}
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem
          onClick={() => {
            handleActionClick('SYNC_WITH_ANOTHER_FLEET_DRIVER')
          }}
        >
          {ctIntl.formatMessage({
            id: 'delivery.syncDrivers.driverCard.button.moreOptions.syncWithAnotherFleetDriver',
          })}
        </MenuItem>
        <MenuItem onClick={() => handleActionClick('ADD_AS_NEW_FLEET_DRIVER')}>
          {ctIntl.formatMessage({
            id: 'delivery.syncDrivers.driverCard.button.moreOptions.addAsNewFleetDriver',
          })}
        </MenuItem>
        <MenuItem onClick={() => handleActionClick('DELETE_DELIVERY_DRIVER')}>
          {ctIntl.formatMessage({
            id: 'delivery.syncDrivers.driverCard.button.moreOptions.deleteDeliveryDriver',
          })}
        </MenuItem>
      </Menu>
    </>
  )
}
