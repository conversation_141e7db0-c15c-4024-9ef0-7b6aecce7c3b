import { useState } from 'react'
import {
  Box,
  DateTimePicker,
  IconButton,
  Popover,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import SaveOutlinedIcon from '@mui/icons-material/SaveOutlined'
import type { DateTime } from 'luxon'

import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

export type TimeRangeEditorPopoverProps = {
  anchorEl: HTMLElement
  initialStart: DateTime | null
  initialEnd: DateTime | null
  initialRemark?: string
  onClose: () => void
  onChangeTimeRange: (newStart: DateTime, newEnd: DateTime, remark?: string) => void
  onDeleteButtonClick: () => void
  onCloseButtonClick: () => void
  onSaveButtonClick: () => void
  minPickupDateTime?: DateTime
  maxPickupDateTime?: DateTime
  maxDuration: {
    value: number
    unit: string
  }
  isExistingSchedule?: boolean
  isJobSchedule?: boolean
  isSaving?: boolean
}

const TimeRangeEditorPopover = ({
  anchorEl,
  initialStart,
  initialEnd,
  initialRemark = '',
  onClose,
  onChangeTimeRange,
  onDeleteButtonClick,
  onCloseButtonClick,
  onSaveButtonClick,
  minPickupDateTime,
  maxPickupDateTime,
  maxDuration,
  isExistingSchedule = false,
  isJobSchedule = false,
  isSaving = false,
}: TimeRangeEditorPopoverProps) => {
  const [startDateTime, setStartDateTime] = useState<DateTime | null>(initialStart)
  const [endDateTime, setEndDateTime] = useState<DateTime | null>(initialEnd)
  const [remark, setRemark] = useState<string>(initialRemark)

  const today = DeliveryDateTime.now().startOf('day')

  const effectiveMinDateTime =
    minPickupDateTime && minPickupDateTime > today ? minPickupDateTime : today

  const maxEndDateTime =
    startDateTime && maxDuration.value > 0
      ? startDateTime.plus({
          [maxDuration.unit]: maxDuration.value,
        })
      : undefined

  const handleStartDateTimeChange = (newStartDateTime: DateTime | null) => {
    if (!newStartDateTime || !newStartDateTime.isValid) return

    setStartDateTime(newStartDateTime)

    if (endDateTime && endDateTime < newStartDateTime) {
      const newEndDateTime = newStartDateTime.plus({ hours: 1 })
      setEndDateTime(newEndDateTime)
      onChangeTimeRange(newStartDateTime, newEndDateTime, remark)
    } else if (endDateTime) {
      onChangeTimeRange(newStartDateTime, endDateTime, remark)
    }
  }

  const handleEndDateTimeChange = (newEndDateTime: DateTime | null) => {
    if (!newEndDateTime || !newEndDateTime.isValid || !startDateTime) return

    setEndDateTime(newEndDateTime)
    onChangeTimeRange(startDateTime, newEndDateTime, remark)
  }

  const handleRemarkChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newRemark = event.target.value
    setRemark(newRemark)
    if (startDateTime && endDateTime) {
      onChangeTimeRange(startDateTime, endDateTime, newRemark)
    }
  }

  return (
    <Popover
      open
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center',
      }}
      transformOrigin={{
        vertical: 'bottom',
        horizontal: 'center',
      }}
      slotProps={{
        paper: {
          sx: {
            mt: -2,
            width: 'auto',
          },
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box
          sx={{
            mb: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="subtitle1">
            {isExistingSchedule
              ? ctIntl.formatMessage({ id: 'delivery.driverAvailability.edit' })
              : ctIntl.formatMessage({ id: 'delivery.driverAvailability.add' })}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            <DateTimePicker
              label={ctIntl.formatMessage({
                id: 'delivery.driverAvailability.field.busyFrom',
              })}
              value={startDateTime}
              onChange={handleStartDateTimeChange}
              minDateTime={effectiveMinDateTime}
              maxDateTime={maxPickupDateTime}
              sx={{ width: '220px' }}
              disabled={isSaving}
            />
            <Typography sx={{ mx: 0.5, color: 'text.secondary' }}>—</Typography>
            <DateTimePicker
              label={ctIntl.formatMessage({ id: 'Busy To' })}
              value={endDateTime}
              onChange={handleEndDateTimeChange}
              minDateTime={startDateTime ?? effectiveMinDateTime}
              maxDateTime={maxEndDateTime}
              sx={{ width: '220px' }}
              disabled={isSaving}
            />
          </Stack>
          <TextField
            label={ctIntl.formatMessage({ id: 'Remarks' })}
            value={remark}
            onChange={handleRemarkChange}
            fullWidth
            multiline
            rows={2}
            disabled={isSaving}
            placeholder={ctIntl.formatMessage({
              id: 'delivery.driverAvailability.notesOrComments',
            })}
          />
          <Stack
            direction="row"
            justifyContent="flex-end"
            spacing={0.5}
          >
            {!isJobSchedule && (
              <>
                <IconButton
                  onClick={onSaveButtonClick}
                  size="small"
                  color="primary"
                  disabled={isSaving}
                >
                  <SaveOutlinedIcon />
                </IconButton>
                <IconButton
                  onClick={onDeleteButtonClick}
                  size="small"
                  disabled={isSaving}
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              </>
            )}
            <IconButton
              onClick={onCloseButtonClick}
              size="small"
              disabled={isSaving}
            >
              <CloseOutlinedIcon />
            </IconButton>
          </Stack>
        </Box>
      </Box>
    </Popover>
  )
}

export default TimeRangeEditorPopover
