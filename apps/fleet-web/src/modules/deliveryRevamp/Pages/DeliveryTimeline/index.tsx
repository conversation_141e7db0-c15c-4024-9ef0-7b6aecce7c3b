import { useEffect, useMemo, useRef, useState, type MouseEvent } from 'react'
import {
  Box,
  InputAdornment,
  LinearProgress,
  TextField,
  Typography,
} from '@karoo-ui/core'
import SearchIcon from '@mui/icons-material/Search'
import type { DateTime } from 'luxon'
import * as R from 'remeda'

import { useDebouncedValue } from 'src/hooks'
import { useEventHandler } from 'src/hooks/useEventHandler'
import useDeliveryGetDriversQuery from 'src/modules/delivery/api/drivers/useDeliveryGetDrivers'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import useGetDriversScheduleQuery from '../../api/appointment/useGetDriversScheduleQuery'
import { useUpdateDriverScheduleMutation } from '../../api/appointment/useUpdateDriverScheduleMutation'
import Header from '../../components/Header'
import { useDeliveryMainPageContext } from '../../contexts/DeliveryMainPageContext'
import SchedulerTimeline from './components/SchedulerTimeline'
import TimeRangeEditorPopover from './components/TimeRangeEditorPopover/TimeRangeEditorPopover'
import driverAvailabilitySchedulerEventHandler, {
  type DriverAvailabilitySchedulerEvent,
  type DriverAvailabilitySchedulerState,
  type ExistingSchedule,
} from './helpers'
import { DATA_RESIZE_HANDLE_ATTRIBUTE, DATA_RESOURCE_ID_ATTRIBUTE } from './utils'

export default function DeliveryTimeline() {
  return (
    <>
      <Header />
      <PageContent />
    </>
  )
}

const PageContent = () => {
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()

  const selectedDate = useMemo(() => {
    if (selectedDateRange) {
      return selectedDateRange.start
    }

    return DeliveryDateTime.now()
  }, [selectedDateRange])

  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebouncedValue(searchTerm, 300)

  const animationFrameRef = useRef<number | null>(null)

  const numberOfDays = 7
  const maxPickupDateTime: DateTime | undefined = undefined
  const minPickupDateTime: DateTime | undefined = undefined
  const maxDuration = {
    value: 1440,
    unit: 'minutes',
  }

  const getDateTimeFromX = (
    rowTimelineWidth: number,
    xOverTimeline: number,
    startOfDay: DateTime,
    totalRangeMinutes: number,
  ) => {
    const TIME_INCREMENT_MINUTES = 5
    const clampedX = R.clamp(xOverTimeline, { min: 0, max: rowTimelineWidth })
    const fraction = clampedX / rowTimelineWidth
    let offsetMin = fraction * totalRangeMinutes
    offsetMin = Math.round(offsetMin / TIME_INCREMENT_MINUTES) * TIME_INCREMENT_MINUTES
    return startOfDay.plus({ minutes: offsetMin })
  }

  const computedStartOfDay = useMemo(
    () =>
      selectedDate.set({
        hour: 0,
        minute: 0,
        second: 0,
        millisecond: 0,
      }),
    [selectedDate],
  )

  const computedEndOfDay = useMemo(
    () => computedStartOfDay.plus({ days: numberOfDays }),
    [computedStartOfDay, numberOfDays],
  )

  const computedTotalRangeMinutes =
    computedEndOfDay.diff(computedStartOfDay, 'minutes').minutes || 1440

  const deliveryDriverListQuery = useDeliveryGetDriversQuery()
  const resources = useMemo(() => {
    if (deliveryDriverListQuery.data && deliveryDriverListQuery.status === 'success') {
      return deliveryDriverListQuery.data
        .filter((driver) => driver.isActive)
        .map((driver) => ({
          id: driver.driverId,
          name: driver.fullName,
        }))
    }
    return []
  }, [deliveryDriverListQuery.data, deliveryDriverListQuery.status])

  const filteredResources = useMemo(() => {
    if (!debouncedSearchTerm.trim()) {
      return resources
    }

    const searchFilters: Filters<(typeof resources)[number]> = {
      search: [(resource) => resource.name],
    }

    const { itemMatchesWithTextAndFilters } =
      generateItemMatchesWithTextAndFilters(debouncedSearchTerm)

    return resources.filter((resource) =>
      itemMatchesWithTextAndFilters(resource, searchFilters),
    )
  }, [resources, debouncedSearchTerm])

  const [driversSchedule, setDriversSchedule] = useState<ExistingSchedule[]>([])

  const driversScheduleQuery = useGetDriversScheduleQuery({
    fromDate: selectedDate.toFormat('yyyy-MM-dd'),
    toDate: computedEndOfDay.toFormat('yyyy-MM-dd'),
  })

  useEffect(() => {
    if (driversScheduleQuery.data) {
      setDriversSchedule(driversScheduleQuery.data)
    }
  }, [driversScheduleQuery.data])

  const updateDriverScheduleMutation = useUpdateDriverScheduleMutation({
    fromDate: selectedDate.toFormat('yyyy-MM-dd'),
    toDate: computedEndOfDay.toFormat('yyyy-MM-dd'),
  })

  const dispatch = useEventHandler((event: DriverAvailabilitySchedulerEvent) => {
    driverAvailabilitySchedulerEventHandler({
      state: internalState,
      setState: setInternalState,
      event,
      isSchedulerDisabled: driversScheduleQuery.status === 'pending',

      blockedExistingSchedules: mappedExistingSchedules,
      startOfDay: computedStartOfDay,
      totalRangeMinutes: computedTotalRangeMinutes,
      onBookingSlotsUpdated: (updatedSlots, resourceId) => {
        if (resourceId) {
          const existingSchedules = mappedExistingSchedules.get(resourceId) || []
          const nonJobSlots = updatedSlots.filter(
            (slot) =>
              !existingSchedules.some(
                (schedule) =>
                  schedule.scheduleType === 'JOB' &&
                  schedule.start.toMillis() === slot.start.toMillis() &&
                  schedule.end.toMillis() === slot.end.toMillis(),
              ),
          )

          if (nonJobSlots.length === 0) {
            return
          }

          const { start: selectionStart, end: selectionEnd } =
            internalState.selectionMeta.value
          if (!selectionStart || !selectionEnd) {
            return
          }

          const modifiedFromDate = selectionStart.toFormat('yyyy-MM-dd')
          const modifiedToDate = selectionEnd.toFormat('yyyy-MM-dd')

          const slotsInModifiedDateRange = nonJobSlots.filter((slot) => {
            const slotDate = slot.start.toFormat('yyyy-MM-dd')
            return slotDate >= modifiedFromDate && slotDate <= modifiedToDate
          })

          if (slotsInModifiedDateRange.length === 0) {
            return
          }

          updateDriverScheduleMutation.mutate({
            deliveryDriverId: resourceId,
            schedule: slotsInModifiedDateRange.map((slot) => ({
              id: slot.id,
              busyFrom: `${slot.start.toFormat('yyyy-MM-dd')}T${slot.start.toFormat(
                'HH:mm:ssZZ',
              )}`,
              busyTo: `${slot.end.toFormat('yyyy-MM-dd')}T${slot.end.toFormat(
                'HH:mm:ssZZ',
              )}`,
              remark: slot.remark,
            })),
          })
        }
      },
    })
  })

  const handleMouseDown = (e: MouseEvent<HTMLDivElement>) => {
    if (driversScheduleQuery.status === 'pending') return
    const container = e.currentTarget as HTMLElement
    const containerRect = container.getBoundingClientRect()

    const rowElement = (e.target as HTMLElement).closest(
      `[${DATA_RESOURCE_ID_ATTRIBUTE}]`,
    )
    if (!rowElement) {
      return
    }
    const resourceId = rowElement.getAttribute(DATA_RESOURCE_ID_ATTRIBUTE)
    if (!resourceId) {
      return
    }

    const resizeHandle = (e.target as HTMLElement).getAttribute(
      DATA_RESIZE_HANDLE_ATTRIBUTE,
    ) as 'start' | 'end' | null

    if (resizeHandle) {
      e.preventDefault()
      dispatch({
        type: 'timelineDragStart',
        payload: {
          containerRect,
          clientX: e.clientX,
          clientY: e.clientY,
          resourceId: resourceId,
          resizeHandle,
        },
      })
      return
    }

    const localX = e.clientX - containerRect.left
    const rowTimelineWidth = containerRect.width
    const clickedTime = getDateTimeFromX(
      rowTimelineWidth,
      localX,
      computedStartOfDay,
      computedTotalRangeMinutes,
    )

    const driverSchedules = mappedExistingSchedules.get(resourceId) || []
    const matchingSchedule = driverSchedules.find(
      (schedule) => clickedTime >= schedule.start && clickedTime <= schedule.end,
    )

    e.preventDefault()

    if (matchingSchedule) {
      if (matchingSchedule.scheduleType === 'JOB') {
        return
      }

      const element = e.currentTarget as HTMLElement
      dispatch({
        type: 'onTimelineSelectionClick',
        mouseEvent: e,
        selection: {
          start: matchingSchedule.start,
          end: matchingSchedule.end,
          resourceId,
          rangeEditorAnchorEl: element,
          remark: matchingSchedule.remark,
        },
      })
    } else {
      dispatch({
        type: 'timelineDragStart',
        payload: {
          containerRect,
          clientX: e.clientX,
          clientY: e.clientY,
          resourceId: resourceId,
          resizeHandle,
        },
      })
    }
  }

  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (driversScheduleQuery.status === 'pending') {
      return
    }
    const containerRect = e.currentTarget.getBoundingClientRect()
    animationFrameRef.current = requestAnimationFrame(() => {
      dispatch({
        type: 'timelineDragMove',
        payload: { containerRect, clientX: e.clientX, clientY: e.clientY },
      })
      animationFrameRef.current = null
    })
  }

  const handleMouseUp = () => {
    dispatch({ type: 'timelineDragEnd' })
  }

  const handleRangeEditorChange = (
    newStart: DateTime,
    newEnd: DateTime,
    remark?: string,
  ) => {
    dispatch({
      type: 'onTimeRangeEditorPopoverChange',
      payload: {
        newStart,
        newEnd,
        remark,
      },
    })
  }

  const mappedExistingSchedules = useMemo((): Map<string, Array<ExistingSchedule>> => {
    const mapByDriverId = new Map<string, Array<ExistingSchedule>>()

    for (const schedule of driversSchedule) {
      const map = mapByDriverId.get(schedule.driverId)

      if (!map) {
        mapByDriverId.set(schedule.driverId, [schedule])
      } else {
        map.push(schedule)
      }
    }

    return mapByDriverId
  }, [driversSchedule])

  const [internalState, setInternalState] = useState(
    (): DriverAvailabilitySchedulerState => ({
      selectionMeta: {
        value: {
          start: null,
          end: null,
          resourceId: null,
          rangeEditorAnchorEl: null,
        },
      },
      dragMeta: null,
      cursorStyle: 'default',
    }),
  )

  const isEditingExistingSchedule = useMemo(() => {
    const { start, end, resourceId } = internalState.selectionMeta.value
    if (!start || !end || !resourceId) return false

    const schedules = mappedExistingSchedules.get(resourceId) || []
    return schedules.some(
      (schedule) =>
        schedule.start.toMillis() === start.toMillis() &&
        schedule.end.toMillis() === end.toMillis(),
    )
  }, [internalState.selectionMeta.value, mappedExistingSchedules])

  const isJobTypeSchedule = useMemo(() => {
    const { start, end, resourceId } = internalState.selectionMeta.value
    if (!start || !end || !resourceId) return false

    const schedules = mappedExistingSchedules.get(resourceId) || []
    return schedules.some(
      (schedule) =>
        schedule.start.toMillis() === start.toMillis() &&
        schedule.end.toMillis() === end.toMillis() &&
        schedule.scheduleType === 'JOB',
    )
  }, [internalState.selectionMeta.value, mappedExistingSchedules])

  return (
    <Box
      sx={() => ({
        display: 'flex',
        flexDirection: 'column',
        flex: '1 1 auto',
        gap: 3,
        py: 3,
        px: 3,
        position: 'relative',
        height: '100%',
        backgroundColor: 'white',
      })}
    >
      {driversScheduleQuery.status === 'pending' && (
        <LinearProgress
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        />
      )}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
        }}
      >
        <Box sx={{ textAlign: 'left' }}>
          <Typography
            variant="h6"
            sx={{ lineHeight: '32px' }}
          >
            {ctIntl.formatMessage({
              id: 'delivery.driverAvailability.title',
            })}
          </Typography>
          <Typography sx={(theme) => ({ color: theme.palette.text.secondary })}>
            {ctIntl.formatMessage({
              id: 'delivery.driverAvailability.body',
            })}
          </Typography>
        </Box>
        <TextField
          size="small"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={ctIntl.formatMessage({ id: 'Search Drivers' })}
          sx={{
            minWidth: 200,
            backgroundColor: 'background.paper',
            '& .MuiOutlinedInput-root': {
              height: '32px',
            },
          }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ fontSize: 16 }} />
                </InputAdornment>
              ),
            },
          }}
        />
      </Box>
      <Box
        sx={() => ({
          display: 'flex',
          flexDirection: 'column',
          py: 2.5,
          pl: 2.5,
          pr: 1,
          gap: 2,
          flex: '1 1 auto',
          position: 'relative',
          height: '100%',
          borderRadius: 2.5,
        })}
      >
        <Box
          sx={() => ({
            display: 'flex',
            flexDirection: 'column',
            flex: '1 1 auto',
            minHeight: 0,
            position: 'relative',
            height: '100%',
            px: 1,
            pl: 1.5,
            borderRadius: 1.5,
          })}
        >
          <SchedulerTimeline
            resources={filteredResources}
            existingSchedules={mappedExistingSchedules}
            internalState={internalState}
            startOfDay={computedStartOfDay}
            totalRangeMinutes={computedTotalRangeMinutes}
            numberOfDays={numberOfDays}
            disabled={driversScheduleQuery.status === 'pending'}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onTimelineSelectionClick={(mouseEvent, selection) => {
              dispatch({
                type: 'onTimelineSelectionClick',
                mouseEvent,
                selection: {
                  ...selection,
                  rangeEditorAnchorEl: mouseEvent.currentTarget as HTMLElement,
                },
              })
            }}
          />
        </Box>
      </Box>

      {!!internalState.selectionMeta.value &&
        !!internalState.selectionMeta.value.rangeEditorAnchorEl && (
          <TimeRangeEditorPopover
            anchorEl={
              internalState.selectionMeta.value.rangeEditorAnchorEl as HTMLElement
            }
            initialStart={internalState.selectionMeta.value.start}
            initialEnd={internalState.selectionMeta.value.end}
            initialRemark={internalState.selectionMeta.value.remark}
            onClose={() => dispatch({ type: 'onTimeRangeEditorPopoverClose' })}
            onCloseButtonClick={() =>
              dispatch({ type: 'onTimeRangeEditorPopoverCloseButtonClick' })
            }
            onChangeTimeRange={handleRangeEditorChange}
            onDeleteButtonClick={() =>
              dispatch({
                type: 'onTimeRangeEditorDeleteIconClick',
              })
            }
            onSaveButtonClick={() =>
              dispatch({
                type: 'onTimeRangeEditorSaveIconClick',
              })
            }
            maxPickupDateTime={maxPickupDateTime}
            minPickupDateTime={minPickupDateTime}
            maxDuration={maxDuration}
            isExistingSchedule={isEditingExistingSchedule}
            isJobSchedule={isJobTypeSchedule}
            isSaving={updateDriverScheduleMutation.isSaving}
          />
        )}
    </Box>
  )
}
