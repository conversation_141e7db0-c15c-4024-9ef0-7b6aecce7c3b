import { useCallback, useMemo, useRef } from 'react'
import {
  Box,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  Stack,
  Tooltip,
} from '@karoo-ui/core'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import PersonRemoveOutlinedIcon from '@mui/icons-material/PersonRemoveOutlined'
import UploadOutlinedIcon from '@mui/icons-material/UploadOutlined'
import type { DateTime } from 'luxon'
import { useHistory, useLocation } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import useUnassignJob from 'src/modules/deliveryRevamp/api/jobs/useUnassignJob'
import useChangeRouteDate from 'src/modules/deliveryRevamp/api/routes/useChangeRouteDate'
import useDeleteSliceJobs from 'src/modules/deliveryRevamp/api/routes/useDeleteSliceJobs'
import useDuplicateRoute from 'src/modules/deliveryRevamp/api/routes/useDuplicateRoute'
import type { RoutesList } from 'src/modules/deliveryRevamp/api/routes/useRoutesList'
import DeliveryDatePicker from 'src/modules/deliveryRevamp/components/DeliveryDatePicker'
import { IMPORT_TYPE } from 'src/modules/deliveryRevamp/constants'
import { FORM_STATE, SCHEDULE_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { FLEXIBLE_DATE_FORMAT } from 'src/modules/deliveryRevamp/helpers'
import useActiveImport from 'src/modules/deliveryRevamp/hooks/useActiveImport'
import type { ActiveImportType } from 'src/modules/deliveryRevamp/slice'
import confirmUpdateRecurringRoute, {
  UPDATE_ROUTE_OPTION,
} from 'src/modules/deliveryRevamp/utils/confirmUpdateRecurringRoute'
import { ctIntl } from 'src/util-components/ctIntl'

import { DeliveryDateTime } from '../../../utils/deliveryDateTime'
import Popover, { type PopoverHandles } from '../../Popover'
import { getDeliveryRecurringDialogMainPath } from '../../RecurringDialog/helpers'

type Props = {
  route: RoutesList.Route
}

const RouteMenu = ({ route }: Props) => {
  const ref = useRef<PopoverHandles>(null)
  const {
    data: { selectedDateRange, isToday, isPastDate },
  } = useDeliveryMainPageContext()
  const isAllStarted = useMemo(
    () => route.notStartedJobIds.length === 0,
    [route.notStartedJobIds.length],
  )

  const location = useLocation()
  const history = useHistory()
  const { mutate: unassignJobs } = useUnassignJob()
  const { mutate: deleteRoute } = useDeleteSliceJobs()
  const { mutate: duplicateRoute } = useDuplicateRoute()
  const { mutate: changeRouteDate } = useChangeRouteDate()
  const { handleSetActiveImport } = useActiveImport()

  const handleUnassignJobs = useCallback(() => {
    unassignJobs({ jobIds: route.assignableJobIds, routeId: route.routeId })
    ref.current?.closePopover()
  }, [route.assignableJobIds, route.routeId, unassignJobs])

  const handleDeleteRoute = useCallback(() => {
    Dialog.alert({
      title: ctIntl.formatMessage({ id: 'delivery.deleteRoute.confirmation.title' }),
      content: (
        <>
          <Box>
            {ctIntl.formatMessage({
              id: 'delivery.deleteRoute.confirmation.body1',
            })}
          </Box>
          <Box>
            {ctIntl.formatMessage({
              id: 'delivery.deleteRoute.confirmation.body2',
            })}
          </Box>
        </>
      ),
      confirmButtonLabel: ctIntl.formatMessage({ id: 'Yes, Delete' }),
      onResult() {
        deleteRoute({ route })
        ref.current?.closePopover()
      },
    })
  }, [deleteRoute, route])

  const handleImportJobsToRoute = useCallback(async () => {
    const schedule = match({ selectedDateRange, isToday })
      .with({ isToday: true }, () => ({
        scheduleTypeId: SCHEDULE_TYPE_ID.SCHEDULE,
        scheduledDeliveryTs: null,
      }))
      .with({ selectedDateRange: P.nonNullable }, ({ selectedDateRange }) => ({
        scheduleTypeId: SCHEDULE_TYPE_ID.SCHEDULE,
        scheduledDeliveryTs: selectedDateRange.start
          .startOf('second')
          .toISO({ suppressMilliseconds: true }),
      }))
      .otherwise(() => ({
        scheduleTypeId: SCHEDULE_TYPE_ID.UNSCHEDULE,
        scheduledDeliveryTs: null,
      }))

    let assignTo: ActiveImportType = route.planId
      ? {
          importType: IMPORT_TYPE.PLAN,
          planId: route.planId,
          ...schedule,
        }
      : {
          importType: IMPORT_TYPE.DRIVER,
          deliveryDriverId: route.deliveryDriverId as string,
          ...schedule,
        }
    if (route.isFullRecurring && route.deliveryDriverId && route.uniquePlanId) {
      try {
        const result = await confirmUpdateRecurringRoute()
        if (result === UPDATE_ROUTE_OPTION.RECURRING) {
          assignTo = {
            ...schedule,
            importType: IMPORT_TYPE.PLAN,
            planId: route.uniquePlanId,
          }
        } else {
          assignTo = {
            ...schedule,
            importType: IMPORT_TYPE.DRIVER,
            deliveryDriverId: route.deliveryDriverId,
          }
        }
      } catch {
        return
      }
    }

    handleSetActiveImport(assignTo)

    ref.current?.closePopover()
  }, [
    handleSetActiveImport,
    isToday,
    route.deliveryDriverId,
    route.isFullRecurring,
    route.planId,
    route.uniquePlanId,
    selectedDateRange,
  ])

  const handleMakeRecurringRoute = useCallback(() => {
    const { orderedStops, deliveryDriverId, routeName, planIds } = route
    const stopIds = orderedStops.map((item) => item.stopId)
    const jobIds = Array.from(new Set(orderedStops.map((item) => item.jobId)))

    history.push(
      getDeliveryRecurringDialogMainPath(location, {
        formParams: {
          existingFormField: {
            name: routeName ?? '',
            targetDriverId: deliveryDriverId,
            orderedStopIds: stopIds,
            scheduledTime: DeliveryDateTime.now().toFormat(FLEXIBLE_DATE_FORMAT),
            formState: match(planIds.length)
              .with(0, () => FORM_STATE.ROUTE_COPY_ASSIGN)
              .with(1, () => FORM_STATE.ROUTE_CONVERT)
              .otherwise(() => FORM_STATE.ROUTE_COPY),
            ...(planIds.length === 1 ? { planId: planIds[0] } : {}),
          },
          additionalFormField: {
            jobIds: jobIds,
          },
        },
      }),
    )
  }, [history, location, route])

  const handleChangeDeliveryDate = useCallback(() => {
    let toDate: DateTime | null =
      selectedDateRange === null ? DeliveryDateTime.now().startOf('day') : null

    Dialog.alert({
      title: ctIntl.formatMessage({
        id: 'Change the delivery date for this route and its jobs',
      }),
      content: (
        <Stack gap={1}>
          <Box>
            <Box component="span">
              {ctIntl.formatMessage({
                id: 'Change from',
              })}
            </Box>{' '}
            <Box
              component="span"
              sx={{ fontWeight: 'bold' }}
            >
              “
              {match({ isToday, selectedDateRange })
                .with({ selectedDateRange: P.nullish }, () =>
                  ctIntl.formatMessage({
                    id: 'Unscheduled',
                  }),
                )
                .with(
                  { isToday: true, selectedDateRange: P.nonNullable },
                  ({ selectedDateRange }) =>
                    `${ctIntl.formatMessage({
                      id: 'Today',
                    })}, ${selectedDateRange.start.toFormat('D ccc')}`,
                )
                .otherwise(({ selectedDateRange }) =>
                  selectedDateRange ? selectedDateRange.start.toFormat('D ccc') : '',
                )}
              ”
            </Box>{' '}
            <Box
              component="span"
              sx={{ textTransform: 'lowercase' }}
            >
              {ctIntl.formatMessage({
                id: 'To',
              })}
              {':'}
            </Box>
          </Box>
          <DeliveryDatePicker
            disableUnscheduled={selectedDateRange === null}
            datePickerProps={{
              defaultValue: toDate,
              autoFocus: true,
              disablePast: true,
              shouldDisableDate: (date) =>
                selectedDateRange
                  ? date.hasSame(selectedDateRange.start, 'day')
                  : false,
              onChange: (date) => {
                toDate = date
              },
            }}
          />
          <Box>
            {ctIntl.formatMessage({
              id: 'Please note that jobs that have been started will not be affected.',
            })}
          </Box>
        </Stack>
      ),
      confirmButtonLabel: ctIntl.formatMessage({ id: 'Save' }),
      onResult() {
        changeRouteDate({
          route,
          toDate,
        })
        ref.current?.closePopover()
      },
    })
  }, [changeRouteDate, isToday, route, selectedDateRange])

  const handleDuplicate = useCallback(() => {
    let toDate: DateTime | null =
      selectedDateRange === null ? DeliveryDateTime.now().startOf('day') : null

    Dialog.alert({
      title: ctIntl.formatMessage({
        id: 'delivery.route.duplicate.confirmation.title',
      }),
      content: (
        <Stack gap={1}>
          <Box component="span">
            {ctIntl.formatMessage({
              id: 'delivery.route.duplicate.confirmation.body1',
            })}
            {':'}
          </Box>
          <DeliveryDatePicker
            disableUnscheduled={selectedDateRange === null}
            datePickerProps={{
              defaultValue: toDate,
              autoFocus: true,
              disablePast: true,
              shouldDisableDate: (date) =>
                selectedDateRange
                  ? date.hasSame(selectedDateRange.start, 'day')
                  : false,
              onChange: (date) => {
                toDate = date
              },
            }}
          />
        </Stack>
      ),
      confirmButtonLabel: ctIntl.formatMessage({ id: 'Save' }),
      onResult() {
        duplicateRoute({ toDate, fromRouteId: route.routeId, toRouteId: route.routeId })
        ref.current?.closePopover()
      },
    })
  }, [duplicateRoute, route.routeId, selectedDateRange])

  const makeRecurringRouteDisabledState = useMemo(():
    | {
        disabled: true
        reason: string
      }
    | { disabled: false } => {
    if (route.isFullRecurring) {
      return {
        disabled: true,
        reason: ctIntl.formatMessage({ id: 'delivery.route.alreadyRecurring' }),
      }
    }

    if (route.completedJobs > 0) {
      return {
        disabled: true,
        reason: ctIntl.formatMessage({
          id: 'delivery.route.makeRecurring.error.completedJobs',
        }),
      }
    }

    return { disabled: false }
  }, [route.completedJobs, route.isFullRecurring])

  return (
    <Popover
      ref={ref}
      content={
        <MenuList>
          <MenuItem
            onClick={handleUnassignJobs}
            disabled={route.assignableJobIds.length === 0}
          >
            <ListItemIcon>
              <PersonRemoveOutlinedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'Unassign all jobs' })}
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleDeleteRoute}
            disabled={
              route.orderedStops.length > 0 && route.notStartedJobIds.length === 0
            }
          >
            <ListItemIcon>
              <DeleteOutlineOutlinedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'delivery.route.action.delete' })}
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleImportJobsToRoute}
            disabled={isPastDate}
          >
            <ListItemIcon>
              <UploadOutlinedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'delivery.route.action.importJobs' })}
            </ListItemText>
          </MenuItem>

          <Tooltip
            title={
              makeRecurringRouteDisabledState.disabled
                ? makeRecurringRouteDisabledState.reason
                : ''
            }
            placement="left"
          >
            <span>
              <MenuItem
                onClick={handleMakeRecurringRoute}
                disabled={makeRecurringRouteDisabledState.disabled}
              >
                <ListItemIcon>
                  <LoopOutlinedIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>
                  {ctIntl.formatMessage({ id: 'delivery.route.action.makeRecurring' })}
                </ListItemText>
              </MenuItem>
            </span>
          </Tooltip>

          <MenuItem
            onClick={handleChangeDeliveryDate}
            disabled={isAllStarted}
          >
            <ListItemIcon>
              <CalendarTodayIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'delivery.route.action.changeDeliveryDate' })}
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleDuplicate}
            disabled={route.orderedStops.length === 0}
          >
            <ListItemIcon>
              <ContentCopyIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'global.duplicate' })}
            </ListItemText>
          </MenuItem>
        </MenuList>
      }
    >
      <Tooltip title={ctIntl.formatMessage({ id: 'delivery.moreActions' })}>
        <IconButton size="small">
          <MoreHorizIcon fontSize="inherit" />
        </IconButton>
      </Tooltip>
    </Popover>
  )
}

export default RouteMenu
