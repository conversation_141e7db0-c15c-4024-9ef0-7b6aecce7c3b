import { useState } from 'react'
import {
  Button,
  TimePicker,
  type InputBaseComponentProps,
  type TimePickerProps,
  type UseTimeFieldProps,
} from '@karoo-ui/core'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import type { DateTime } from 'luxon'
import type { Except } from 'type-fest'

type ButtonFieldProps = {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
} & UseTimeFieldProps<DateTime, false> &
  InputBaseComponentProps

function ButtonField(props: ButtonFieldProps) {
  const { setOpen, label, disabled, InputProps: { ref } = {} } = props

  return (
    <Button
      variant="text"
      size="small"
      color="secondary"
      endIcon={<ArrowDropDownIcon sx={{ fontSize: 'inherit' }} />}
      disabled={disabled}
      ref={ref}
      onClick={() => setOpen?.((prev) => !prev)}
    >
      {label}
    </Button>
  )
}

export default function ButtonTimePicker(
  props: Except<TimePickerProps<DateTime>, 'open' | 'onOpen' | 'onClose'>,
) {
  const [open, setOpen] = useState(false)

  return (
    <TimePicker
      slots={{ ...props.slots, field: ButtonField }}
      slotProps={{ ...props.slotProps, field: { setOpen } as any }}
      {...props}
      open={open}
      onClose={() => setOpen(false)}
      onOpen={() => setOpen(true)}
    />
  )
}
