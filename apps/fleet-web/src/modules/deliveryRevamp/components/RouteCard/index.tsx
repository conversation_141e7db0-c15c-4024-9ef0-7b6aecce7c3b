import { useMemo } from 'react'
import {
  Box,
  Button,
  IconButton,
  Stack,
  styled,
  Tooltip,
  Typography,
  useTheme,
  type SxProps,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import AutoAwesomeOutlinedIcon from '@mui/icons-material/AutoAwesomeOutlined'
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import LocationDisabledOutlinedIcon from '@mui/icons-material/LocationDisabledOutlined'
import LoopIcon from '@mui/icons-material/Loop'
import MyLocationOutlinedIcon from '@mui/icons-material/MyLocationOutlined'
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined'
import WarningIcon from '@mui/icons-material/Warning'
// eslint-disable-next-line no-restricted-imports
import { alpha } from '@mui/material/styles'
// eslint-disable-next-line no-restricted-imports
import type { Theme } from '@mui/system/createTheme'
import { produce } from 'immer'
import type { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import type { DriversList } from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import type { RoutesList } from 'src/modules/deliveryRevamp/api/routes/useRoutesList'
import {
  DROPPABLE_TYPE,
  useDroppable,
} from 'src/modules/deliveryRevamp/contexts/DeliveryDndJobsContext'
import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { ctIntl } from 'src/util-components/ctIntl'

import useOptimizeRoute from '../../api/routes/useOptimizeRoute'
import useRouteLegs from '../../api/routes/useRouteLegs'
import { DELIVERY_COLOR } from '../../constants/colors'
import { STOP_STATUS_ID } from '../../constants/job'
import { useDeliveryMainPageContext } from '../../contexts/DeliveryMainPageContext'
import { DeliveryDateTime } from '../../utils/deliveryDateTime'
import DriverChip, { type Props as DriverChipProps } from '../DriverChip'
import Popover from '../Popover'
import ViewDriverSettings from '../ViewDriverSettings'
import ButtonTimePicker from './component/ButtonTimePicker'
import DotDivider from './component/DotDivider'
import EditableRouteName from './component/EditableRouteName'
import RecurringTooltip from './component/RecurringTooltip'
import RouteMenu from './component/RouteMenu'

type Props = {
  route: RoutesList.Route
  driverInfo: {
    driver: DriversList.Driver | undefined
    driverFullName: string | undefined
    isDriverDeactivated: boolean
  }
  expanded: boolean
  stopsLength?: number
  showAddButton?: boolean
  onExpandedChange: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
  isHovered?: boolean
  isSelected?: boolean
  onClick?: (e: React.MouseEvent<HTMLElement>) => void
  basedOnTime?: DateTime
  setBasedOnTimeMap: (
    value: React.SetStateAction<Record<RoutesList.Route['routeId'], DateTime>>,
  ) => void
  onClickAddJob: (route: RoutesList.Route) => void
  sx?: SxProps<Theme>
  onAssignSuccessAndRefresh?: DriverChipProps['onAssignSuccessAndRefresh']
  showRefreshETA: boolean
}

export function calcBasedOnTime(
  route: RoutesList.Route,
  driver: DriversList.Driver | undefined,
) {
  let basedOnTime = DeliveryDateTime.now()
  const routeDateTime = route.fromTs
    ? DeliveryDateTime.fromJSDate(new Date(route.fromTs))
    : null
  const driverShiftTimeStart = driver?.shiftTimeStart
    ? DeliveryDateTime.fromJSDate(
        new Date(
          `${DeliveryDateTime.now().toFormat('yyyy-MM-dd')} ${driver.shiftTimeStart}`,
        ),
      )
    : null
  const isFutureRoute =
    routeDateTime &&
    routeDateTime > basedOnTime &&
    routeDateTime.diff(basedOnTime, 'days')
  if (isFutureRoute) {
    basedOnTime = driverShiftTimeStart || DeliveryDateTime.now().startOf('day')
  } else if (route.orderedStops.length >= 2) {
    const firstStartedStopIndex = route.orderedStops.findIndex(
      (stop) => stop.activityStartedTs && stop.stopStatusId === STOP_STATUS_ID.STARTED,
    )
    if (firstStartedStopIndex !== -1) {
      const firstStartedStop = route.orderedStops[firstStartedStopIndex]
      const preStop = route.orderedStops[firstStartedStopIndex - 1]
      if (
        preStop &&
        typeof preStop.etaInSeconds === 'number' &&
        typeof firstStartedStop.etaInSeconds === 'number'
      ) {
        const startedTime = DeliveryDateTime.fromJSDate(
          new Date(firstStartedStop.activityStartedTs as string),
        )
        const travelTime = firstStartedStop.etaInSeconds - preStop.etaInSeconds
        basedOnTime =
          DeliveryDateTime.now() > startedTime.plus({ seconds: travelTime })
            ? DeliveryDateTime.now().minus({ seconds: travelTime })
            : startedTime
      }
    }
  }
  return basedOnTime
}

export const RouteHeader = ({
  route,
  driverInfo,
  expanded,
  stopsLength,
  onExpandedChange,
  showAddButton,
  sx,
  onMouseEnter,
  onMouseLeave,
  isHovered,
  isSelected,
  onClick,
  basedOnTime = calcBasedOnTime(route, driverInfo.driver),
  setBasedOnTimeMap,
  onClickAddJob,
  onAssignSuccessAndRefresh,
  showRefreshETA = false,
}: Props) => {
  const theme = useTheme()
  const {
    data: { isPastDate, selectedDateRange },
  } = useDeliveryMainPageContext()
  const { mutate: optimizeRoute, isPending: isOptimizePending } = useOptimizeRoute()

  const { data: routeLegs } = useRouteLegs(route.routeId, {
    enabled: isHovered || isSelected,
  })

  const { setNodeRef, isOver } = useDroppable({
    id: route.routeId,
    data: { type: DROPPABLE_TYPE.ROUTES_PANEL_ADD_BUTTON, route },
  })

  const isTheRouteOptimized = useMemo(
    () => routeLegs && routeLegs.stopLegs.length > 0,
    [routeLegs],
  )

  const isFutureDate =
    selectedDateRange?.start &&
    selectedDateRange.start.startOf('day') > DeliveryDateTime.now().startOf('day')

  const isRouteCompleted =
    route.totalJobs > 0 && route.completedJobs === route.totalJobs

  const showOptimize = isHovered
  const isTheRouteOptimizable =
    route.assignableJobIds.length >= 2 && !isPastDate && !isRouteCompleted
  const isOptimizeButtonDisabled = useMemo(
    () =>
      match({ isTheRouteOptimizable, isTheRouteOptimized })
        .with({ isTheRouteOptimizable: true }, () => false)
        .with({ isTheRouteOptimizable: false, isTheRouteOptimized: true }, () => false)
        .otherwise(() => true),
    [isTheRouteOptimizable, isTheRouteOptimized],
  )

  const isDriverAssignmentDisabled = isPastDate && !isRouteCompleted
  const isRouteNameChangeDisabled = isPastDate
  const showBusyStatus = useMemo(
    () =>
      driverInfo.driver?.isBusy &&
      selectedDateRange?.start &&
      selectedDateRange.start >= DeliveryDateTime.now().startOf('day'),
    [driverInfo.driver?.isBusy, selectedDateRange],
  )

  const nextStopETA = useMemo(() => {
    const getNextEta = (statusId: STOP_STATUS_ID) => {
      const idx = route.orderedStops.findIndex((stop) => stop.stopStatusId === statusId)
      if (idx !== -1) {
        const curr = route.orderedStops[idx]
        const prev = route.orderedStops[idx - 1]
        if (
          prev &&
          typeof prev.etaInSeconds === 'number' &&
          typeof curr.etaInSeconds === 'number'
        ) {
          const travelTime = curr.etaInSeconds - prev.etaInSeconds
          return basedOnTime.plus({ seconds: travelTime })
        }
      }
      return null
    }

    // Try STARTED, then CREATED, fallback to basedOnTime
    return (
      getNextEta(STOP_STATUS_ID.STARTED) ??
      getNextEta(STOP_STATUS_ID.CREATED) ??
      basedOnTime
    )
  }, [basedOnTime, route.orderedStops])

  return (
    <>
      <Stack
        direction="row"
        alignItems="center"
        flexWrap="wrap"
        justifyContent="space-between"
        flexGrow={1}
        gap={1}
        sx={{
          overflow: 'hidden',
          ...sx,
          ...(isHovered && {
            backgroundColor: 'action.hover',
          }),
          ...(isSelected && {
            backgroundColor: alpha(
              theme.palette.primary.main,
              theme.palette.action.selectedOpacity,
            ),
          }),
        }}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onClick={onClick}
      >
        <Stack
          direction="row"
          alignItems="center"
          flexGrow={99}
          flexShrink={1}
          gap="0 2px"
          sx={{ overflow: 'hidden' }}
        >
          <DriverChip
            driver={
              driverInfo.driver
                ? driverInfo.driver
                : { fullName: driverInfo.driverFullName ?? '' }
            }
            isDriverDeactivated={driverInfo.isDriverDeactivated}
            route={route}
            canExpand={!isDriverAssignmentDisabled}
            onAssignSuccessAndRefresh={onAssignSuccessAndRefresh}
          />

          <EditableRouteName
            key={route.routeName}
            route={route}
            isHovered={isHovered}
            disabled={isRouteNameChangeDisabled}
          />

          {route.isFullRecurring && (
            <>
              <DotDivider />
              <Stack
                direction="row"
                alignItems="baseline"
              >
                <RecurringTooltip
                  planName={route.planName}
                  planId={route.planIds[0]}
                  recurrence={route.routeRecurrence}
                >
                  <LoopIcon sx={{ fontSize: 18, color: 'action.active' }} />
                </RecurringTooltip>
                {!route.recurrenceStartDate && (
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: `The recurring start date is not set, so the route hasn't begun. Please enter a start date.`,
                    })}
                  >
                    <WarningIcon sx={{ fontSize: 11, color: 'warning.main' }} />
                  </Tooltip>
                )}
              </Stack>
            </>
          )}
        </Stack>
        <Stack
          direction="row"
          alignItems="center"
          flexWrap="wrap"
          justifyContent="space-between"
          flexGrow={1}
          gap={0.5}
        >
          {route.orderedStops.length > 0 && (
            <Stack
              direction="row"
              flexWrap="wrap"
              alignItems="center"
            >
              {driverInfo.driver ? (
                <>
                  {route.isOnRoute && (
                    <>
                      <Typography
                        variant="caption"
                        sx={{ color: 'primary.main' }}
                      >
                        {ctIntl.formatMessage({
                          id: 'On route',
                        })}
                      </Typography>
                      <DotDivider />
                    </>
                  )}

                  {route.lateCount > 0 && (
                    <>
                      <Typography
                        variant="caption"
                        sx={{ color: 'error.main' }}
                      >
                        {ctIntl.formatMessage(
                          { id: 'delivery.routes.jobsLate' },
                          { values: { count: route.lateCount } },
                        )}
                      </Typography>
                      <DotDivider />
                    </>
                  )}

                  {showBusyStatus && (
                    <Tooltip
                      title={
                        <Box>
                          {ctIntl.formatMessage({
                            id: 'Driver is busy. This driver has reached the stop limit.',
                          })}{' '}
                          <ViewDriverSettings />
                        </Box>
                      }
                    >
                      <ErrorOutlineOutlinedIcon
                        sx={{ fontSize: 16, color: 'warning.main', mr: 0.2 }}
                      />
                    </Tooltip>
                  )}

                  <Typography
                    variant="caption"
                    sx={{ color: 'text.secondary', lineHeight: 1 }}
                  >
                    {ctIntl.formatMessage(
                      { id: 'delivery.routes.jobsCompleted' },
                      {
                        values: {
                          count: route.completedJobs,
                          total: route.totalJobs,
                        },
                      },
                    )}
                  </Typography>
                </>
              ) : (
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary' }}
                >
                  {ctIntl.formatMessage(
                    { id: 'delivery.routes.unassignedJobs' },
                    {
                      values: {
                        count: route.totalJobs,
                      },
                    },
                  )}
                </Typography>
              )}
            </Stack>
          )}

          <Stack
            direction="row"
            alignItems="center"
          >
            {route.orderedStops.length > 0 && showOptimize && (
              <Popover
                content={
                  <Stack
                    gap={1}
                    sx={{ p: 1 }}
                  >
                    <Typography variant="subtitle2">
                      {ctIntl.formatMessage({ id: 'Optimise route' })}
                    </Typography>
                    {isTheRouteOptimized && (
                      <Stack
                        direction="row"
                        alignItems="center"
                        gap={0.5}
                        sx={{ color: 'text.secondary' }}
                      >
                        {driverInfo.driver?.isOnline ? (
                          <MyLocationOutlinedIcon
                            sx={{ fontSize: 14, color: 'success.main' }}
                          />
                        ) : (
                          <LocationDisabledOutlinedIcon sx={{ fontSize: 14 }} />
                        )}
                        {ctIntl.formatMessage({ id: 'Next stop ETA' })}
                        <ButtonTimePicker
                          label={nextStopETA.toFormat('HH:mm')}
                          value={nextStopETA}
                          disablePast={!isFutureDate}
                          onChange={(time) => {
                            if (time)
                              setBasedOnTimeMap(
                                produce((draft) => {
                                  draft[route.routeId] = time
                                }),
                              )
                          }}
                        />
                        <Tooltip
                          title={ctIntl.formatMessage({ id: 'Refresh ETA' })}
                          onClick={() => {
                            setBasedOnTimeMap(
                              produce((draft) => {
                                delete draft[route.routeId]
                              }),
                            )
                          }}
                        >
                          <IconButton
                            size="small"
                            color="primary"
                            sx={{
                              visibility: showRefreshETA ? 'visible' : 'hidden',
                            }}
                          >
                            <RefreshOutlinedIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    )}
                    <Button
                      size="small"
                      variant="contained"
                      disableElevation={false}
                      disabled={!isTheRouteOptimizable}
                      loading={isOptimizePending}
                      onClick={() => {
                        if (
                          route.orderedStops.some(
                            (stop) => stop.stopStatusId === STOP_STATUS_ID.STARTED,
                          )
                        ) {
                          Dialog.alert({
                            title: ctIntl.formatMessage({
                              id: 'Optimize started jobs?',
                            }),
                            content: ctIntl.formatMessage({
                              id: 'delivery.optimizeStartedJobs.subtext',
                            }),
                            confirmButtonLabel: ctIntl.formatMessage({
                              id: 'Proceed',
                            }),
                            rejectButtonLabel: ctIntl.formatMessage({
                              id: 'Quit',
                            }),
                            onResult: () => {
                              optimizeRoute({ routeId: route.routeId })
                            },
                          })
                        } else {
                          optimizeRoute({ routeId: route.routeId })
                        }
                      }}
                    >
                      {ctIntl.formatMessage({
                        id: 'delivery.rightPanel.driver.optimize',
                      })}
                    </Button>
                  </Stack>
                }
              >
                <Button
                  disabled={isOptimizeButtonDisabled}
                  size="small"
                  startIcon={<AutoAwesomeOutlinedIcon />}
                  sx={{
                    lineHeight: 1,
                    color: DELIVERY_COLOR.PURPLE,
                    '&:hover': {
                      backgroundColor: alpha(
                        DELIVERY_COLOR.PURPLE,
                        theme.palette.action.hoverOpacity,
                      ),
                    },
                  }}
                >
                  {ctIntl.formatMessage({
                    id: 'delivery.rightPanel.driver.optimize',
                  })}
                </Button>
              </Popover>
            )}

            <RouteMenu route={route} />

            <Tooltip
              title={`${ctIntl.formatMessage({
                id: 'Collapse',
              })}/${ctIntl.formatMessage({ id: 'Expand' })}`}
            >
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  onExpandedChange()
                }}
              >
                <ExpandMoreIcon
                  fontSize="inherit"
                  sx={{
                    transform: expanded ? 'rotate(0deg)' : 'rotate(90deg)',
                    transition: 'transform .2s',
                  }}
                />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Stack>

      {match({ stopsLength, showAddButton, expanded })
        .with({ expanded: false }, () => null)
        .with({ stopsLength: 0, showAddButton: false }, () => (
          <Stack sx={{ pb: 2 }}>
            <Typography
              variant="caption"
              sx={{ color: 'text.secondary', textAlign: 'center' }}
            >
              {ctIntl.formatMessage({
                id: 'delivery.routes.noJobStopsAssigned',
              })}
            </Typography>
          </Stack>
        ))
        .with({ showAddButton: true }, () => (
          <Stack
            sx={{
              px: 2.5,
              pb: 2,
              ...(isHovered && {
                backgroundColor: 'action.hover',
              }),
              ...(isSelected && {
                backgroundColor: alpha(
                  theme.palette.primary.main,
                  theme.palette.action.selectedOpacity,
                ),
              }),
            }}
          >
            <Button
              ref={setNodeRef}
              variant="outlined"
              color="secondary"
              size="small"
              startIcon={<AddIcon />}
              sx={{
                width: 'auto',
                height: '86px',
                borderRadius: 1,
                border: '2px dashed',
                borderColor: isOver ? 'primary.main' : 'grey.300',
                color: 'text.secondary',
                ...(isOver && {
                  backgroundColor: 'action.hover',
                }),
              }}
              onClick={() => onClickAddJob(route)}
            >
              {ctIntl.formatMessage({ id: 'delivery.routes.addJobStops' })}&nbsp;
              <Typography
                variant="caption"
                sx={{
                  color: 'text.secondary',
                  textTransform: 'lowercase',
                }}
              >
                {ctIntl.formatMessage({
                  id: 'delivery.routes.addJobStopsDragHere',
                })}
              </Typography>
            </Button>
          </Stack>
        ))
        .otherwise(() => null)}
    </>
  )
}

export const StopsGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(230px, 1fr))',
  gap: theme.spacing(1),
}))
