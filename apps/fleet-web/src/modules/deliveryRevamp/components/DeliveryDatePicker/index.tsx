import { DatePicker, type DatePickerProps } from '@karoo-ui/core'
import type { DateTime } from 'luxon'

import { ctIntl } from 'src/util-components/ctIntl'

import { generateShortcuts, generateSlots } from '../DatePickerPopper'

export default function DeliveryDatePicker({
  datePickerProps,
  disableUnscheduled = false,
}: {
  datePickerProps?: DatePickerProps<DateTime>
  disableUnscheduled?: boolean
}) {
  return (
    <DatePicker
      slots={{
        ...generateSlots(),
        toolbar: () => null,
      }}
      slotProps={{
        shortcuts: {
          items: generateShortcuts(disableUnscheduled),
        },
        toolbar: {
          hidden: true,
        },
        textField: {
          placeholder: ctIntl.formatMessage({ id: 'Unscheduled' }),
        },
      }}
      {...datePickerProps}
    />
  )
}
