import { useCallback, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Typography } from '@karoo-ui/core'
import CloseFilledIcon from '@mui/icons-material/Close'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import SwapHorizOutlinedIcon from '@mui/icons-material/SwapHorizOutlined'

import { useDeliverySettingsContext } from 'src/modules/deliveryRevamp/contexts/DeliverySettingsContext'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  DELIVERY_VERSION_SWITCH_REMIND_ME_LATER_STORAGE,
  useSwitchVersion,
} from '../../contexts/DeliverySwitchVersionContext'

export const DELIVERY_VERSION_SWITCH_FRAME_HEIGHT_IN_PX = 54

const DeliveryVersionSwitchFrame = () => {
  const {
    updateDeliverySettings,
    localDesignStyleCurrent,
    setLocalDesignStyleCurrent,
  } = useDeliverySettingsContext()

  const { shouldDisplaySwitchFrame, setShouldDisplaySwitchFrame } = useSwitchVersion()

  const [isSwitchingVersion, setIsSwitchingVersion] = useState(false)

  const isUsingNewStyle = localDesignStyleCurrent === 'new'

  const handleClose = useCallback(() => {
    setShouldDisplaySwitchFrame(false)

    sessionStorage.setItem(DELIVERY_VERSION_SWITCH_REMIND_ME_LATER_STORAGE, 'true')
  }, [setShouldDisplaySwitchFrame])

  if (!shouldDisplaySwitchFrame) {
    return null
  }

  return (
    <Stack
      direction="row"
      sx={{
        height: `${DELIVERY_VERSION_SWITCH_FRAME_HEIGHT_IN_PX}px`,
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingX: 3,
        paddingY: 1.5,
        backgroundColor: isUsingNewStyle ? '#********' : '#********',
      }}
    >
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
      >
        {isUsingNewStyle ? (
          <span
            role="img"
            aria-label="frame-emoji"
          >
            🌟
          </span>
        ) : (
          <InfoOutlinedIcon color="error" />
        )}
        <Typography>
          {` ${ctIntl.formatMessage({
            id: isUsingNewStyle
              ? 'delivery.switchBanner.revamp.title'
              : 'delivery.switchBanner.legacy.title',
          })}`}
        </Typography>
      </Stack>
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
        justifyContent="flex-end"
      >
        <Button
          loading={isSwitchingVersion}
          variant={isUsingNewStyle ? 'text' : 'contained'}
          size="small"
          color={isUsingNewStyle ? 'secondary' : 'primary'}
          endIcon={<SwapHorizOutlinedIcon />}
          onClick={() => {
            const newValue = isUsingNewStyle ? 'old' : 'new'
            setIsSwitchingVersion(true)

            updateDeliverySettings(
              [
                {
                  key: 'designStyleCurrent',
                  value: newValue,
                },
              ],
              {
                onSettled: () => {
                  setIsSwitchingVersion(false)
                },
              },
            )

            setLocalDesignStyleCurrent(newValue)
          }}
        >
          {ctIntl.formatMessage({
            id: isUsingNewStyle
              ? 'delivery.switchBanner.revamp.button.label'
              : 'delivery.switchBanner.legacy.button.label',
          })}
        </Button>

        <IconButton
          size="small"
          color="secondary"
          disableRipple
          onClick={handleClose}
        >
          <CloseFilledIcon />
        </IconButton>
      </Stack>
    </Stack>
  )
}

export default DeliveryVersionSwitchFrame
