import { useCallback, useMemo, useRef } from 'react'
import { difference } from 'lodash'
import {
  Box,
  Button,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
} from '@karoo-ui/core'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import CloseIcon from '@mui/icons-material/Close'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import FileDownloadIcon from '@mui/icons-material/FileDownload'
import { FormattedMessage } from 'react-intl'
import { useDispatch } from 'react-redux'

import useCancelJobs from 'src/modules/deliveryRevamp/api/jobs/useCancelJobs'
import useDeleteSliceJobs from 'src/modules/deliveryRevamp/api/routes/useDeleteSliceJobs'
import { REPORT_TYPES } from 'src/modules/deliveryRevamp/constants'
import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { useRefreshDeliveryData } from 'src/modules/deliveryRevamp/hooks/useRefresh'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import useClickOrSelectJobs from '../../../hooks/useClickOrSelectJobs'
import {
  clickedJobDetailsCloseButton,
  getCompletedJobIds,
  getStartedJobIds,
  initializeDownloadJobReport,
} from '../../../slice'
import Popover, { type PopoverHandles } from '../../Popover'

const MoreActions = () => {
  const dispatch = useDispatch()
  const ref = useRef<PopoverHandles>(null)
  const allStartedJobIdsIncludingNotSelected = useTypedSelector(getStartedJobIds)
  const allCompletedJobIdsIncludingNotSelected = useTypedSelector(getCompletedJobIds)
  const { allSelectedJobIds, isSelectingCanceledJob, resetSelectedJobIds } =
    useClickOrSelectJobs()
  const { mutate: deleteJobs } = useDeleteSliceJobs()
  const { mutate: cancelJobs } = useCancelJobs()

  const { refresh } = useRefreshDeliveryData()

  const notStartedJobIds = useMemo(
    () =>
      difference(
        allSelectedJobIds,
        allStartedJobIdsIncludingNotSelected,
        allCompletedJobIdsIncludingNotSelected,
      ),
    [
      allCompletedJobIdsIncludingNotSelected,
      allSelectedJobIds,
      allStartedJobIdsIncludingNotSelected,
    ],
  )

  const resetAllSelectedJobIds = useCallback(() => {
    resetSelectedJobIds('LeftPanel')
    resetSelectedJobIds('RoutesPanel')
  }, [resetSelectedJobIds])

  const handleDownload = useCallback(
    (downloadType: REPORT_TYPES) => {
      dispatch(
        initializeDownloadJobReport({
          ids: allSelectedJobIds,
          downloadType,
        }),
      )
      ref.current?.closePopover()
    },
    [allSelectedJobIds, dispatch],
  )

  const handleCancelJobs = useCallback(() => {
    Dialog.alert({
      title: ctIntl.formatMessage(
        {
          id: 'Cancel {count} jobs?',
        },
        { values: { count: notStartedJobIds.length } },
      ),
      content: ctIntl.formatMessage({
        id: 'Please note that jobs that have been started, arrived or completed will not be affected.',
      }),
      onResult() {
        cancelJobs(
          {
            jobIds: notStartedJobIds,
          },
          {
            onSuccess: () => {
              dispatch(clickedJobDetailsCloseButton())
              resetAllSelectedJobIds()
            },
          },
        )
        ref.current?.closePopover()
      },
    })
  }, [notStartedJobIds, dispatch, cancelJobs, resetAllSelectedJobIds])

  const handleDeleteJobs = useCallback(() => {
    Dialog.alert({
      title: ctIntl.formatMessage(
        {
          id: 'Delete {count} jobs?',
        },
        { values: { count: notStartedJobIds.length } },
      ),
      content: (
        <>
          <Box>
            {ctIntl.formatMessage({
              id: 'delivery.rightPanel.multiJobDelete.successful.subtitle',
            })}
          </Box>
          <Box>
            {ctIntl.formatMessage({
              id: 'Please note that jobs that have been started, arrived or completed will not be affected.',
            })}
          </Box>
        </>
      ),
      confirmButtonLabel: ctIntl.formatMessage({ id: 'Delete' }),
      onResult() {
        deleteJobs(
          {
            jobIds: notStartedJobIds,
          },
          {
            onSuccess: () => {
              dispatch(clickedJobDetailsCloseButton())
              resetAllSelectedJobIds()
              window.setTimeout(() => refresh())
            },
          },
        )
        ref.current?.closePopover()
      },
    })
  }, [notStartedJobIds, deleteJobs, dispatch, refresh, resetAllSelectedJobIds])

  return (
    <Popover
      ref={ref}
      popoverProps={{
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'left',
        },
        transformOrigin: {
          vertical: 'bottom',
          horizontal: 'left',
        },
      }}
      content={
        <MenuList>
          <MenuItem
            onClick={() => handleDownload(REPORT_TYPES.JOB_REPORT)}
            disabled={allSelectedJobIds.length === 0}
          >
            <ListItemIcon>
              <FileDownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              <FormattedMessage
                id={'delivery.moreActions.downloadJobCard.pdf'}
                values={{
                  b: (chunks) => <span style={{ color: '#00000099' }}>{chunks}</span>,
                }}
              />
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={() => handleDownload(REPORT_TYPES.SHIPPING_LABEL)}
            disabled={allSelectedJobIds.length === 0}
          >
            <ListItemIcon>
              <FileDownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              <FormattedMessage
                id={'delivery.moreActions.downloadShippingLabel.pdf'}
                values={{
                  b: (chunks) => <span style={{ color: '#00000099' }}>{chunks}</span>,
                }}
              />
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleCancelJobs}
            disabled={notStartedJobIds.length === 0 || isSelectingCanceledJob}
          >
            <ListItemIcon>
              <CloseIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>
              {ctIntl.formatMessage({ id: 'delivery.job.cancel' })}
            </ListItemText>
          </MenuItem>
          <MenuItem
            onClick={handleDeleteJobs}
            disabled={notStartedJobIds.length === 0}
          >
            <ListItemIcon>
              <DeleteOutlinedIcon
                fontSize="small"
                color="error"
              />
            </ListItemIcon>
            <ListItemText>{ctIntl.formatMessage({ id: 'Delete' })}</ListItemText>
          </MenuItem>
        </MenuList>
      }
    >
      <Button
        variant="outlined"
        color="secondary"
        endIcon={<ArrowDropDownIcon />}
        disabled={allSelectedJobIds.length === 0}
      >
        {ctIntl.formatMessage({ id: 'delivery.moreActions' })}
      </Button>
    </Popover>
  )
}

export default MoreActions
