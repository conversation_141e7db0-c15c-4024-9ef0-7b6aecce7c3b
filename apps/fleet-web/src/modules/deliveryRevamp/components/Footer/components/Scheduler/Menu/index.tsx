import { useMemo, useState, type ReactElement } from 'react'
import {
  DateTimePicker,
  Stack,
  Typography,
  type DateTimePickerProps,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import Menu from '../../../../MapPanel/components/JobDetails/Form/components/Menu'
import { SEND_JOB_TO_DRIVER_OPTIONS } from '../../../constants'

type SendJobToDriverType = keyof typeof SEND_JOB_TO_DRIVER_OPTIONS

type Props = {
  values: {
    scheduledDate: DateTime
    sendToDriverAt: DateTime | null
    allowedToStartAt: DateTime | null
  }
  dateField: 'sendToDriverAt' | 'allowedToStartAt'
  handleDateTimeChange: (value: {
    option: SendJobToDriverType
    dateTime?: DateTime
  }) => void
  children: ReactElement
  isDisabled?: boolean
}

const ScheduleOptionsMenu = ({
  values: { scheduledDate, sendToDriverAt, allowedToStartAt },
  dateField,
  handleDateTimeChange,
  children,
  isDisabled = false,
}: Props) => {
  const [openDateTimePicker, setOpenDateTimePicker] = useState(false)

  const isScheduledDateTimeInTheFuture = useMemo(
    () =>
      scheduledDate ? scheduledDate.startOf('day') > DeliveryDateTime.now() : false,
    [scheduledDate],
  )

  const { menuOptions, dateLimits } = useMemo(() => {
    const disabled = isDisabled
      ? { reason: ctIntl.formatMessage({ id: 'No jobs to update' }), isDisabled: true }
      : undefined
    const menuOptions = (() => {
      const startOfDeliveryDayOption = {
        label: 'delivery.jobDetails.startOfDeliveryDay',
        subLabel: scheduledDate ? scheduledDate.toFormat('D 00:00') : '',
        value: SEND_JOB_TO_DRIVER_OPTIONS.START_OF_DELIVERY_DAY,
        disabled,
      }

      const immediatelyOption = {
        label: 'deliveryRevamp.jobDetails.sendToDriverAt.immediately',
        value: SEND_JOB_TO_DRIVER_OPTIONS.IMMEDIATELY,
        disabled,
      }

      const specificDateAndTimeOption = {
        label: 'delivery.jobDetails.specificDateAndTime',
        value: SEND_JOB_TO_DRIVER_OPTIONS.SPECIFIC_DATE_AND_TIME,
        disabled,
      }

      if (dateField === 'sendToDriverAt') {
        return isScheduledDateTimeInTheFuture
          ? [immediatelyOption, startOfDeliveryDayOption, specificDateAndTimeOption]
          : [immediatelyOption, specificDateAndTimeOption]
      }

      return [
        startOfDeliveryDayOption,
        {
          ...specificDateAndTimeOption,
          label: 'delivery.jobDetails.specificTimeOfTheDeliveryDay',
        },
      ]
    })()

    const dateLimits = (() => {
      if (!scheduledDate) {
        return { minDateTime: undefined, maxDateTime: undefined }
      }

      const currentTime = DeliveryDateTime.now()

      if (dateField === 'allowedToStartAt') {
        const referenceDateTime = sendToDriverAt ?? scheduledDate

        if (isScheduledDateTimeInTheFuture) {
          return {
            minDateTime: referenceDateTime,
            maxDateTime: scheduledDate.endOf('day'),
          }
        } else {
          return {
            minDateTime: referenceDateTime.set({
              hour: currentTime.hour,
              minute: currentTime.minute,
              second: currentTime.second,
              millisecond: 0,
            }),
            maxDateTime: scheduledDate.endOf('day'),
          }
        }
      }

      // sendToDriverAt - Future
      if (isScheduledDateTimeInTheFuture) {
        return {
          minDateTime: DeliveryDateTime.now(),
          maxDateTime: scheduledDate.endOf('day'),
        }
      }

      // sendToDriverAt Today
      return {
        minDateTime: scheduledDate.set({
          hour: currentTime.hour,
          minute: currentTime.minute,
          second: currentTime.second,
          millisecond: 0,
        }),
        maxDateTime: scheduledDate.endOf('day'),
      }
    })()

    return { menuOptions, dateLimits }
  }, [
    dateField,
    isDisabled,
    isScheduledDateTimeInTheFuture,
    scheduledDate,
    sendToDriverAt,
  ])

  return (
    <Stack direction="row">
      <Menu
        menuItems={menuOptions}
        onItemClick={(clickedMenu) => {
          match(clickedMenu as SendJobToDriverType)
            .with(SEND_JOB_TO_DRIVER_OPTIONS.IMMEDIATELY, () => {
              handleDateTimeChange({
                option: SEND_JOB_TO_DRIVER_OPTIONS.IMMEDIATELY,
              })
            })
            .with(SEND_JOB_TO_DRIVER_OPTIONS.START_OF_DELIVERY_DAY, () => {
              handleDateTimeChange({
                option: SEND_JOB_TO_DRIVER_OPTIONS.START_OF_DELIVERY_DAY,
                dateTime: scheduledDate?.startOf('day'),
              })
            })
            .with(SEND_JOB_TO_DRIVER_OPTIONS.SPECIFIC_DATE_AND_TIME, () => {
              setOpenDateTimePicker(true)
            })
            .otherwise(() => null)
        }}
        header={
          <Typography variant="caption">
            {ctIntl.formatMessage({
              id:
                dateField === 'sendToDriverAt'
                  ? 'deliveryRevamp.jobDetails.sendToDriverAt'
                  : 'deliveryRevamp.jobDetails.allowedToStartAt',
            })}
          </Typography>
        }
      >
        {children}
      </Menu>
      <ScheduleOptionsDateTimePicker
        open={openDateTimePicker}
        value={dateField === 'sendToDriverAt' ? sendToDriverAt : allowedToStartAt}
        minDateTime={dateLimits.minDateTime}
        maxDateTime={dateLimits.maxDateTime}
        orientation="landscape"
        onAccept={(date) => {
          if (date) {
            handleDateTimeChange({
              option: SEND_JOB_TO_DRIVER_OPTIONS.SPECIFIC_DATE_AND_TIME,
              dateTime: date,
            })
          }
          setOpenDateTimePicker(false)
        }}
        onClose={() => setOpenDateTimePicker(false)}
      />
    </Stack>
  )
}

export default ScheduleOptionsMenu

export const ScheduleOptionsDateTimePicker = (props: DateTimePickerProps<DateTime>) => (
  <DateTimePicker
    orientation="landscape"
    {...props}
    sx={{
      // hide textfield
      '&.MuiTextField-root': {
        visibility: 'hidden',
        height: 0,
        width: 0,
      },

      '& *': {
        width: '0 !important',
        height: '0 !important',
        padding: '0 !important',
        margin: '0 !important',
        border: 'none !important',
      },
      ...props.sx,
    }}
  />
)
