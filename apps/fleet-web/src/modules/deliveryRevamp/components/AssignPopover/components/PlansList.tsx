import { useCallback, useMemo, useState } from 'react'
import {
  Box,
  Button,
  InputAdornment,
  ListItem,
  ListSubheader,
  <PERSON>ack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import SearchIcon from '@mui/icons-material/Search'
import type { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'
import type { RequireAllOrNone } from 'type-fest'
import { VList } from 'virtua'

import type { RoutesList } from 'src/modules/deliveryRevamp/api/routes/useRoutesList'
import { ctIntl } from 'src/util-components/ctIntl'

import type { FetchDeliveryPlanDetails } from '../../../api/plans/useDeliveryPlanDetailsQuery'
import type { FetchDeliveryPlanLists } from '../../../api/plans/useDeliveryPlanListsQuery'
import RecurringCard from '../../Header/components/RecurringPopper/components/RecurringCard'

type RoutePlanId = {
  routeId: RoutesList.Route['routeId']
  planId: FetchDeliveryPlanDetails.Return['planId']
}

type MultipleSelectionProps = RequireAllOrNone<
  {
    enableMultipleSelection?: boolean
    onMultipleSelectionChange?: (selectedRoutePlanIds: Array<RoutePlanId>) => void
    defaultSelectedPlanIds?: Array<RoutePlanId>
  },
  'enableMultipleSelection' | 'onMultipleSelectionChange' | 'defaultSelectedPlanIds'
>

type SingleSelectionProps = RequireAllOrNone<
  {
    defaultSelectedPlanId?: FetchDeliveryPlanDetails.Return['planId'] | null
    onSelectionChange?: (
      routeId: RoutesList.Route['routeId'],
      planId: FetchDeliveryPlanDetails.Return['planId'],
    ) => void
  },
  'defaultSelectedPlanId' | 'onSelectionChange'
>

type Props = {
  plans: FetchDeliveryPlanLists.Return
  selectedDate: DateTime | null
} & SingleSelectionProps &
  MultipleSelectionProps

export default function PlansList({
  plans,
  defaultSelectedPlanId,
  defaultSelectedPlanIds = [],
  onSelectionChange,
  onMultipleSelectionChange,
  enableMultipleSelection,
  selectedDate,
}: Props) {
  const [search, setSearch] = useState('')

  const [selectedRoutePlanIds, setSelectedRoutePlanIds] =
    useState<Array<RoutePlanId>>(defaultSelectedPlanIds)

  const handleSelectRoute = useCallback(
    (
      routeId: RoutesList.Route['routeId'],
      planId: FetchDeliveryPlanDetails.Return['planId'],
    ) => {
      let result = []
      if (
        selectedRoutePlanIds.some(
          (routePlanId) =>
            routePlanId.routeId === routeId && routePlanId.planId === planId,
        )
      ) {
        result = selectedRoutePlanIds.filter((id) => id.planId !== planId)
      } else {
        result = [...selectedRoutePlanIds, { routeId, planId: planId }]
      }
      setSelectedRoutePlanIds(result)
      onMultipleSelectionChange?.(result)
    },
    [onMultipleSelectionChange, selectedRoutePlanIds],
  )

  const handleClickRoute = useCallback(
    (
      routeId: RoutesList.Route['routeId'],
      planId: FetchDeliveryPlanDetails.Return['planId'],
    ) => {
      if (enableMultipleSelection) {
        handleSelectRoute(routeId, planId)
      }
      if (planId !== defaultSelectedPlanId) {
        onSelectionChange?.(routeId, planId)
      }
    },
    [
      defaultSelectedPlanId,
      enableMultipleSelection,
      handleSelectRoute,
      onSelectionChange,
    ],
  )

  const [unassignedRoutes, selectedDateRecurringRoutes, otherRecurringRoutes] =
    useMemo(() => {
      const unassignedRoutes: Array<FetchDeliveryPlanDetails.Return> = []
      const selectedDateRecurringRoutes: Array<FetchDeliveryPlanDetails.Return> = []
      const otherRecurringRoutes: Array<FetchDeliveryPlanDetails.Return> = []
      for (const planId of plans.allIds) {
        const plan = plans.byId[planId]
        if (plan.name.toLowerCase().includes(search.toLowerCase())) {
          if (plan.isRecurring) {
            if (plan.isPlanOnDate(selectedDate)) {
              selectedDateRecurringRoutes.push(plan)
            } else {
              otherRecurringRoutes.push(plan)
            }
          } else {
            unassignedRoutes.push(plan)
          }
        }
      }

      return [unassignedRoutes, selectedDateRecurringRoutes, otherRecurringRoutes]
    }, [plans, search, selectedDate])

  const renderRouteItem = useCallback(
    (plan: FetchDeliveryPlanDetails.Return) => (
      <RecurringCard
        key={plan.planId}
        plan={plan}
        selected={
          defaultSelectedPlanId === plan.planId ||
          selectedRoutePlanIds.some(
            ({ routeId, planId }) => routeId === plan.routeId && planId === plan.planId,
          )
        }
        onClick={() => {
          if (plan.planId !== defaultSelectedPlanId) {
            handleClickRoute(plan.routeId, plan.planId)
          }
        }}
        hideMenu
        hideNextRun
        showCheckbox={enableMultipleSelection}
      />
    ),
    [
      defaultSelectedPlanId,
      selectedRoutePlanIds,
      enableMultipleSelection,
      handleClickRoute,
    ],
  )

  const handleResetSelectedDrivers = useCallback(() => {
    setSelectedRoutePlanIds([])
    onMultipleSelectionChange?.([])
  }, [onMultipleSelectionChange])

  return (
    <Stack sx={{ overflow: 'hidden', flex: 1 }}>
      <Stack sx={{ p: 2, pb: 0 }}>
        <TextField
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          placeholder={ctIntl.formatMessage({ id: 'Search' })}
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            },
          }}
        />
      </Stack>

      <VList>
        <ListSubheader sx={{ lineHeight: 1, py: 1 }}>
          <Typography
            variant="caption"
            sx={{ color: 'text.secondary' }}
          >
            {ctIntl.formatMessage({ id: 'Unassigned routes' })}
          </Typography>
        </ListSubheader>
        {match(unassignedRoutes)
          .with(
            P.when((unassignedRoutes) => unassignedRoutes.length === 0),
            () => (
              <ListItem>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary' }}
                >
                  {ctIntl.formatMessage({ id: 'No unassigned route is created yet.' })}
                </Typography>
              </ListItem>
            ),
          )
          .otherwise(() => unassignedRoutes.map((route) => renderRouteItem(route)))}

        <ListSubheader sx={{ lineHeight: 1, pt: 2, pb: 1 }}>
          <Typography
            variant="caption"
            sx={{ color: 'text.secondary' }}
          >
            {selectedDate
              ? selectedDate.toFormat('dd LLL')
              : ctIntl.formatMessage({ id: 'Unscheduled' })}{' '}
            <Box
              component="span"
              sx={{ textTransform: 'lowercase' }}
            >
              {ctIntl.formatMessage({ id: 'delivery.recurringRoutes' })}
            </Box>
          </Typography>
        </ListSubheader>
        {match(selectedDateRecurringRoutes)
          .with(
            P.when(
              (selectedDateRecurringRoutes) => selectedDateRecurringRoutes.length === 0,
            ),
            () => (
              <ListItem>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary' }}
                >
                  {ctIntl.formatMessage({ id: 'No recurring route is created yet.' })}
                </Typography>
              </ListItem>
            ),
          )
          .otherwise(() =>
            selectedDateRecurringRoutes.map((route) => renderRouteItem(route)),
          )}

        <ListSubheader sx={{ lineHeight: 1, pt: 2, pb: 1 }}>
          <Typography
            variant="caption"
            sx={{ color: 'text.secondary' }}
          >
            {ctIntl.formatMessage({ id: 'Other recurring routes' })}
          </Typography>
        </ListSubheader>
        {match(otherRecurringRoutes)
          .with(
            P.when((otherRecurringRoutes) => otherRecurringRoutes.length === 0),
            () => (
              <ListItem>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary' }}
                >
                  {ctIntl.formatMessage({ id: 'No recurring route is created yet.' })}
                </Typography>
              </ListItem>
            ),
          )
          .otherwise(() => otherRecurringRoutes.map((route) => renderRouteItem(route)))}
      </VList>
      {enableMultipleSelection && (
        <Button
          size="small"
          variant="text"
          sx={{ alignSelf: 'start', mx: 2, my: 1 }}
          disabled={selectedRoutePlanIds.length === 0}
          onClick={handleResetSelectedDrivers}
        >
          {ctIntl.formatMessage({ id: 'Reset All' })}
          {selectedRoutePlanIds.length > 0 && ` (${selectedRoutePlanIds.length})`}
        </Button>
      )}
    </Stack>
  )
}
