import { useC<PERSON>back, useMemo, useRef, useState } from 'react'
import {
  Box,
  Button,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
  MenuList,
  Stack,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import UploadOutlinedIcon from '@mui/icons-material/UploadOutlined'
import { useDispatch } from 'react-redux'
import { useHistory, useLocation } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import type { DriversList } from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import useAssignJob from 'src/modules/deliveryRevamp/api/jobs/useAssignJob'
import { useCreatePlanMutation } from 'src/modules/deliveryRevamp/api/plans/useCreatePlanMutation'
import type { FetchDeliveryPlanDetails } from 'src/modules/deliveryRevamp/api/plans/useDeliveryPlanDetailsQuery'
import DriverChip from 'src/modules/deliveryRevamp/components/DriverChip'
import { IMPORT_TYPE } from 'src/modules/deliveryRevamp/constants'
import { SCHEDULE_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import {
  DROPPABLE_TYPE,
  useDndMonitor,
  useDroppable,
} from 'src/modules/deliveryRevamp/contexts/DeliveryDndJobsContext'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import useActiveImport from 'src/modules/deliveryRevamp/hooks/useActiveImport'
import { useRefreshDeliveryData } from 'src/modules/deliveryRevamp/hooks/useRefresh'
import { clickedButtonToCreateANewJob } from 'src/modules/deliveryRevamp/slice'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import AssignCard, { ASSIGN_CARD_TABS } from '../../AssignCard'
import Popover, { type PopoverHandles } from '../../Popover'
import { getDeliveryRecurringDialogMainPath } from '../../RecurringDialog/helpers'

export type FakeRouteType = {
  droppableId: string
  driver?: DriversList.Driver
}

type Props = {
  driver?: DriversList.Driver
  droppableId: string
  otherFakeRoutes: Array<FakeRouteType>
  onDelete: (droppapleId: string) => void
  driverIdsInExistingRoutes: Array<DriversList.Driver['deliveryDriverId']>
  onCreateSuccess: (
    driverIdOrPlanId:
      | DriversList.Driver['deliveryDriverId']
      | FetchDeliveryPlanDetails.Return['planId'],
  ) => void
}

export default function FakeRoute({
  driver,
  droppableId,
  otherFakeRoutes,
  onDelete,
  driverIdsInExistingRoutes,
  onCreateSuccess,
}: Props) {
  const dispatch = useDispatch()
  const location = useLocation()
  const history = useHistory()
  const [expanded, setExpanded] = useState(true)
  const driverChipPopoverRef = useRef<PopoverHandles>(null)
  const menuPopoverRef = useRef<PopoverHandles>(null)
  const { refresh } = useRefreshDeliveryData()
  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()
  const scheduledTime = useMemo(
    () =>
      selectedDateRange
        ? selectedDateRange.start
            .startOf('second')
            .toISO({ suppressMilliseconds: true })
        : null,
    [selectedDateRange],
  )
  const defaultRouteName = ctIntl.formatMessage({ id: 'delivery.unassignedRoute' })
  const { mutateAsync: createPlan, isPending: isCreatingPlan } = useCreatePlanMutation({
    withoutMessage: true,
    refreshOnSuccess: false,
  })
  const { mutate: assignJob } = useAssignJob()
  const { handleSetActiveImport } = useActiveImport()

  const hideDeliveryDriverIds = useMemo(
    () => [
      ...otherFakeRoutes.flatMap((fake) =>
        fake.driver ? [fake.driver.deliveryDriverId] : [],
      ),
      ...driverIdsInExistingRoutes,
    ],
    [driverIdsInExistingRoutes, otherFakeRoutes],
  )

  const { setNodeRef, isOver } = useDroppable({
    id: droppableId,
    data: { type: DROPPABLE_TYPE.FAKE_ROUTE },
  })

  const handleCreateOtherRoutes = useCallback(
    () =>
      Promise.allSettled(
        otherFakeRoutes.map((other) =>
          createPlan({
            name: defaultRouteName,
            ...(scheduledTime && { scheduledTime }),
            ...(other.driver && { targetDriverId: other.driver.deliveryDriverId }),
          }),
        ),
      ),
    [createPlan, defaultRouteName, otherFakeRoutes, scheduledTime],
  )

  const handleCreateRealRoute = useCallback(
    (...params: Parameters<typeof createPlan>) =>
      Promise.allSettled([handleCreateOtherRoutes(), createPlan(...params)]),
    [createPlan, handleCreateOtherRoutes],
  )

  useDndMonitor({
    onDragEnd(event) {
      const activeData = event.active?.data.current
      const overData = event.over?.data.current
      if (
        !activeData ||
        overData?.type !== DROPPABLE_TYPE.FAKE_ROUTE ||
        event.over?.id !== droppableId
      )
        return

      if (driver) {
        Promise.allSettled([
          handleCreateOtherRoutes(),
          assignJob({
            routeId: `driver_${driver.deliveryDriverId}`,
            jobIds: activeData.jobIdsWillBeAssigned,
            routeTs: selectedDateRange?.start || null,
          }),
        ]).then(refresh)
      } else {
        handleCreateRealRoute(
          {
            name: defaultRouteName,
            contents: {
              orderedJobIds: activeData.jobIdsWillBeAssigned,
            },
            ...(scheduledTime && { scheduledTime }),
          },
          {
            async onSuccess(res) {
              refresh()
              onCreateSuccess(res.planId)
            },
          },
        )
      }
    },
  })

  const handleAddJob = useCallback(async () => {
    if (driver) {
      dispatch(
        clickedButtonToCreateANewJob({
          type: 'driver',
          id: driver.deliveryDriverId,
        }),
      )
      return
    }

    handleCreateRealRoute(
      {
        name: defaultRouteName,
        ...(scheduledTime && { scheduledTime }),
      },
      {
        onSuccess(res) {
          dispatch(
            clickedButtonToCreateANewJob({
              type: 'route',
              id: res.planId,
            }),
          )
        },
      },
    )
  }, [defaultRouteName, dispatch, driver, handleCreateRealRoute, scheduledTime])

  const handleDeleteRoute = useCallback(() => {
    onDelete(droppableId)
    handleCreateOtherRoutes().then(() => refresh())
    menuPopoverRef.current?.closePopover()
  }, [droppableId, handleCreateOtherRoutes, onDelete, refresh])

  const handleChangeAssignee = useCallback(
    (deliveryDriverId?: DriversList.Driver['deliveryDriverId']) => {
      if (deliveryDriverId && hideDeliveryDriverIds.includes(deliveryDriverId)) {
        handleDeleteRoute()
        onCreateSuccess(deliveryDriverId)
        return
      }
      handleCreateRealRoute(
        {
          name: defaultRouteName,
          targetDriverId: deliveryDriverId,
          ...(scheduledTime && { scheduledTime }),
        },
        {
          async onSuccess() {
            refresh()
            if (deliveryDriverId) {
              onCreateSuccess(deliveryDriverId)
            }
          },
        },
      )
      driverChipPopoverRef.current?.closePopover()
    },
    [
      scheduledTime,
      defaultRouteName,
      hideDeliveryDriverIds,
      refresh,
      onCreateSuccess,
      handleDeleteRoute,
      handleCreateRealRoute,
    ],
  )

  const handleImportJobsToRoute = useCallback(async () => {
    const schedule = match({ scheduledTime })
      .with(
        {
          scheduledTime: P.when(
            (scheduledTime) =>
              !!scheduledTime &&
              DeliveryDateTime.fromISO(scheduledTime).hasSame(
                DeliveryDateTime.now(),
                'day',
              ),
          ),
        },
        () => ({
          scheduleTypeId: SCHEDULE_TYPE_ID.ASAP,
          scheduledDeliveryTs: null,
        }),
      )
      .with({ scheduledTime: P.nonNullable }, ({ scheduledTime }) => ({
        scheduleTypeId: SCHEDULE_TYPE_ID.SCHEDULE,
        scheduledDeliveryTs: scheduledTime,
      }))
      .otherwise(() => ({
        scheduleTypeId: SCHEDULE_TYPE_ID.UNSCHEDULE,
        scheduledDeliveryTs: null,
      }))

    if (driver) {
      handleSetActiveImport({
        importType: IMPORT_TYPE.DRIVER,
        deliveryDriverId: driver.deliveryDriverId,
        ...schedule,
      })
    } else {
      createPlan({
        name: defaultRouteName,
        ...(scheduledTime && { scheduledTime }),
      }).then(({ planId }) => {
        handleSetActiveImport({
          importType: IMPORT_TYPE.PLAN,
          planId,
          ...schedule,
        })
      })
      handleCreateOtherRoutes().then(() => refresh())
    }
    menuPopoverRef.current?.closePopover()
  }, [
    createPlan,
    defaultRouteName,
    driver,
    handleCreateOtherRoutes,
    handleSetActiveImport,
    refresh,
    scheduledTime,
  ])

  const handleMakeRecurringRoute = useCallback(() => {
    history.push(
      getDeliveryRecurringDialogMainPath(location, {
        formParams: {
          existingFormField: {
            targetDriverId: driver?.deliveryDriverId,
          },
        },
      }),
    )
    menuPopoverRef.current?.closePopover()
  }, [driver?.deliveryDriverId, history, location])

  return (
    <Stack gap={1}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Stack
          direction="row"
          alignItems="center"
        >
          <Popover
            ref={driverChipPopoverRef}
            content={
              <AssignCard
                tabs={[ASSIGN_CARD_TABS.DRIVERS]}
                deliveryDriverId={driver?.deliveryDriverId}
                onSelectDriver={handleChangeAssignee}
                onUnassign={handleChangeAssignee}
              />
            }
            popoverProps={{
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
              transformOrigin: {
                vertical: 'top',
                horizontal: 'left',
              },
            }}
          >
            <Box sx={{ cursor: 'pointer' }}>
              <DriverChip
                canExpand={true}
                driver={driver}
              />
            </Box>
          </Popover>
        </Stack>

        <Stack
          direction="row"
          alignItems="center"
        >
          <Popover
            ref={menuPopoverRef}
            content={
              <MenuList>
                <MenuItem onClick={handleDeleteRoute}>
                  <ListItemIcon>
                    <DeleteOutlinedIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>
                    {ctIntl.formatMessage({ id: 'delivery.route.action.delete' })}
                  </ListItemText>
                </MenuItem>
                <MenuItem onClick={handleImportJobsToRoute}>
                  <ListItemIcon>
                    <UploadOutlinedIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>
                    {ctIntl.formatMessage({ id: 'delivery.route.action.importJobs' })}
                  </ListItemText>
                </MenuItem>
                <MenuItem onClick={handleMakeRecurringRoute}>
                  <ListItemIcon>
                    <LoopOutlinedIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>
                    {ctIntl.formatMessage({
                      id: 'delivery.route.action.makeRecurring',
                    })}
                  </ListItemText>
                </MenuItem>
              </MenuList>
            }
          >
            <Tooltip title={ctIntl.formatMessage({ id: 'delivery.route.action.more' })}>
              <IconButton size="small">
                <MoreHorizIcon fontSize="inherit" />
              </IconButton>
            </Tooltip>
          </Popover>
          <Tooltip
            title={`${ctIntl.formatMessage({
              id: 'Collapse',
            })}/${ctIntl.formatMessage({ id: 'Expand' })}`}
          >
            <IconButton
              size="small"
              onClick={() => setExpanded((prev) => !prev)}
            >
              <ExpandMoreIcon
                fontSize="inherit"
                sx={{
                  transform: expanded ? 'rotate(0deg)' : 'rotate(90deg)',
                  transition: 'transform .2s',
                }}
              />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>

      {expanded && (
        <Stack>
          <Button
            ref={setNodeRef}
            variant="outlined"
            color="secondary"
            size="small"
            startIcon={<AddIcon />}
            sx={{
              width: 'auto',
              height: '86px',
              borderRadius: 1,
              border: '2px dashed',
              borderColor: isOver ? 'primary.main' : 'grey.300',
              color: 'text.secondary',
              ...(isOver && {
                backgroundColor: 'action.hover',
              }),
            }}
            onClick={handleAddJob}
            disabled={isCreatingPlan}
          >
            {ctIntl.formatMessage({ id: 'delivery.routes.addJobStops' })}&nbsp;
            <Typography
              variant="caption"
              sx={{
                color: 'text.secondary',
                textTransform: 'lowercase',
              }}
            >
              {ctIntl.formatMessage({
                id: 'delivery.routes.addJobStopsDragHere',
              })}
            </Typography>
          </Button>
        </Stack>
      )}
    </Stack>
  )
}
