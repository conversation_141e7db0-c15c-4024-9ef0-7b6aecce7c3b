import { useCallback, useEffect, useMemo, useState } from 'react'
import { debounce, includes, keyBy } from 'lodash'
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  DatePicker,
  FormControlLabel,
  InputAdornment,
  ListItemText,
  MenuItem,
  Stack,
  Switch,
  TextField,
  Toolt<PERSON>,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled, type UseControlledFormReturn } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined'
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircleOutlined'
import { produce } from 'immer'
import { Controller, useWatch } from 'react-hook-form'
import { useIntl } from 'react-intl'
import * as R from 'remeda'
import { match, P } from 'ts-pattern'

import type { DriverId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import useDriversList from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import { JOB_FORM_SOURCES } from 'src/modules/deliveryRevamp/constants'
import useGetSubUsers from 'src/modules/deliveryRevamp/hooks/useGetSubUsers'
import useSubUserDataAccessScope from 'src/modules/deliveryRevamp/hooks/useSubUserDataAccessScope'
import type { LabelValue } from 'src/modules/deliveryRevamp/types'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import type { AddressDetail } from 'src/modules/deliveryRevamp/utils/google-maps-api'
import { ctIntl } from 'src/util-components/ctIntl'

import { FORM_STATE, JOB_TYPE_ID, STOP_TYPE_ID } from '../../../constants/job'
import SubUserAvatar from '../../Avatar/SubUser'
import type { JobDetailsFormType } from '../../MapPanel/components/JobDetails/Form/schema'
import { getDriverSubUserId } from '../../MapPanel/components/JobDetails/Form/Sections/JobDetails'
import {
  addJobAndStopsUniqueId,
  addStopsUniqueId,
  constructRecurrenceSummary,
  getDaysOfWeekOptions,
  INTERVAL_CONFIG_INITIAL_VALUES,
  INTERVAL_OPTIONS,
  jobFormDefaultValues,
  MONTH_LIST,
  parseAllStopsFormToApi,
  parseStopsFormToApi,
} from '../helpers'
import useGroupedStops from '../hooks/useGroupedStops'
import { ERROR_TYPE, INTERVAL, type ErrorState, type RecurringFormType } from '../types'
import AddJobMenu from './AddJobMenu'
import AssignDriverPopover from './AssignDriverPopover'
import JobForm from './JobForm'
import MapSection from './MapSection'
import StopList from './StopList'

type Props = {
  formError: ErrorState
  jobForm: UseControlledFormReturn<JobDetailsFormType>
  recurringForm: UseControlledFormReturn<RecurringFormType>
  onSubmit: () => void
}

const RecurringForm = ({ jobForm, recurringForm, formError, onSubmit }: Props) => {
  const { formatList } = useIntl()

  const jobDefaultValues = useMemo<JobDetailsFormType>(() => jobFormDefaultValues, [])

  const [showJobForm, setShowJobForm] = useState(false)

  const jobFormValues = useWatch({
    control: jobForm.control,
  })

  const writeJobFormValues = useEffectEvent(() => {
    const values = jobForm.getValues()
    if (jobForm.formState.isDirty) {
      const jobs = recurringForm.getValues('jobs')
      const jobIndex = jobs.findIndex((job) => job.jobId === values.jobId)
      if (jobIndex !== -1) {
        const stopIds = recurringForm.getValues('stops')
        const currentJobStops = parseStopsFormToApi(jobs[jobIndex])
        const currentJobStopIds = new Set(currentJobStops.map((stop) => stop.stopId))

        let updatedJob

        const updatedJobs = [...jobs]

        const isJobTypeChanged =
          values.stops.jobTypeId !== jobs[jobIndex].stops.jobTypeId

        if (isJobTypeChanged) {
          const filteredStopsIds = stopIds.filter(
            (item) => !currentJobStopIds.has(item),
          )

          updatedJob = {
            ...jobs[jobIndex],
            ...values,
            stops: addStopsUniqueId(values.stops),
          }

          const updatedJobStopsIds = parseStopsFormToApi(updatedJob).map(
            (stop) => stop.stopId,
          )

          jobForm.setValue('stops', updatedJob.stops)
          recurringForm.setValue('stops', [...filteredStopsIds, ...updatedJobStopsIds])
        } else {
          updatedJob = { ...jobs[jobIndex], ...values }
        }

        updatedJobs[jobIndex] = updatedJob
        recurringForm.setValue('jobs', updatedJobs)
      }
    }
  })

  useEffect(() => {
    const debounceWrite = debounce(() => writeJobFormValues(), 750)

    debounceWrite()

    return () => {
      debounceWrite.cancel()
    }
  }, [jobFormValues])

  const { data: driversList } = useDriversList()

  const [
    jobList,
    driverId,
    interval,
    intervalConfig,
    dates,
    isExcludeDays,
    stops,
    planId,
  ] = useWatch({
    control: recurringForm.control,
    name: [
      'jobs',
      'driverId',
      'interval',
      'intervalConfig',
      'dates',
      'isExcludeDays',
      'stops',
      'planId',
    ],
  })

  const { doesSubUserHaveAccessToScope, canThisUserSeeSubuser } =
    useSubUserDataAccessScope()

  const { subUserOptions } = useGetSubUsers()

  const subUsersById = useMemo(
    () =>
      subUserOptions.reduce<Record<string, (typeof subUserOptions)[number]>>(
        (acc, option) => {
          acc[option.value as string] = option
          return acc
        },
        {},
      ),
    [subUserOptions],
  )

  const driverAssignedSubUserId = useMemo(() => {
    if (driversList && driverId) {
      return getDriverSubUserId(driversList, driverId as DriverId)
    }

    return null
  }, [driverId, driversList])

  const handleDeleteJob = () => {
    const jobFormValues = jobForm.getValues()
    const stopIds = new Set(
      parseStopsFormToApi(jobFormValues).map((stop) => stop.stopId),
    )

    const filteredStops = stops.filter((item) => !stopIds.has(item))
    const filteredJobs = jobList.filter((item) => item.jobId !== jobFormValues.jobId)
    recurringForm.setValue('jobs', filteredJobs)
    recurringForm.setValue('stops', filteredStops)
    jobForm.reset(jobDefaultValues)
    setShowJobForm(false)
  }
  const handleUpdateStopAddress = useCallback(
    (jobId: number, stopTypeId: STOP_TYPE_ID, address: AddressDetail) => {
      const jobs = recurringForm.getValues('jobs')
      const jobIndex = jobs.findIndex((job) => job.jobId === jobId)
      if (jobIndex !== -1) {
        const updatedJob = produce(jobs[jobIndex], (draft) => {
          if (draft.stops.jobTypeId === JOB_TYPE_ID.SINGLE) {
            draft.stops.single.address = {
              ...draft.stops.single.address,
              ...address,
            }
          } else if (draft.stops.jobTypeId === JOB_TYPE_ID.PD) {
            const key = stopTypeId === STOP_TYPE_ID.PICKUP ? 'pickup' : 'dropoff'
            draft.stops[key].address = {
              ...draft.stops[key].address,
              ...address,
            }
          }
        })
        const updatedJobs = [...jobs]

        updatedJobs[jobIndex] = updatedJob

        recurringForm.setValue('jobs', updatedJobs)

        const jobFormData = jobForm.getValues()

        if (jobId === jobFormData.jobId) {
          const { stops } = updatedJob
          const jobFormData = jobForm.getValues()
          jobForm.reset({ ...jobFormData, stops })
        }
      }
    },
    [jobForm, recurringForm],
  )

  const orderedStops = useMemo(() => {
    const getAllStops = parseAllStopsFormToApi(jobList)
    const stopsLookup = keyBy(getAllStops, 'stopId')
    return stops.map((id, index) => ({
      ...stopsLookup[id],
      ordering: index + 1,
    }))
  }, [jobList, stops])

  const { groupedStops, groupedCluster } = useGroupedStops(orderedStops)

  const handleAddnewJob = () => {
    setShowJobForm(true)
    const jobs = recurringForm.getValues('jobs')
    const stops = recurringForm.getValues('stops')
    const parsedJobData = addJobAndStopsUniqueId(jobDefaultValues)
    recurringForm.setValue('jobs', [...jobs, parsedJobData])
    recurringForm.setValue('stops', [
      ...stops,
      ...parseStopsFormToApi(parsedJobData).map((stop) => stop.stopId),
    ])
    jobForm.reset(parsedJobData)
  }

  return (
    <Stack
      flex={1}
      height={'100%'}
    >
      {match(formError)
        .with({ type: ERROR_TYPE.JOB }, ({ jobIds }) => (
          <Alert
            severity="error"
            sx={{ mx: 2, mb: 2 }}
          >
            {ctIntl.formatMessage(
              {
                id: '{count} job(s) could not be saved due to errors. Please edit the jobs with errors and retry.',
              },
              {
                values: {
                  count: jobIds.length,
                },
              },
            )}
          </Alert>
        ))
        .with({ type: ERROR_TYPE.NETWORK }, () => (
          <Alert
            severity="error"
            sx={{ mx: 2, mb: 2 }}
          >
            <Box
              gap={0.5}
              display="flex"
              flexWrap="wrap"
            >
              <Typography>
                {ctIntl.formatMessage({
                  id: 'The route cannot be saved due to an internal error. Please try saving the form again or',
                })}
              </Typography>
              <Typography
                onClick={onSubmit}
                sx={{ textDecoration: 'underline', cursor: 'pointer' }}
              >
                {ctIntl.formatMessage({
                  id: 'click here',
                })}
              </Typography>
              <Typography>
                {ctIntl.formatMessage({
                  id: 'to retry',
                })}
              </Typography>
            </Box>
          </Alert>
        ))
        .otherwise(() => null)}
      <Stack
        flex={1}
        spacing={2}
        minHeight={0}
      >
        <Stack
          px={2}
          spacing={2}
        >
          <Typography variant="subtitle2">
            {ctIntl.formatMessage({ id: 'delivery.recurrentRoute.recurrenceSettings' })}
          </Typography>
          <Stack
            direction="row"
            gap={2}
          >
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <PersonOutlineOutlinedIcon sx={{ fontSize: 20 }} />
              <Typography variant="body2">
                {ctIntl.formatMessage({ id: 'Assign Driver' })}
              </Typography>
              <AssignDriverPopover
                deliveryDriverId={driverId}
                onSelectedDriver={(driverId) => {
                  recurringForm.setValue('driverId', driverId)
                  const subUserId =
                    driversList && driverId
                      ? getDriverSubUserId(driversList, driverId as DriverId)
                      : null

                  recurringForm.setValue('subuserId', subUserId ?? null)
                }}
              >
                <TextFieldControlled
                  select
                  required
                  readOnly
                  ControllerProps={{
                    control: recurringForm.control,
                    name: 'driverId',
                  }}
                  sx={{ width: 150 }}
                >
                  {driversList &&
                    driversList.allIds.map((id) => (
                      <MenuItem
                        key={driversList.byId[id].deliveryDriverId}
                        value={driversList.byId[id].deliveryDriverId}
                      >
                        {driversList.byId[id].fullName}
                      </MenuItem>
                    ))}
                </TextFieldControlled>
              </AssignDriverPopover>
            </Stack>
            {doesSubUserHaveAccessToScope() && canThisUserSeeSubuser() && (
              <Stack
                direction="row"
                alignItems="center"
                gap={1}
              >
                <SupervisedUserCircleIcon sx={{ fontSize: 20 }} />
                <Stack
                  gap={0.5}
                  sx={{
                    whiteSpace: 'nowrap',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}
                >
                  <Typography variant="body2">
                    {ctIntl.formatMessage({ id: 'Assign Sub-user' })}
                  </Typography>
                  <Tooltip
                    title={ctIntl.formatMessage({
                      id: 'delivery.recurrentRoute.assignSubuser.tooltip',
                    })}
                  >
                    <InfoOutlinedIcon sx={{ fontSize: 16 }} />
                  </Tooltip>
                </Stack>
                <Controller
                  key={driverAssignedSubUserId}
                  control={recurringForm.control}
                  name="subuserId"
                  render={({ field }) => (
                    <Autocomplete
                      fullWidth
                      disabled={
                        Boolean(driverAssignedSubUserId) || jobList.length === 0
                      }
                      value={
                        field.value
                          ? (subUsersById[field.value] as LabelValue) ?? null
                          : null
                      }
                      onChange={(_event, option) => {
                        field.onChange(option?.value ?? null)
                      }}
                      {...getAutocompleteVirtualizedProps({
                        options: subUserOptions as Array<LabelValue>,
                        renderRowSingleItemContent: ({ label }) => (
                          <Stack
                            direction="row"
                            alignItems="center"
                            gap={1}
                          >
                            <SubUserAvatar
                              variant="square"
                              username={label}
                            />
                            <ListItemText>{label}</ListItemText>
                          </Stack>
                        ),
                      })}
                      getOptionLabel={(option) => option.label ?? ''}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          sx={{ width: 150 }}
                          slotProps={{
                            input: {
                              startAdornment:
                                field.value !== null && subUsersById[field.value] ? (
                                  <InputAdornment
                                    position="start"
                                    sx={{ m: 0, ml: 1 }}
                                  >
                                    <SubUserAvatar
                                      variant="square"
                                      username={subUsersById[field.value].label ?? ''}
                                    />
                                  </InputAdornment>
                                ) : null,
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
              </Stack>
            )}
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <LoopOutlinedIcon sx={{ fontSize: 20 }} />
              <Typography variant="body2">
                {ctIntl.formatMessage({ id: 'Repeat' })}
              </Typography>
              <TextFieldControlled
                required
                select
                fullWidth
                ControllerProps={{
                  control: recurringForm.control,
                  name: 'interval',
                }}
                onChange={({ target: { value } }) => {
                  if (isExcludeDays) {
                    recurringForm.setValue('isExcludeDays', false)
                  }

                  if (
                    includes(
                      [INTERVAL.CUSTOM, INTERVAL.WEEKLY, INTERVAL.BIWEEKLY],
                      value,
                    )
                  ) {
                    recurringForm.setValue('intervalConfig', {
                      ...INTERVAL_CONFIG_INITIAL_VALUES,
                      daysOfWeek: [],
                    })
                  } else {
                    recurringForm.setValue(
                      'intervalConfig',
                      INTERVAL_CONFIG_INITIAL_VALUES,
                    )
                  }

                  recurringForm.setValue('interval', value)
                }}
                sx={{ minWidth: '100px' }}
              >
                {INTERVAL_OPTIONS.map((option) => (
                  <MenuItem
                    key={option.value}
                    value={option.value}
                  >
                    {ctIntl.formatMessage({ id: option.label })}
                  </MenuItem>
                ))}
              </TextFieldControlled>
            </Stack>
            {match(interval)
              .with(
                INTERVAL.DAILY,
                INTERVAL.CUSTOM,
                INTERVAL.WEEKLY,
                INTERVAL.MONTHLY,
                INTERVAL.YEARLY,
                () => (
                  <Stack
                    direction="row"
                    alignItems="center"
                    gap={1}
                  >
                    {match(interval)
                      .with(
                        INTERVAL.CUSTOM,
                        INTERVAL.WEEKLY,
                        INTERVAL.MONTHLY,
                        INTERVAL.YEARLY,
                        () => (
                          <Typography variant="body2">
                            {ctIntl.formatMessage({ id: 'Every' })}
                          </Typography>
                        ),
                      )
                      .otherwise(() => null)}
                    {match(interval)
                      .with(INTERVAL.CUSTOM, () => (
                        <>
                          <TextFieldControlled
                            fullWidth
                            type="number"
                            variant="outlined"
                            ControllerProps={{
                              control: recurringForm.control,
                              name: 'intervalConfig.runEvery',
                            }}
                            onChange={(e, { controller }) => {
                              const value = e.target.value.trim()

                              if (value === '') {
                                controller.onChange(null)
                              } else if (/^[1-9]\d*$/.test(value)) {
                                controller.onChange(Number(value))
                              }
                            }}
                            sx={{ width: '70px' }}
                          />
                          <Typography variant="body2">
                            {ctIntl.formatMessage({ id: 'day(s)' })}
                          </Typography>
                        </>
                      ))
                      .with(INTERVAL.WEEKLY, () => (
                        <TextFieldControlled
                          select
                          variant="outlined"
                          ControllerProps={{
                            control: recurringForm.control,
                            name: 'intervalConfig.daysOfWeek',
                          }}
                          slotProps={{
                            select: {
                              multiple: true,
                              renderValue: (selected) =>
                                R.isArray(selected)
                                  ? selected.map((v) => v).join(', ')
                                  : '',
                            },
                          }}
                          sx={{ minWidth: '100px' }}
                        >
                          {getDaysOfWeekOptions('long').map(({ label, value }) => (
                            <MenuItem
                              key={value}
                              value={value}
                            >
                              <Checkbox
                                checked={intervalConfig.daysOfWeek.includes(value)}
                              />
                              <ListItemText primary={label} />
                            </MenuItem>
                          ))}
                        </TextFieldControlled>
                      ))
                      .with(
                        INTERVAL.DAILY,
                        INTERVAL.MONTHLY,
                        INTERVAL.YEARLY,

                        (interval) => (
                          <>
                            {interval === INTERVAL.YEARLY && (
                              <TextFieldControlled
                                select
                                variant="outlined"
                                ControllerProps={{
                                  control: recurringForm.control,
                                  name: 'intervalConfig.runEvery',
                                }}
                                sx={{ minWidth: '100px' }}
                              >
                                {MONTH_LIST.map((month) => (
                                  <MenuItem
                                    key={month}
                                    value={month}
                                  >
                                    {month}
                                  </MenuItem>
                                ))}
                              </TextFieldControlled>
                            )}
                            {(interval === INTERVAL.YEARLY ||
                              interval === INTERVAL.MONTHLY) && (
                              <TextFieldControlled
                                select
                                variant="outlined"
                                ControllerProps={{
                                  control: recurringForm.control,
                                  name:
                                    interval === INTERVAL.MONTHLY
                                      ? 'intervalConfig.runEvery'
                                      : 'intervalConfig.yearlyDay',
                                }}
                                sx={{ width: '70px' }}
                              >
                                {Array.from({ length: 31 }, (_, index) => (
                                  <MenuItem
                                    key={index + 1}
                                    value={index + 1}
                                  >
                                    {index + 1}
                                  </MenuItem>
                                ))}
                              </TextFieldControlled>
                            )}
                            <Stack
                              direction="row"
                              alignItems="center"
                            >
                              <Switch
                                checked={isExcludeDays}
                                onChange={() => {
                                  if (isExcludeDays) {
                                    recurringForm.setValue(
                                      'intervalConfig.excludeDays',
                                      INTERVAL_CONFIG_INITIAL_VALUES.excludeDays,
                                    )
                                  }

                                  recurringForm.setValue(
                                    'isExcludeDays',
                                    !isExcludeDays,
                                  )
                                }}
                              />
                              <Typography variant="body2">
                                {ctIntl.formatMessage({ id: 'Exclude days' })}
                              </Typography>
                            </Stack>
                          </>
                        ),
                      )
                      .otherwise(() => null)}
                  </Stack>
                ),
              )
              .otherwise(() => null)}
          </Stack>
          {isExcludeDays && (
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <Typography variant="body2">
                {ctIntl.formatMessage({ id: 'Choose days to exclude:' })}
              </Typography>
              <Stack
                direction="row"
                spacing={1}
              >
                {getDaysOfWeekOptions('short').map(({ value, label }) => (
                  <Controller
                    key={value}
                    name={`intervalConfig.excludeDays.${value}`}
                    control={recurringForm.control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                          />
                        }
                        label={label}
                      />
                    )}
                  />
                ))}
              </Stack>
            </Stack>
          )}
          <Stack
            direction="row"
            gap={2}
          >
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <Typography variant="body2">
                {ctIntl.formatMessage({ id: 'Start date' })}
              </Typography>
              <Controller
                name="dates.start"
                control={recurringForm.control}
                render={({ field, fieldState }) => (
                  <DatePicker
                    disablePast
                    label={ctIntl.formatMessage({
                      id: 'Start Date',
                    })}
                    value={
                      field.value
                        ? DeliveryDateTime.fromJSDate(field.value as Date)
                        : null
                    }
                    slotProps={{
                      textField: {
                        onKeyDown: (e) => {
                          e.preventDefault()
                        },
                        onBlur: field.onBlur,
                        error: fieldState.invalid,
                        helperText: ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        }),
                      },
                      field: { clearable: true },
                    }}
                    onChange={(newValue) => {
                      field.onChange(newValue?.toJSDate() ?? null)
                      recurringForm.trigger('dates.end')
                    }}
                  />
                )}
              />
            </Stack>
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <Typography variant="body2">
                {ctIntl.formatMessage({ id: 'End date' })}
              </Typography>
              <Controller
                name="dates.end"
                control={recurringForm.control}
                render={({ field, fieldState }) => (
                  <DatePicker
                    disablePast
                    label={ctIntl.formatMessage({
                      id: 'End Date',
                    })}
                    value={
                      field.value
                        ? DeliveryDateTime.fromJSDate(field.value as Date)
                        : null
                    }
                    onChange={(newValue) => {
                      field.onChange(newValue?.toJSDate() ?? null)
                      recurringForm.trigger('dates.end')
                    }}
                    slotProps={{
                      textField: {
                        onKeyDown: (e) => {
                          e.preventDefault()
                        },
                        onBlur: field.onBlur,
                        error: fieldState.invalid,
                        helperText: ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        }),
                      },
                      field: { clearable: true },
                    }}
                  />
                )}
              />
            </Stack>
          </Stack>
          {constructRecurrenceSummary({
            interval,
            dates,
            intervalConfig,
            formatList,
          })}
        </Stack>
        <Stack
          flex={1}
          minHeight={0}
        >
          <Stack
            direction="row"
            flex={1}
            height="100%"
          >
            <Stack
              minWidth="264px"
              maxWidth="300px"
              px={2}
              gap={2}
              overflow="auto"
            >
              <Stack
                direction="row"
                justifyContent="space-between"
              >
                <Typography variant="subtitle2">
                  {ctIntl.formatMessage({ id: 'delivery.jobStops' })}
                </Typography>
                <AddJobMenu
                  planId={planId}
                  startDate={dates.start}
                  onAddJobClick={handleAddnewJob}
                />
              </Stack>
              {match({ jobList })
                .with(
                  {
                    jobList: P.when((jobList) => jobList.length > 0),
                  },
                  ({ jobList }) => (
                    <StopList
                      selectedJobId={jobFormValues.jobId}
                      jobErrorIds={
                        formError && formError.type === ERROR_TYPE.JOB
                          ? formError.jobIds
                          : []
                      }
                      groupedStops={groupedStops}
                      onItemClick={(stop) => {
                        const findJobByStop = jobList.find(
                          (job) => job.jobId === stop.jobId,
                        )
                        setShowJobForm(true)
                        jobForm.reset({
                          ...findJobByStop,
                          formState: FORM_STATE.EDIT,
                        })
                      }}
                      onUpdateStops={(stops) => recurringForm.setValue('stops', stops)}
                    />
                  ),
                )
                .otherwise(() => (
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    align="center"
                  >
                    {ctIntl.formatMessage({
                      id: 'No job stops in routes. Start adding!',
                    })}
                  </Typography>
                ))}
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                startIcon={<AddIcon />}
                sx={{
                  width: '100%',
                  height: '86px',
                  borderRadius: 1,
                  border: '2px dashed',
                  borderColor: 'grey.300',
                  color: 'text.secondary',
                  marginBottom: '20px',
                }}
                onClick={handleAddnewJob}
              >
                {ctIntl.formatMessage({ id: 'Add New Job' })}
              </Button>
            </Stack>
            <Box sx={{ position: 'relative', width: '100%' }}>
              {showJobForm && (
                <JobForm
                  form={jobForm}
                  deleteStop={handleDeleteJob}
                  onClose={() => {
                    setShowJobForm(false)
                    jobForm.reset(jobDefaultValues)
                  }}
                  formSource={JOB_FORM_SOURCES.RECURRING_FORM}
                />
              )}
              <MapSection
                groupedStops={groupedCluster}
                handleDragStopMarker={handleUpdateStopAddress}
                onClickMarkerItem={(jobId) => {
                  const findJobByStop = jobList.find((job) => job.jobId === jobId)
                  setShowJobForm(true)
                  jobForm.reset({
                    ...findJobByStop,
                    formState: FORM_STATE.EDIT,
                  })
                }}
              />
            </Box>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default RecurringForm
