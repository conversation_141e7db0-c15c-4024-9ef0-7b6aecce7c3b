import { useCallback, useMemo, useState } from 'react'
import { keyBy } from 'lodash'
import { Box, Button, Stack, Typography } from '@karoo-ui/core'
import type { UseControlledFormReturn } from '@karoo-ui/core-rhf'
import AutoAwesomeOutlinedIcon from '@mui/icons-material/AutoAwesomeOutlined'
import LoopOutlinedIcon from '@mui/icons-material/LoopOutlined'
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined'
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircleOutlined'
import { useWatch } from 'react-hook-form'
import { useIntl } from 'react-intl'
import { match, P } from 'ts-pattern'

import useDriversList from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import type { FetchDeliveryJobDetails } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import type { LegsData } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobListFilter'
import type { PlanDetailReturn } from 'src/modules/deliveryRevamp/api/plans/types'
import useDeliveryPlanOptimizeStopsMutation from 'src/modules/deliveryRevamp/api/plans/useDeliveryPlanOptimizeStopsMutation'
import { JOB_FORM_SOURCES } from 'src/modules/deliveryRevamp/constants'
import { DELIVERY_COLOR } from 'src/modules/deliveryRevamp/constants/colors'
import { FORM_STATE } from 'src/modules/deliveryRevamp/constants/job'
import useGetSubUsers from 'src/modules/deliveryRevamp/hooks/useGetSubUsers'
import { ctIntl } from 'src/util-components/ctIntl'

import { EditJobForm } from '../../MapPanel/components/JobDetails/EditJob'
import type { JobDetailsFormType } from '../../MapPanel/components/JobDetails/Form/schema'
import {
  constructRecurrenceSummary,
  getAllStopsFromJobsApi,
  parseJobApiToForm,
} from '../helpers'
import useGroupedStops from '../hooks/useGroupedStops'
import type { RecurringFormType } from '../types'
import { JobFormWrapper } from './JobForm'
import MapSection from './MapSection'
import StopList from './StopList'

type Props = {
  jobForm: UseControlledFormReturn<JobDetailsFormType>
  recurringForm: UseControlledFormReturn<RecurringFormType>
  jobs: FetchDeliveryJobDetails.Return
  plan: PlanDetailReturn
  legs: Array<LegsData>
}

const ViewRecurring = ({ jobForm, recurringForm, jobs, plan, legs }: Props) => {
  const { formatList } = useIntl()

  const [selectedJob, setSelectedJob] = useState<
    FetchDeliveryJobDetails.Return[number]['formData'] | null
  >(null)

  const { data: driversList, isSuccess: isDriversSuccess } = useDriversList()

  const optimizeStops = useDeliveryPlanOptimizeStopsMutation()

  const [interval, intervalConfig, dates, driverId, stops, subuserId] = useWatch({
    control: recurringForm.control,
    name: ['interval', 'intervalConfig', 'dates', 'driverId', 'stops', 'subuserId'],
  })

  const { subUserOptions } = useGetSubUsers()

  const subuserData = useMemo(() => {
    if (!subuserId || subUserOptions.length === 0) return null

    return subUserOptions.find(({ value }) => value === subuserId)
  }, [subUserOptions, subuserId])

  const orderedStops = useMemo(() => {
    const getAllStops = getAllStopsFromJobsApi(jobs)
    const stopsLookup = keyBy(getAllStops, 'stopId')
    return stops.map((id, index) => ({
      ...stopsLookup[id],
      ordering: index + 1,
    }))
  }, [jobs, stops])

  const { groupedStops, groupedCluster } = useGroupedStops(orderedStops)

  const handleShowJobFromByJobId = useCallback(
    (jobId: number) => {
      const findJobByStop = Object.values(jobs).find(
        (job) => job.formData.jobId === jobId,
      )
      if (findJobByStop) {
        setSelectedJob(findJobByStop.formData)
        jobForm.reset({
          ...parseJobApiToForm(findJobByStop.formData),
          formState: FORM_STATE.EDIT,
        })
      }
    },
    [jobForm, jobs],
  )

  const handleOptimizeStop = useCallback(() => {
    optimizeStops.mutate({ planId: plan.planId })
  }, [optimizeStops, plan.planId])

  const isOptimizeButtonDisabled = orderedStops.length < 2

  return (
    <Stack
      flex={1}
      sx={{
        height: '100%',
      }}
    >
      <Box
        display="flex"
        flexWrap="wrap"
        gap={2}
        sx={{ pb: 2, px: 2 }}
      >
        {driverId && isDriversSuccess && (
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
          >
            <PersonOutlineOutlinedIcon sx={{ fontSize: 20 }} />
            <Typography variant="body2">
              {ctIntl.formatMessage({ id: 'Assign Driver' })} :
            </Typography>
            <Typography variant="body2">
              {driversList.byId[driverId].fullName}
            </Typography>
          </Stack>
        )}
        {subuserData && (
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
          >
            <SupervisedUserCircleIcon sx={{ fontSize: 20 }} />
            <Typography variant="body2">
              {ctIntl.formatMessage({ id: 'Assign Sub-user' })} :
            </Typography>
            <Typography variant="body2">{subuserData.name}</Typography>
          </Stack>
        )}
        {dates.start && (
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
          >
            <LoopOutlinedIcon sx={{ fontSize: 20 }} />
            <Typography variant="body2">
              {ctIntl.formatMessage({ id: 'Repeat' })} :
            </Typography>
            <Typography variant="body2">
              {constructRecurrenceSummary({
                interval,
                dates,
                intervalConfig,
                formatList,
              })}
            </Typography>
          </Stack>
        )}
      </Box>
      <Stack
        direction="row"
        flex={1}
        minHeight={0}
      >
        <Stack
          minWidth="264px"
          maxWidth="300px"
          px={2}
          gap={2}
          overflow="auto"
        >
          <Button
            size="small"
            disabled={isOptimizeButtonDisabled}
            loadingIndicator={ctIntl.formatMessage({
              id: 'delivery.rightPanel.driver.optimizing',
            })}
            loading={optimizeStops.isPending}
            onClick={handleOptimizeStop}
            startIcon={<AutoAwesomeOutlinedIcon />}
            sx={{
              lineHeight: 1,
              width: 'max-content',
              color: DELIVERY_COLOR.PURPLE,
            }}
          >
            {ctIntl.formatMessage({
              id: 'delivery.rightPanel.driver.optimize',
            })}
          </Button>
          <Stack
            direction="row"
            justifyContent="space-between"
          >
            <Typography variant="subtitle2">
              {ctIntl.formatMessage({ id: 'delivery.jobStops' })}
            </Typography>
          </Stack>
          {match({ jobs })
            .with(
              {
                jobs: P.when((jobs) => Object.keys(jobs).length > 0),
              },
              () => (
                <StopList
                  selectedJobId={selectedJob?.jobId}
                  isDragDisabled={true}
                  groupedStops={groupedStops}
                  onItemClick={(stop) => {
                    handleShowJobFromByJobId(stop.jobId)
                  }}
                />
              ),
            )
            .otherwise(() => (
              <Typography
                variant="caption"
                color="text.secondary"
                align="center"
              >
                {ctIntl.formatMessage({
                  id: 'No job stops in routes. Start adding!',
                })}
              </Typography>
            ))}
        </Stack>
        <Box sx={{ position: 'relative', width: '100%' }}>
          {selectedJob && (
            <JobFormWrapper>
              <EditJobForm
                form={jobForm}
                job={selectedJob}
                onClose={() => {
                  setSelectedJob(null)
                }}
                readOnly
                formSource={JOB_FORM_SOURCES.RECURRING_FORM}
              />
            </JobFormWrapper>
          )}
          <MapSection
            legs={legs}
            groupedStops={groupedCluster}
            onClickMarkerItem={(jobId) => {
              handleShowJobFromByJobId(jobId)
            }}
          />
        </Box>
      </Stack>
    </Stack>
  )
}

export default ViewRecurring
