import { useCallback, useMemo } from 'react'
import { IconButton } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import UploadOutlinedIcon from '@mui/icons-material/UploadOutlined'

import { IMPORT_TYPE } from 'src/modules/deliveryRevamp/constants'
import { SCHEDULE_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import useActiveImport from 'src/modules/deliveryRevamp/hooks/useActiveImport'
import { ctIntl } from 'src/util-components/ctIntl'

import Menu from '../../MapPanel/components/JobDetails/Form/components/Menu'

type Props = {
  onAddJobClick: () => void
  startDate: Date | null
  planId?: number
}

enum ACTION_TYPE {
  ADD_JOB = 'addJob',
  IMPORT_JOB = 'importJobs',
}

export default function AddJobMenu({ onAddJobClick, startDate, planId }: Props) {
  const { handleSetActiveImport } = useActiveImport()

  const handleImportJobsToRoute = useCallback(() => {
    if (!planId) return

    handleSetActiveImport({
      scheduleTypeId: startDate
        ? SCHEDULE_TYPE_ID.SCHEDULE
        : SCHEDULE_TYPE_ID.UNSCHEDULE,
      scheduledDeliveryTs: null,
      importType: IMPORT_TYPE.PLAN,
      planId,
    })
  }, [handleSetActiveImport, planId, startDate])

  const menuItems = useMemo(
    () => [
      {
        value: ACTION_TYPE.ADD_JOB,
        icon: (
          <AddIcon
            fontSize="small"
            sx={{ color: 'text.secondary' }}
          />
        ),
        label: ctIntl.formatMessage({ id: 'Add New Job' }),
      },
      ...(planId
        ? [
            {
              value: ACTION_TYPE.IMPORT_JOB,
              icon: (
                <UploadOutlinedIcon
                  fontSize="small"
                  sx={{ color: 'text.secondary' }}
                />
              ),
              label: ctIntl.formatMessage({ id: 'delivery.route.action.importJobs' }),
            },
          ]
        : []),
    ],
    [planId],
  )

  return (
    <Menu
      menuItems={menuItems}
      onItemClick={(item) => {
        if (item === ACTION_TYPE.ADD_JOB) {
          onAddJobClick()
        } else if (item === ACTION_TYPE.IMPORT_JOB) {
          handleImportJobsToRoute()
        }
      }}
    >
      <IconButton size="small">
        <AddIcon fontSize="small" />
      </IconButton>
    </Menu>
  )
}
