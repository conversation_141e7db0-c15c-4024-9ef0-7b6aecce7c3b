import { useMemo, useState } from 'react'
import { Button, IconButton, Stack } from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import { useWatch } from 'react-hook-form'
import { useHistory, useLocation } from 'react-router'

import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { useCheckDriverCapacityForJobForm } from 'src/modules/deliveryRevamp/api/jobs/useCreateDeliveryJobMutation'
import { useCreatePlanMutation } from 'src/modules/deliveryRevamp/api/plans/useCreatePlanMutation'
import { useEditPlanMutation } from 'src/modules/deliveryRevamp/api/plans/useEditPlanMutation'
import { FORM_STATE } from 'src/modules/deliveryRevamp/constants/job'
import useCloseFormConfirmation from 'src/modules/deliveryRevamp/hooks/useCloseFormConfirmation'
import useSubUserDataAccessScope from 'src/modules/deliveryRevamp/hooks/useSubUserDataAccessScope'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  getJobDetailsSchema,
  type JobDetailsFormType,
} from '../../MapPanel/components/JobDetails/Form/schema'
import RecurringForm from '../components/RecurringForm'
import {
  constructFormPayload,
  getDeliveryRecurringDialogMainPath,
  getJobErrorIds,
  jobFormDefaultValues,
  recurringFormDefaultValues,
} from '../helpers'
import { getRecurringFormSchema } from '../schema'
import { ERROR_TYPE, type ErrorState, type RecurringFormType } from '../types'

type Props = {
  onClose: () => void
  initialValues?: Partial<RecurringFormType>
}

const AddRecurring = ({ onClose, initialValues }: Props) => {
  const history = useHistory()
  const location = useLocation()

  const createDeliveryPlanMutation = useCreatePlanMutation()

  const editDeliveryPlanMutation = useEditPlanMutation()

  const checkDriverCapacityForJobForm = useCheckDriverCapacityForJobForm()

  const { doesSubUserHaveAccessToScope, canThisUserSeeSubuser } =
    useSubUserDataAccessScope()

  const jobDefaultValues = useMemo<JobDetailsFormType>(() => jobFormDefaultValues, [])

  const recurringDefaultValues = useMemo<RecurringFormType>(
    () => ({ ...recurringFormDefaultValues, ...initialValues }),
    [initialValues],
  )

  const jobDetailsSchema = useMemo(() => getJobDetailsSchema, [])

  const jobForm = useControlledForm<JobDetailsFormType>({
    mode: 'onChange',
    resolver: zodResolverV4(jobDetailsSchema),
    values: jobDefaultValues,
  })

  const recurringForm = useControlledForm<RecurringFormType>({
    mode: 'onChange',
    resolver: zodResolverV4(getRecurringFormSchema),
    values: recurringDefaultValues,
  })

  const [jobList] = useWatch({
    control: recurringForm.control,
    name: ['jobs'],
  })

  const [currentJobId, jobFormState] = useWatch({
    control: jobForm.control,
    name: ['jobId', 'formState'],
  })

  const [isSubmitError, setIsSubmitError] = useState(false)

  const formError: ErrorState = useMemo(() => {
    const filteredJobs = jobList.filter(
      (item) => item.jobId !== currentJobId || jobFormState !== FORM_STATE.CREATE,
    )
    const jobErrorIds = getJobErrorIds(filteredJobs)

    if (jobErrorIds.length > 0) {
      return {
        type: ERROR_TYPE.JOB,
        jobIds: jobErrorIds,
      }
    }

    if (isSubmitError) {
      return {
        type: ERROR_TYPE.NETWORK,
      }
    }

    return null
  }, [currentJobId, isSubmitError, jobFormState, jobList])

  const handleCloseDialog = () => {
    setIsSubmitError(false)
    recurringForm.reset(recurringDefaultValues)
    jobForm.reset(jobDefaultValues)
    onClose()
  }

  const submitForm = recurringForm.handleSubmit(async (values) => {
    const formState = recurringForm.getValues('formState')
    const dirtyFields = recurringForm.formState.dirtyFields

    let payload = constructFormPayload(values, {
      removeSubuserField:
        'subuserId' in values &&
        !(doesSubUserHaveAccessToScope() && canThisUserSeeSubuser()),
    })

    if (payload.contents?.jobs?.length) {
      try {
        await checkDriverCapacityForJobForm(
          payload.contents.jobs.map((job) => ({
            ...job,
            deliveryDriverId: payload.targetDriverId,
            scheduledDeliveryTs: payload.scheduledTime || null,
          })),
        )
      } catch {
        return
      }
    }

    const isUpdateFieldChanged =
      'driverId' in dirtyFields ||
      'stops' in dirtyFields ||
      'jobs' in dirtyFields ||
      (dirtyFields.dates && 'start' in dirtyFields.dates)

    if (
      values.planId &&
      !isUpdateFieldChanged &&
      formState === FORM_STATE.ROUTE_CONVERT
    ) {
      editDeliveryPlanMutation.mutate([{ planId: values.planId, ...payload }], {
        onSuccess: () => {
          handleCloseDialog()
        },
        onError: () => {
          setIsSubmitError(true)
        },
      })
    } else {
      if (
        [FORM_STATE.ROUTE_COPY, FORM_STATE.DUPLICATE].includes(formState) ||
        ([FORM_STATE.ROUTE_CONVERT, FORM_STATE.ROUTE_COPY_ASSIGN].includes(formState) &&
          isUpdateFieldChanged)
      ) {
        payload = constructFormPayload(values, {
          removeJobAndStopId: true,
          removeSubuserField:
            'subuserId' in values &&
            !(doesSubUserHaveAccessToScope() && canThisUserSeeSubuser()),
        })
      }

      createDeliveryPlanMutation.mutate(payload, {
        onSuccess: (data) => {
          handleCloseDialog()
          history.push(
            getDeliveryRecurringDialogMainPath(location, { id: data.planId }),
          )
        },
        onError: () => {
          setIsSubmitError(true)
        },
      })
    }
  })

  const { showConfirmation, confirmationPrompt } = useCloseFormConfirmation({
    isDirty:
      recurringForm.formState.isDirty &&
      Object.keys(recurringForm.formState.dirtyFields).some(
        (field) => field !== 'formState',
      ),
    onConfirm: handleCloseDialog,
  })

  return (
    <Stack
      sx={{
        p: 2,
        gap: 2,
        width: '100%',
        backgroundColor: '#F9F9F9',
        height: '100%',
      }}
      flex={1}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
      >
        <TextFieldControlled
          required
          ControllerProps={{
            control: recurringForm.control,
            name: 'routeName',
          }}
          placeholder={ctIntl.formatMessage({ id: 'Enter route name' })}
          sx={{ width: '50%' }}
        />
        <Stack
          direction="row"
          gap={1}
          alignItems="center"
        >
          <Button
            variant="contained"
            type="submit"
            size="small"
            onClick={submitForm}
            loading={createDeliveryPlanMutation.isPending}
            disabled={
              !(recurringForm.formState.isDirty && recurringForm.formState.isValid)
            }
          >
            {ctIntl.formatMessage({
              id: 'delivery.addRecurring',
            })}
          </Button>
          <IconButton
            onClick={() => {
              if (showConfirmation) {
                confirmationPrompt(handleCloseDialog)
                return
              }

              handleCloseDialog()
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </Stack>
      <Stack
        sx={{
          pt: 2,
          backgroundColor: 'white',
          minHeight: 0,
        }}
        flex={1}
      >
        <RecurringForm
          recurringForm={recurringForm}
          jobForm={jobForm}
          formError={formError}
          onSubmit={submitForm}
        />
      </Stack>
    </Stack>
  )
}

export default AddRecurring
