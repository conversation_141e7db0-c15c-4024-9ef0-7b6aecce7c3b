import { useMemo, useState } from 'react'
import { <PERSON><PERSON>, IconButton, Stack, Typography } from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'
import { useWatch } from 'react-hook-form'
import { useHistory, useLocation } from 'react-router'
import { match } from 'ts-pattern'

import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { useCheckDriverCapacityForJobForm } from 'src/modules/deliveryRevamp/api/jobs/useCreateDeliveryJobMutation'
import type { FetchDeliveryJobDetails } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import type { LegsData } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobListFilter'
import type { PlanDetailReturn } from 'src/modules/deliveryRevamp/api/plans/types'
import { useCreatePlanMutation } from 'src/modules/deliveryRevamp/api/plans/useCreatePlanMutation'
import { useDeleteDeliveryPlanMutation } from 'src/modules/deliveryRevamp/api/plans/useDeletePlanMutation'
import { useEditPlanMutation } from 'src/modules/deliveryRevamp/api/plans/useEditPlanMutation'
import { DELIVERY_COLOR } from 'src/modules/deliveryRevamp/constants/colors'
import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import useCloseFormConfirmation from 'src/modules/deliveryRevamp/hooks/useCloseFormConfirmation'
import useSubUserDataAccessScope from 'src/modules/deliveryRevamp/hooks/useSubUserDataAccessScope'
import { ctIntl } from 'src/util-components/ctIntl'

import { FORM_STATE } from '../../../constants/job'
import {
  getJobDetailsSchema,
  type JobDetailsFormType,
} from '../../MapPanel/components/JobDetails/Form/schema'
import RecurringForm from '../components/RecurringForm'
import ViewRecurring from '../components/ViewRecurring'
import {
  constructEditFormInitialValues,
  constructFormPayload,
  getDeliveryRecurringDialogMainPath,
  getJobErrorIds,
  jobFormDefaultValues,
  recurringFormDefaultValues,
} from '../helpers'
import { getRecurringFormSchema } from '../schema'
import { ERROR_TYPE, type ErrorState, type RecurringFormType } from '../types'

type Props = {
  onClose: () => void
  plan: PlanDetailReturn
  jobs: FetchDeliveryJobDetails.Return
  legs: Array<LegsData>
}

const EditRecurring = ({ onClose, plan, legs, jobs }: Props) => {
  const history = useHistory()
  const location = useLocation()

  const createDeliveryPlanMutation = useCreatePlanMutation()

  const editDeliveryPlanMutation = useEditPlanMutation()

  const deletePlanMutation = useDeleteDeliveryPlanMutation()

  const checkDriverCapacityForJobForm = useCheckDriverCapacityForJobForm()

  const { doesSubUserHaveAccessToScope, canThisUserSeeSubuser } =
    useSubUserDataAccessScope()

  const jobDefaultValues = useMemo<JobDetailsFormType>(() => jobFormDefaultValues, [])

  const recurringDefaultValues = useMemo<RecurringFormType>(
    () => recurringFormDefaultValues,
    [],
  )

  const jobDetailsSchema = useMemo(() => getJobDetailsSchema, [])

  const jobForm = useControlledForm<JobDetailsFormType>({
    mode: 'onChange',
    resolver: zodResolverV4(jobDetailsSchema),
    values: jobDefaultValues,
  })

  const recurringForm = useControlledForm<RecurringFormType>({
    mode: 'onChange',
    resolver: zodResolverV4(getRecurringFormSchema),
    values: constructEditFormInitialValues(plan, jobs),
  })

  const [jobList, recurringFormState, routeName] = useWatch({
    control: recurringForm.control,
    name: ['jobs', 'formState', 'routeName'],
  })

  const [currentJobId, jobFormState] = useWatch({
    control: jobForm.control,
    name: ['jobId', 'formState'],
  })

  const [isSubmitError, setIsSubmitError] = useState(false)

  const formError: ErrorState = useMemo(() => {
    const filteredJobs = jobList.filter(
      (item) => item.jobId !== currentJobId || jobFormState !== FORM_STATE.CREATE,
    )
    const jobErrorIds = getJobErrorIds(filteredJobs)

    if (jobErrorIds.length > 0) {
      return {
        type: ERROR_TYPE.JOB,
        jobIds: jobErrorIds,
      }
    }

    if (isSubmitError) {
      return {
        type: ERROR_TYPE.NETWORK,
      }
    }

    return null
  }, [currentJobId, isSubmitError, jobFormState, jobList])

  const handleResetDialog = () => {
    setIsSubmitError(false)
    recurringForm.reset(recurringDefaultValues)
    jobForm.reset(jobDefaultValues)
  }

  const handleCloseDialog = () => {
    handleResetDialog()
    onClose()
  }

  const submitForm = recurringForm.handleSubmit(async (values) => {
    const isEdit = values.formState === FORM_STATE.EDIT
    const dirtyFields = recurringForm.formState.dirtyFields
    const payload = isEdit
      ? constructFormPayload(values, {
          isStartDateChange: dirtyFields.dates && 'start' in dirtyFields.dates,
          removeSubuserField:
            'subuserId' in values &&
            !(doesSubUserHaveAccessToScope() && canThisUserSeeSubuser()),
        })
      : constructFormPayload(values, {
          removeJobAndStopId: true,
          removeSubuserField:
            'subuserId' in values &&
            !(doesSubUserHaveAccessToScope() && canThisUserSeeSubuser()),
        })

    if (payload.contents?.jobs?.length) {
      try {
        await checkDriverCapacityForJobForm(
          payload.contents.jobs.map((job) => ({
            ...job,
            deliveryDriverId: payload.targetDriverId,
            scheduledDeliveryTs: payload.scheduledTime || null,
          })),
        )
      } catch {
        return
      }
    }

    if (isEdit) {
      editDeliveryPlanMutation.mutate([{ planId: plan.planId, ...payload }], {
        onSuccess: () => {
          handleResetDialog()
        },
        onError: () => {
          setIsSubmitError(true)
        },
      })
    } else {
      createDeliveryPlanMutation.mutate(payload, {
        onSuccess: (data) => {
          handleResetDialog()
          history.push(
            getDeliveryRecurringDialogMainPath(location, { id: data.planId }),
          )
        },
        onError: () => {
          setIsSubmitError(true)
        },
      })
    }
  })

  const handleCancel = () => {
    setIsSubmitError(false)
    jobForm.reset(jobDefaultValues)
    recurringForm.reset()
    recurringForm.setValue('formState', FORM_STATE.VIEW)
  }

  const { showConfirmation, confirmationPrompt } = useCloseFormConfirmation({
    isDirty:
      recurringForm.formState.isDirty &&
      Object.keys(recurringForm.formState.dirtyFields).some(
        (field) => field !== 'formState',
      ),
    onConfirm: handleCloseDialog,
  })

  return (
    <Stack
      sx={{
        p: 2,
        gap: 2,
        width: '100%',
        backgroundColor: '#F9F9F9',
        height: '100%',
      }}
      flex={1}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
      >
        {recurringFormState === FORM_STATE.VIEW && (
          <Typography variant="h6">{routeName}</Typography>
        )}
        {match(recurringFormState)
          .with(FORM_STATE.EDIT, FORM_STATE.DUPLICATE, () => (
            <TextFieldControlled
              required
              ControllerProps={{
                control: recurringForm.control,
                name: 'routeName',
              }}
              placeholder={ctIntl.formatMessage({ id: 'Enter route name' })}
              sx={{ width: '50%' }}
            />
          ))
          .otherwise(() => null)}
        <Stack
          gap={1}
          direction="row"
          alignItems="center"
        >
          {recurringFormState === FORM_STATE.VIEW && (
            <>
              <Button
                variant="outlined"
                size="small"
                startIcon={<DeleteIcon />}
                loading={deletePlanMutation.isPending}
                sx={(theme) => ({
                  color: theme.palette.error.main,
                  borderColor: theme.palette.error.main,
                })}
                onClick={() =>
                  Dialog.alert({
                    title: ctIntl.formatMessage({
                      id: 'delivery.deleteRecurringRoute.confirmation.title',
                    }),
                    content: ctIntl.formatMessage({
                      id: 'delivery.deleteRecurringRoute.confirmation.body',
                    }),
                    confirmButtonLabel: ctIntl.formatMessage({ id: 'Confirm' }),
                    rejectButtonLabel: ctIntl.formatMessage({ id: 'Cancel' }),
                    onResult: () => {
                      deletePlanMutation.mutate(
                        { planId: plan.planId },
                        {
                          onSuccess: () => handleCloseDialog(),
                        },
                      )
                    },
                  })
                }
              >
                {ctIntl.formatMessage({ id: 'Delete' })}
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ContentCopyIcon />}
                sx={{
                  color: DELIVERY_COLOR.GREY,
                  borderColor: DELIVERY_COLOR.GREY,
                }}
                onClick={() => {
                  jobForm.reset(jobDefaultValues)
                  recurringForm.setValue('formState', FORM_STATE.DUPLICATE)
                }}
              >
                {ctIntl.formatMessage({ id: 'global.duplicate' })}
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<EditIcon />}
                onClick={() => {
                  jobForm.reset(jobDefaultValues)
                  recurringForm.setValue('formState', FORM_STATE.EDIT)
                }}
              >
                {ctIntl.formatMessage({
                  id: 'Edit',
                })}
              </Button>
            </>
          )}
          {match(recurringFormState)
            .with(FORM_STATE.EDIT, FORM_STATE.DUPLICATE, () => (
              <>
                <Button
                  variant="outlined"
                  size="small"
                  color="secondary"
                  onClick={() => {
                    if (showConfirmation) {
                      confirmationPrompt(handleCancel)
                      return
                    }

                    handleCancel()
                  }}
                >
                  {ctIntl.formatMessage({ id: 'Cancel' })}
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  size="small"
                  onClick={submitForm}
                  loading={
                    editDeliveryPlanMutation.isPending ||
                    createDeliveryPlanMutation.isPending
                  }
                  disabled={
                    !(
                      recurringForm.formState.isDirty && recurringForm.formState.isValid
                    )
                  }
                >
                  {ctIntl.formatMessage({
                    id:
                      recurringFormState === FORM_STATE.DUPLICATE
                        ? 'delivery.addRecurring'
                        : 'Save changes',
                  })}
                </Button>
              </>
            ))
            .otherwise(() => null)}
          <IconButton
            onClick={() => {
              if (showConfirmation) {
                confirmationPrompt(handleCloseDialog)
                return
              }

              handleCloseDialog()
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </Stack>
      <Stack
        sx={{
          pt: 2,
          backgroundColor: 'white',
          minHeight: 0,
        }}
        flex={1}
      >
        {recurringFormState === FORM_STATE.VIEW && (
          <ViewRecurring
            legs={legs}
            jobs={jobs}
            plan={plan}
            jobForm={jobForm}
            recurringForm={recurringForm}
          />
        )}
        {match(recurringFormState)
          .with(FORM_STATE.EDIT, FORM_STATE.DUPLICATE, () => (
            <RecurringForm
              recurringForm={recurringForm}
              jobForm={jobForm}
              formError={formError}
              onSubmit={submitForm}
            />
          ))
          .otherwise(() => null)}
      </Stack>
    </Stack>
  )
}

export default EditRecurring
