import type React from 'react'
import { useC<PERSON>back, useMemo, useState } from 'react'
import {
  Button,
  CircularProgressDelayedAbsolute,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import SearchIcon from '@mui/icons-material/Search'
import { useHistory, useLocation } from 'react-router'
import { match, P } from 'ts-pattern'
import { VList } from 'virtua'

import { useDriversAndRoutesContext } from 'src/modules/deliveryRevamp/components/MapPanel/DriversAndRoutesProvider'
import { getDeliveryRecurringDialogMainPath } from 'src/modules/deliveryRevamp/components/RecurringDialog/helpers'
import { ctIntl } from 'src/util-components/ctIntl'

import useDeliveryPlanListsQuery from '../../../../../api/plans/useDeliveryPlanListsQuery'
import RecurringCard from './RecurringCard'

type Props = {
  onClose: (event: React.MouseEvent<HTMLElement>) => void
}

export default function RecurringList({ onClose }: Props) {
  const history = useHistory()

  const location = useLocation()

  const { driversList } = useDriversAndRoutesContext()

  const [search, setSearch] = useState('')

  const planList = useDeliveryPlanListsQuery({ onlyRecurrent: true })

  const filteredPlanList = useMemo(() => {
    if (!planList.data) return { allIds: [], byId: {} }
    const { allIds, byId } = planList.data
    const filteredIds = allIds.filter((planId) => {
      const plan = byId[planId]
      let targetDriverFullName
      if (plan.targetDriverId) {
        targetDriverFullName = driversList?.byId[plan.targetDriverId]?.fullName
      }
      return (
        !search.trim() ||
        [plan.name, ...(targetDriverFullName ? [targetDriverFullName] : [])].some(
          (str) => str.toLowerCase().includes(search.trim().toLowerCase()),
        )
      )
    })
    return {
      allIds: filteredIds,
      byId,
    }
  }, [planList.data, driversList, search])

  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
  }, [])

  return (
    <Stack sx={{ flex: 1 }}>
      <Stack
        sx={{ py: 2, px: 2 }}
        direction="row"
        justifyContent="space-between"
      >
        <Typography variant="subtitle2">
          {ctIntl.formatMessage({ id: 'Manage Recurring' })}
        </Typography>
        <Stack direction="row">
          <Tooltip title={ctIntl.formatMessage({ id: 'delivery.addRecurring' })}>
            <IconButton
              size="small"
              onClick={() => {
                history.push(getDeliveryRecurringDialogMainPath(location, {}))
              }}
            >
              <AddIcon sx={{ fontSize: 'inherit' }} />
            </IconButton>
          </Tooltip>

          <Tooltip title={ctIntl.formatMessage({ id: 'Close' })}>
            <IconButton
              size="small"
              onClick={onClose}
            >
              <CloseIcon sx={{ fontSize: 'inherit' }} />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>
      {match(planList)
        .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, () => (
          <Stack sx={{ flex: 1 }}>
            <Stack sx={{ pb: 1, px: 2 }}>
              {Object.keys(filteredPlanList.byId).length > 0 && (
                <TextField
                  value={search}
                  onChange={handleSearch}
                  placeholder={ctIntl.formatMessage({ id: 'delivery.searchRecurring' })}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              )}
            </Stack>
            {match(filteredPlanList)
              .with({ byId: P.when((byId) => Object.keys(byId).length === 0) }, () => (
                <Stack
                  gap={2}
                  flex={1}
                  sx={{ p: 2 }}
                >
                  <Typography
                    variant="caption"
                    sx={{ color: 'text.secondary' }}
                  >
                    {ctIntl.formatMessage({
                      id: 'No recurring added yet. You can add new recurring.',
                    })}
                  </Typography>
                  <Button
                    variant="outlined"
                    color="secondary"
                    size="small"
                    startIcon={<AddIcon />}
                    sx={{
                      width: 'auto',
                      borderRadius: 1,
                      border: '2px dashed',
                      borderColor: 'grey.300',
                      color: 'text.secondary',
                    }}
                    onClick={() => {
                      history.push(getDeliveryRecurringDialogMainPath(location, {}))
                    }}
                  >
                    {ctIntl.formatMessage({ id: 'delivery.addRecurring' })}
                  </Button>
                </Stack>
              ))
              .with({ allIds: P.when((allIds) => allIds.length === 0) }, () => (
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary', textAlign: 'center', pt: 1, flex: 1 }}
                >
                  {ctIntl.formatMessage({ id: 'No data found' })}
                </Typography>
              ))
              .otherwise(({ allIds, byId }) => (
                <VList>
                  {allIds.map((planId) => (
                    <RecurringCard
                      withDroppable
                      key={planId}
                      plan={byId[planId]}
                    />
                  ))}
                </VList>
              ))}
          </Stack>
        ))
        .otherwise(() => null)}
    </Stack>
  )
}
