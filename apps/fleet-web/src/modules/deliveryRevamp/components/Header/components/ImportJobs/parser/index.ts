import lodash from 'lodash'
import { match, P } from 'ts-pattern'
import XLSX, { type ParsingOptions } from 'xlsx'

import { TODO_TYPE_ID } from 'src/modules/delivery/utils/constants'
import type { DeliveryCapabilitiesReturn } from 'src/modules/deliveryRevamp/api/lookup/types'
import { DELIVERY_MAX_WEIGHT, IMPORT_TYPE } from 'src/modules/deliveryRevamp/constants'
import { JOB_TYPE_ID, SCHEDULE_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import { UNIQ_ID } from 'src/modules/deliveryRevamp/helpers'
import type { ActiveImportType } from 'src/modules/deliveryRevamp/slice'
import { ctIntl } from 'src/util-components/ctIntl'

import { DeliveryDateTime } from '../../../../../utils/deliveryDateTime'
import { IMPORT_COLUMNS, UngroupType } from './constants'
import { generateItems } from './payload/items'
import { generateStops } from './payload/stops'
import type { JobImporterError, JobImporterInput, JobImporterOutPut } from './types'
import { ExcelColName } from './utils'

export type CSVXLSObject = {
  [key: string]: any
}

export type ExtraProps = {
  phonePrefix: string
  deliveryCapabilities?: DeliveryCapabilitiesReturn
} & ActiveImportType

export const getFileColumns = (rawData: Array<CSVXLSObject>) => {
  if (rawData.length > 0) {
    const keys = Object.keys(rawData[0] as any)
      .filter((item) => !item.includes('EMPTY'))
      .map((col) => col.trim())
    // Deduplicate. E.g. xxx and xxx_1 are duplicate
    return keys.map((col) =>
      col.replace(
        /_(\d+)$/,
        `(${ctIntl.formatMessage({
          id: 'delivery.rightPanel.driverInfo.option.duplicate',
        })})`,
      ),
    )
  }

  return []
}

const cellTagData = async (
  rawData: Array<CSVXLSObject>,
  userConfig: Record<string, string>,
) => {
  try {
    const errors = [] as Array<JobImporterError.Error>
    let columns = [] as Array<string>
    columns = getFileColumns(rawData)

    const invalidMapping = false
    const invalidUserSetting = false
    const userConfigArr = userConfig
      ? Object.entries(userConfig).map(([key, val]) => ({
          excelColumn: val,
          column: key,
        }))
      : []

    // Cleaning up data key into lowerCase
    const data = rawData.map((item) => {
      const temp = {} as { [k: string]: any }
      for (const [key, val] of Object.entries(item as any)) {
        if (!key.includes('EMPTY')) {
          temp[key.toLowerCase().replace('*', '')] = val
        }
      }
      return Object.defineProperty(temp, '__rowNum__', {
        value: item.__rowNum__,
        enumerable: false,
      })
    })

    const modMappingConfig = IMPORT_COLUMNS.map((item) => {
      const userCfg = userConfigArr.find((config) => config.column.trim() === item)
      return {
        column: item,
        excelColumn: userCfg
          ? userCfg.excelColumn.toLowerCase().replace('*', '')
          : item.toLowerCase(),
      }
    })

    const mappedKeyArray = modMappingConfig.map(
      (item) => `${item.excelColumn}|${item.column}`,
    )

    // e.g. { orderNumber: 'orderNumber|a|*' }
    const camelCaseMapToModKey: Record<string, string> = {}

    // Transform raw data to mapped data by using mapping config, and camel case it
    // e.g. { "Custom Order Number": 1 } -> { "orderNumber": 1 }
    const mappedData = data.map((item) => {
      const newObject = mappedKeyArray
        .map((key) => {
          const temp = {} as CSVXLSObject
          const fieldColumn = key.replace('*', '').split('|')[1]
          const excelColumn = key.split('|')[0]
          const camelCaseKey = lodash.camelCase(fieldColumn)
          const keyIndex = columns.findIndex(
            (col) => col.replace('*', '')?.toLowerCase() === excelColumn,
          )
          camelCaseMapToModKey[camelCaseKey] = `${camelCaseKey}|${ExcelColName(
            keyIndex >= 0 ? keyIndex : 701, //701 means it defaults to 'zz'
          )}|${key.includes('*') ? '*' : ''}`

          // Ignore unmapped field
          const found = userConfigArr.find(
            (config) => config.column.trim() === fieldColumn,
          )
          temp[camelCaseKey] =
            !found && !lodash.isEmpty(userConfigArr)
              ? ''
              : (item as CSVXLSObject)[excelColumn]
          //for order number, remove all whitespace
          //if it is empty, put a unique value starting with 'auto'
          if (camelCaseKey === 'orderNumber') {
            temp[camelCaseKey] =
              temp[camelCaseKey] !== undefined && temp[camelCaseKey] !== ''
                ? `${temp[camelCaseKey].toString().replaceAll(/\s/g, '')}`
                : `auto${UNIQ_ID()}`
          }
          return temp
        })
        .reduce((result, current) => Object.assign(result, current), {})

      return Object.defineProperty(newObject, '__rowNum__', {
        value: item.__rowNum__,
        enumerable: false,
      })
    })

    function rewriteOrderNumberAndKey(item: CSVXLSObject, suffix: number) {
      return Object.defineProperty(
        lodash.mapKeys(
          {
            ...item,
            orderNumber: `${item.orderNumber.trim()}-${suffix}`,
          },
          (_, key) => camelCaseMapToModKey[key] || key,
        ),
        '__rowNum__',
        {
          value: item.__rowNum__,
          enumerable: false,
        },
      )
    }

    // Since we don't have job group anymore, pre-unique the orderNumber of the rows with same orderNumber
    // e.g. [{ "orderNumber": "1", stopType: null }, { "orderNumber": "1", stopType: null }] -> [{ "orderNumber": "1-1", stopType: null }, { "orderNumber": "1-2", stopType: null }]
    // e.g. [{ "orderNumber": "1", stopType: "P" }, { "orderNumber": "1", stopType: null }, { "orderNumber": "1", stopType: null }] -> [[{ "orderNumber": "1-1" }, { "orderNumber": "1-1" }], [{ "orderNumber": "1-2" }, { "orderNumber": "1-2" }]]
    const groupedByOrderNumber = lodash.groupBy(mappedData, 'orderNumber')
    const pipedData = lodash.flatMap(groupedByOrderNumber, (group) => {
      if (group.length === 1) return group

      const noStopTypeInThisGroup =
        group.length > 1 &&
        !group.some((n) => ['p', 'pickup'].includes(n.stopType?.toLowerCase().trim()))

      if (!noStopTypeInThisGroup && group.length === 2) return group
      let countOfDropOff = 0
      return group.flatMap((item, index) => {
        if (noStopTypeInThisGroup) {
          const hasSameStopNo =
            item.stopNo && group.some((i) => i.stopNo === item.stopNo)
          return hasSameStopNo ? item : [rewriteOrderNumberAndKey(item, index + 1)]
        } else {
          const isPickupRow = ['p', 'pickup'].includes(
            item.stopType?.toLowerCase().trim(),
          )

          if (isPickupRow) return []
          const hasSameStopNo =
            item.stopNo && group.some((i) => i.stopNo === item.stopNo && !i.stopType)
          if (!hasSameStopNo) countOfDropOff += 1
          const pickupRows = group.filter((row) =>
            ['p', 'pickup'].includes(row.stopType?.toLowerCase().trim()),
          )
          if (hasSameStopNo) {
            const firstSameStopNoIndex = group.findIndex(
              (i) => i.stopNo === item.stopNo && !i.stopType,
            )
            if (firstSameStopNoIndex === index) {
              return [...pickupRows, item]
            } else {
              return [item]
            }
          } else {
            return [
              ...pickupRows.map((item) =>
                rewriteOrderNumberAndKey(item, countOfDropOff),
              ),
              rewriteOrderNumberAndKey(item, countOfDropOff),
            ]
          }
        }
      })
    })

    const taggedData = pipedData.map((item) => {
      // This process transform from the piped data
      // {
      //   'orderNumber|a|*': 'CT102',
      //   'driverName|b|': 'Abner',
      //   'sendDateTime|c|': 'ASAP',
      // }
      // to
      // {
      //   orderNumber: 'CT102|a,13',
      //   driverName: 'Abner|b,13',
      //   sendDateTime: 'ASAP|c,13'
      // }
      // This is Temporary fix for the GPS issue and will be succeded by DWA-1059
      const mappedKeys = Object.keys(item)
      // const gpsKey = mappedKeys.find((i) => i.startsWith('gps')) || ''
      // const latKey = mappedKeys.find((i) => i.startsWith('lat')) || ''
      // const lngKey = mappedKeys.find((i) => i.startsWith('lng')) || ''
      // const customerIdKey = mappedKeys.find((i) => i.startsWith('customerId')) || ''
      // const countryCode = mappedKeys.find((i) => i.startsWith('countryCode')) || ''
      // const allowedByPassLocationValidatiion = ['SG']
      const rowLocation = item.__rowNum__ + 1
      const taggedRows = Object.keys(item)
        .map((key) => {
          const temp = {} as CSVXLSObject
          // This here key is look like that orderNumber|a|*
          // split will ["orderNumber","a","*"]
          // * will be used to determine if the field is required or not.
          //
          // Pit fall::You will noticed that we use index+2 because the excel cells start from index 1 and our index starts from 0
          // inorder to tag the correct row index we will sum the index with 2
          // a,1--->Header to be skipped <--- Row index= 1
          // a,2--->data<---Row index=0+2
          // a,3--->data<---Row index=1+2
          // a,4--->data<---Row index=1+3

          const cellLocation = `${key.split('|')[1]},${rowLocation}`
          temp[`${key.split('|')[0]}`] = `${item[key] || ''}|${cellLocation}`
          // after that the temp object will have {orderNumber:CT102|a,3}

          // if (
          //   item[key] === '' &&
          //   key.includes('addressLine1') &&
          //   !allowedByPassLocationValidatiion.includes(item[countryCode])
          // ) {
          //   const isGPSIncluded =
          //     item[gpsKey] !== '' || (item[latKey] !== '' && item[lngKey] !== '')
          //   const isCustomerIDIncluded = item[customerIdKey] !== ''
          //   if (!isCustomerIDIncluded && !isGPSIncluded) {
          //     const err = {
          //       message: `Error required at field ${key.split('|')[1]},${rowLocation}`,
          //       cellLocation: [cellLocation],
          //       errorType: 'required' as JobImporterError.errorType,
          //     }
          //
          //     errors.push(err)
          //   }
          // }
          /** Validate maximum item weight set to 1_000_000 */
          const itemWeightKey = mappedKeys.find((i) => i.startsWith('itemWeight')) || ''
          const itemWeightUnitKey =
            mappedKeys.find((i) => i.startsWith('itemWeightUnit')) || ''
          if (
            key.split('|')[0] === 'itemWeight' &&
            item[itemWeightKey] !== '' &&
            +item[itemWeightKey] > DELIVERY_MAX_WEIGHT
          ) {
            const cellLocation = `${key.split('|')[1]},${rowLocation}`
            errors.push({
              message: `Max weight of ${DELIVERY_MAX_WEIGHT.toLocaleString('en-US')} ${
                !!itemWeightUnitKey && item[itemWeightUnitKey]
              } exceeded at field ${key.split('|')[1]},${rowLocation}`,
              cellLocation: [cellLocation],
              errorType: 'invalid' as JobImporterError.errorType,
            })
          }

          return temp
        })
        .reduce((result, current) => Object.assign(result, current), {})
      taggedRows.rowLocation = rowLocation
      return taggedRows
    }) as Array<JobImporterInput.ParsedObject>

    return {
      taggedData: lodash.groupBy(taggedData, (item) =>
        item.orderNumber.split('|')[0].trim().toLowerCase(),
      ),
      taggedErrors: errors,
      pipedData,
      columns,
      invalidMapping,
      invalidUserSetting,
    }
  } catch (e) {
    throw e?.message || e
  }
}

export const readXLSXFile = (
  file: string | ArrayBuffer | null,
  type: ParsingOptions['type'],
) => {
  const wb = XLSX.read(file, {
    type,
    cellDates: true,
    raw: type === 'string',
  })
  // Some sheet may not be named Sheet1 so we just get the first initial sheet in
  // these other instances based on what comes up first in wb.Sheets.keys
  const firstSheetName = wb.SheetNames[0]
  const sheetData = wb.Sheets[firstSheetName]
  const date1904 = wb.Workbook?.WBProps?.date1904

  function getDataRange(data: XLSX.WorkSheet) {
    const dataWithValues = lodash.pickBy(data, (value) => !!value.v)
    const cellNamesWithValues = lodash.keys(dataWithValues)
    const cellsWithValues = cellNamesWithValues.map((cell) =>
      XLSX.utils.decode_cell(cell),
    )
    const maxRow = lodash.max(cellsWithValues.map((cell) => cell.r))
    const maxColumn = lodash.max(cellsWithValues.map((cell) => cell.c))
    const lastCellName = XLSX.utils.encode_cell({
      c: maxColumn as number,
      r: maxRow as number,
    })
    return `A1:${lastCellName}`
  }

  sheetData['!ref'] = getDataRange(sheetData)

  return XLSX.utils
    .sheet_to_json(sheetData, {
      blankrows: false,
      defval: '',
    })
    .filter((object) =>
      Object.values(object as Record<string, any>).some(
        (val) => lodash.isString(val) && val.trim(),
      ),
    )
    .map((object) => {
      const result: CSVXLSObject = {}
      // Avoid the time offset issue of Date1904
      for (const [key, value] of Object.entries(object as CSVXLSObject)) {
        if (lodash.isDate(value)) {
          result[key] = XLSX.SSF.format('YYYY-MM-DD hh:mm', value, { date1904 })
        }
      }

      return Object.assign(object as CSVXLSObject, result)
    }) as Array<CSVXLSObject>
}

const filterItemTodos = (todos: JobImporterOutPut.Item['todos']) => {
  const lastScanToAttachIndex = todos.findLastIndex(
    (todo) => todo.todoTypeId === TODO_TYPE_ID.SCAN_TO_ATTACH,
  )

  return todos.filter(
    (todo, index) =>
      index === lastScanToAttachIndex ||
      todo.todoTypeId !== TODO_TYPE_ID.SCAN_TO_ATTACH,
  )
}

const splitLocations = (locations?: string) => locations?.split(',') ?? []

const parsedPayload = async (
  data: Array<CSVXLSObject>,
  config: Record<string, string>,
  extraProps: ExtraProps,
): Promise<{
  data: Array<{ jobs: Array<JobImporterOutPut.Job> }>
  errors: Array<JobImporterError.Error | undefined>
  columns: Array<string>
  invalidMapping: boolean
  invalidUserSetting: boolean
  rawData: Array<unknown>
}> => {
  try {
    const {
      taggedData,
      taggedErrors,
      pipedData,
      columns,
      invalidMapping,
      invalidUserSetting,
    } = await cellTagData(data, config)
    const unTaggedData = pipedData.map((item) => {
      const newObject = Object.keys(item)
        .map((key) => {
          const temp = {} as CSVXLSObject
          temp[`${key.split('|')[0]}`] = item[key] === undefined ? null : `${item[key]}`
          return temp
        })
        .reduce((result, current) => Object.assign(result, current), {})
      newObject.rowLocation = item.__rowNum__ + 1
      return newObject
    }) as Array<JobImporterInput.ParsedObject>

    const errors = [] as Array<JobImporterError.Error | undefined>
    let parsedData = [] as Array<{ jobs: Array<JobImporterOutPut.Job> }>
    if (!invalidMapping) {
      // Groupinguntagged data by orderNumber
      const orderGroupedData = lodash.groupBy(unTaggedData, (n) =>
        n.orderNumber.trim().toLowerCase(),
      )

      parsedData = Object.entries(orderGroupedData).flatMap(([key, value]) => {
        const uniqueOrder: JobImporterInput.ParsedObject = lodash.clone(value[0])
        for (const key of Object.keys(value[0])) {
          uniqueOrder[key] = value?.find((n) => n[key])?.[key]
        }

        //For 'requiredCapabilities', retrieve all unique values on the column from all rows
        uniqueOrder.requiredCapabilities = value.reduce(
          (acc, cur) => `${(cur.specialRequirements || '').toString()},${acc}`,
          '',
        )

        //For 'job labels', retrieve all unique values on the column from all rows
        uniqueOrder.labels = value.reduce(
          (acc, cur) => `${(cur.jobLabels || '').toString()},${acc}`,
          '',
        )

        const { stops, multiStops, stopErrors } = generateStops(value, taggedData[key])

        const { items, itemErrors } = generateItems(value, taggedData[key])

        const planDriverAssignment = (
          uniqueOrderItem: JobImporterInput.ParsedObject | undefined,
        ) => {
          /* The order of condition will determine the priority of the property that should be
          submitted to the payload */
          if (extraProps.importType === IMPORT_TYPE.PLAN) {
            return { planId: extraProps.planId }
          }

          if (extraProps.importType === IMPORT_TYPE.DRIVER) {
            return { deliveryDriverId: extraProps.deliveryDriverId }
          }

          if (uniqueOrderItem?.planName && extraProps.importType === IMPORT_TYPE.JOB) {
            return { planName: uniqueOrderItem?.planName.trim() }
          }

          if (
            uniqueOrderItem?.driverName &&
            extraProps.importType === IMPORT_TYPE.JOB
          ) {
            return { driverName: uniqueOrderItem?.driverName.trim() }
          }

          return
        }

        const schedulePayloadData = (
          uniqueOrderData: JobImporterInput.ParsedObject | undefined,
        ) => {
          /* Temporarily removing this part while backend is still in progress to avoid blocking
          the normal flow and will still pass the schedule type and schedule timestamp */
          // if (extraProps.planId || uniqueOrder?.planName) return
          if (
            extraProps.importType === IMPORT_TYPE.PLAN ||
            extraProps.importType === IMPORT_TYPE.DRIVER
          ) {
            return {
              scheduleTypeId: extraProps.scheduleTypeId,
              scheduledDeliveryTs: extraProps.scheduledDeliveryTs,
            }
          }

          if (
            uniqueOrder?.sendDateTime &&
            uniqueOrderData?.sendDateTime &&
            uniqueOrder.sendDateTime.trim() !== ''
          ) {
            const toDateTime = DeliveryDateTime.fromJSDate(
              // Add a space to prevent reconizing 'yyyy-MM-dd' as UTC date
              new Date(uniqueOrderData.sendDateTime + ' '),
            )
            const scheduledDeliveryTs = match(toDateTime)
              .with({ isValid: P.when((isValid) => !isValid) }, () =>
                DeliveryDateTime.fromFormat(
                  uniqueOrderData.sendDateTime.trim(),
                  'dd/MM/yyyy HH:mm',
                )
                  .startOf('second')
                  .toISO({ suppressMilliseconds: true }),
              )
              .with(
                P.when((toDateTime) =>
                  toDateTime.hasSame(DeliveryDateTime.now(), 'day'),
                ),
                () => null,
              )
              .otherwise(() =>
                toDateTime.startOf('second').toISO({ suppressMilliseconds: true }),
              )

            return {
              scheduleTypeId: SCHEDULE_TYPE_ID.SCHEDULE,
              scheduledDeliveryTs,
            }
          } else {
            return {
              scheduleTypeId: SCHEDULE_TYPE_ID.UNSCHEDULE,
              scheduledDeliveryTs: null,
            }
          }
        }

        let result
        if (stops) {
          // This is For Single Stop Payload
          result = [
            {
              rowLocation: stops
                .map((stop) => stop?.rowLocation)
                .join(',')
                .toString(),
              jobTypeId: stops.length > 1 ? JOB_TYPE_ID.PD : JOB_TYPE_ID.SINGLE,
              ...schedulePayloadData(uniqueOrder),
              ...planDriverAssignment(uniqueOrder),
              referenceNumber: uniqueOrder?.orderNumber
                ?.trim()
                ?.toLowerCase()
                ?.startsWith('auto')
                ? ''
                : uniqueOrder?.orderNumber,
              stops: stops,
              items: items || [],
              ...(uniqueOrder?.requiredCapabilities && {
                requiredCapabilities: lodash.uniq(
                  uniqueOrder?.requiredCapabilities &&
                    uniqueOrder?.requiredCapabilities
                      .split(',')
                      .filter((value: string) => !lodash.isEmpty(value)),
                ),
              }),
              ...(uniqueOrder?.labels && {
                labels: lodash.uniq(
                  uniqueOrder?.labels &&
                    uniqueOrder?.labels
                      .split(',')
                      .filter((value: string) => !lodash.isEmpty(value)),
                ),
              }),
            },
          ]
        } else {
          // This is For multi stop Payload
          result =
            (multiStops &&
              multiStops.map((item) => ({
                rowLocation: item
                  .map((stop) => stop?.rowLocation)
                  .join(',')
                  .toString(),
                jobTypeId: item.length > 1 ? JOB_TYPE_ID.PD : JOB_TYPE_ID.SINGLE,
                ...schedulePayloadData(uniqueOrder),
                ...planDriverAssignment(uniqueOrder),
                referenceNumber:
                  uniqueOrder &&
                  uniqueOrder.orderNumber &&
                  !uniqueOrder.orderNumber.trim().toLowerCase().startsWith('auto')
                    ? uniqueOrder?.orderNumber
                    : '',
                stops: item || [],
                items: items || [],
                ...(uniqueOrder?.requiredCapabilities && {
                  requiredCapabilities: lodash.uniq(
                    uniqueOrder?.requiredCapabilities &&
                      uniqueOrder?.requiredCapabilities
                        .split(',')
                        .filter((value: string) => !lodash.isEmpty(value)),
                  ),
                }),
                ...(uniqueOrder?.labels && {
                  labels: lodash.uniq(
                    uniqueOrder?.labels &&
                      uniqueOrder?.labels
                        .split(',')
                        .filter((value: string) => !lodash.isEmpty(value)),
                  ),
                }),
              }))) ||
            []
        }
        errors.push(...stopErrors, ...itemErrors)

        result = (result ?? []).map((job) => {
          const jobLocationsSet = new Set(splitLocations(job.rowLocation))

          const filteredItems = job.items
            .filter((item) => {
              const itemLocations = splitLocations(item.rowLocation)
              if (itemLocations.length === 0) return true
              return itemLocations.some((loc) => jobLocationsSet.has(loc))
            })
            .map((item) => ({
              ...item,
              todos: filterItemTodos(item.todos),
            }))

          return {
            ...job,
            items: filteredItems,
          }
        })

        const ungroup = value.find((row) => row?.ungroup)?.ungroup.toLowerCase() || ''
        if (
          result &&
          (result.length === 1 ||
            ungroup === UngroupType.NO ||
            ungroup === UngroupType.FALSE ||
            ungroup === UngroupType.BLANK ||
            ungroup === UngroupType.YES ||
            ungroup === UngroupType.TRUE)
        ) {
          return result.map((splitResult, index) => ({
            jobs: [
              {
                ...splitResult,
                ...(splitResult.referenceNumber && {
                  referenceNumber:
                    result.length > 1
                      ? `${splitResult.referenceNumber}-${index + 1}`
                      : splitResult.referenceNumber,
                }),
              },
            ] as Array<JobImporterOutPut.Job>,
          })) as Array<{ jobs: Array<JobImporterOutPut.Job> }>
        } else {
          const cellLocation = taggedData[key]
            .find((row) => row?.ungroup)
            ?.ungroup.split('|')[1]
          const err = {
            message: `Unrecognized values at cell ${cellLocation}`,
            cellLocation: cellLocation,
            errorType: 'invalid' as JobImporterError.errorType,
          }
          errors.push(err)
          return [{ jobs: result }] as Array<{
            jobs: Array<JobImporterOutPut.Job>
          }>
        }
      })
      errors.push(...taggedErrors)
    }

    return {
      data: errors.length > 1 ? [] : parsedData,
      errors,
      columns,
      invalidMapping,
      invalidUserSetting,
      rawData: data,
    }
  } catch (e) {
    throw e?.message || e
  }
}
export default parsedPayload
