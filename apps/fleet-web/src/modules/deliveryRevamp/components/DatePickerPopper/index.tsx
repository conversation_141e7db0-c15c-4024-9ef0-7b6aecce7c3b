import {
  ClickAwayListener,
  Fade,
  Paper,
  Popper,
  Stack,
  StaticDatePicker,
  Typography,
  type DatePickerProps,
  type PickersShortcutsItem,
  type PopperProps,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { useParams } from 'react-router'

import { ctIntl } from 'src/util-components/ctIntl'

import { DELIVERY_PAGES } from '../../types'
import { DeliveryDateTime } from '../../utils/deliveryDateTime'
import type { PAGE_OPTIONS } from '../Header'

export function generateShortcuts(disableUnscheduled = false) {
  return [
    {
      label: ctIntl.formatMessage({ id: 'Today' }),
      getValue: () => DeliveryDateTime.now(),
    },
    ...(disableUnscheduled
      ? []
      : [
          {
            label: ctIntl.formatMessage({ id: 'Unscheduled' }),
            getValue: () => null,
          },
        ]),
  ] satisfies Array<PickersShortcutsItem<DateTime | null>>
}

export function generateSlots() {
  return {
    actionBar: () => null,
    toolbar: () => (
      <Typography
        variant="subtitle2"
        sx={{
          paddingY: 2,
          paddingLeft: 2,
        }}
      >
        {ctIntl.formatMessage({ id: 'delivery.scheduledDeliveryDate' })}
      </Typography>
    ),
  }
}

function DatePickerPopper({
  value,
  onChange,
  anchorEl,
  onClose,
  datePickerProps,
  popperProps,
}: {
  value: DateTime | null
  onChange: (newDate: DateTime | null) => void
  anchorEl: HTMLElement | null
  onClose: () => void
  datePickerProps?: DatePickerProps<DateTime>
  popperProps?: Partial<PopperProps>
}) {
  const isOpen = Boolean(anchorEl)
  const { page } = useParams<{ page: (typeof PAGE_OPTIONS)[number]['value'] }>()

  return (
    <Popper
      id="calendar-popper"
      open={isOpen}
      anchorEl={anchorEl}
      transition
      disablePortal
      sx={{ zIndex: (theme) => theme.zIndex.modal }}
      {...popperProps}
    >
      {({ TransitionProps }) => (
        <Fade {...TransitionProps}>
          <Paper elevation={6}>
            <ClickAwayListener
              onClickAway={() => {
                if (isOpen) {
                  onClose()
                }
              }}
            >
              <Stack>
                <StaticDatePicker
                  {...datePickerProps}
                  value={value}
                  onChange={(newDate) => {
                    onClose()
                    onChange(newDate?.startOf('day') ?? null)
                  }}
                  slots={generateSlots()}
                  slotProps={{
                    shortcuts: {
                      items: generateShortcuts(page === DELIVERY_PAGES.TIMELINE),
                    },
                    toolbar: {
                      hidden: true,
                    },
                  }}
                />
              </Stack>
            </ClickAwayListener>
          </Paper>
        </Fade>
      )}
    </Popper>
  )
}

export default DatePickerPopper
