import { use<PERSON><PERSON>back, useMemo, useTransition } from 'react'
import {
  Autocomplete,
  IconButton,
  InputAdornment,
  ListItemText,
  Stack,
  TextField,
  Tooltip,
} from '@karoo-ui/core'
import {
  TextFieldControlled,
  useNumberFieldProps,
  type UseControlledFormReturn,
} from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import InfoIcon from '@mui/icons-material/InfoOutlined'
import LowPriorityIcon from '@mui/icons-material/LowPriority'
import { Controller, useWatch } from 'react-hook-form'

import {
  FORM_STATE,
  getPriorityOptionTranslationKeys,
  PRIORITY_ID,
  PRIORITY_OPTIONS,
} from 'src/modules/deliveryRevamp/constants/job'
import { ctIntl } from 'src/util-components/ctIntl'

import JobFieldContainer from '../../../components/JobFieldContainer'
import type { JobDetailsFormType } from '../../schema'

const CUSTOM_PRIORITY_INITIAL_VALUE = 1
const CUSTOM_PRIORITY_MIN = 1
const CUSTOM_PRIORITY_MAX = 10000
const CUSTOM_PRIORITY_STEP = 1
const CUSTOM_PRIORITY_WIDTH = '191px'

type PriorityOptionType = (typeof PRIORITY_OPTIONS)[number]

type Props = {
  form: UseControlledFormReturn<JobDetailsFormType>
  setShouldDisplayOptimizationPriorityField: (value: boolean) => void
}

const PriorityOptionItem = ({
  id,
  props,
}: {
  id: PriorityOptionType['id']
  props: React.HTMLAttributes<HTMLLIElement>
}) => {
  const translationKeys = getPriorityOptionTranslationKeys(id)

  return (
    <li
      {...props}
      key={id}
    >
      <ListItemText>{ctIntl.formatMessage({ id: translationKeys.label })}</ListItemText>

      <Tooltip
        title={ctIntl.formatMessage({
          id: translationKeys.infoTooltip,
        })}
        placement="right"
      >
        <InfoIcon
          fontSize="small"
          sx={{ color: 'text.secondary' }}
        />
      </Tooltip>
    </li>
  )
}

const CustomPriorityInput = ({
  control,
}: {
  control: UseControlledFormReturn<JobDetailsFormType>['control']
}) => {
  const numberFieldProps = useNumberFieldProps({ required: true })

  return (
    <JobFieldContainer>
      <Stack
        ml={5}
        sx={{
          width: CUSTOM_PRIORITY_WIDTH,
        }}
      >
        <TextFieldControlled
          ControllerProps={{
            name: 'optimizationPriority',
            control: control,
          }}
          placeholder={ctIntl.formatMessage({
            id: 'Priority Points',
          })}
          {...numberFieldProps}
          slotProps={{
            input: {
              inputProps: {
                min: CUSTOM_PRIORITY_MIN,
                max: CUSTOM_PRIORITY_MAX,
                step: CUSTOM_PRIORITY_STEP,
              },
              endAdornment: <InputAdornment position="end">ppt</InputAdornment>,
            },
          }}
        />
      </Stack>
    </JobFieldContainer>
  )
}

const OptimizationPriorityField = ({
  form,
  setShouldDisplayOptimizationPriorityField,
}: Props) => {
  const [isPending, startTransition] = useTransition()
  const [hasCustomPriority, formState] = useWatch({
    control: form.control,
    name: ['hasCustomPriority', 'formState'],
  })

  const handleClose = useCallback(() => {
    startTransition(() => {
      setShouldDisplayOptimizationPriorityField(false)
      form.setValue('optimizationPriority', null)
      form.setValue('hasCustomPriority', false)
    })
  }, [form, setShouldDisplayOptimizationPriorityField])

  const handlePriorityChange = useCallback(
    (_event: any, option: PriorityOptionType | null) => {
      startTransition(() => {
        if (option?.id === PRIORITY_ID.CUSTOM) {
          form.setValue('hasCustomPriority', true)
          form.setValue('optimizationPriority', CUSTOM_PRIORITY_INITIAL_VALUE)
        } else {
          form.setValue('hasCustomPriority', false)
          form.setValue('optimizationPriority', option?.value ?? null)
        }
      })
    },
    [form],
  )

  const selectedOption = useMemo(() => {
    if (hasCustomPriority) {
      return PRIORITY_OPTIONS.find((option) => option.id === PRIORITY_ID.CUSTOM)
    }
    const currentValue = form.getValues('optimizationPriority')
    return PRIORITY_OPTIONS.find((option) => option.value === currentValue) ?? null
  }, [hasCustomPriority, form])

  return (
    <Stack gap={1}>
      <JobFieldContainer>
        <JobFieldContainer.Icon>
          <LowPriorityIcon />
        </JobFieldContainer.Icon>

        <Stack
          direction="row"
          alignItems="center"
          flex={1}
          gap={1}
        >
          <Controller
            control={form.control}
            name="optimizationPriority"
            render={() => (
              <Autocomplete
                fullWidth
                options={PRIORITY_OPTIONS}
                value={selectedOption}
                onChange={handlePriorityChange}
                disabled={isPending}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder={ctIntl.formatMessage({
                      id: 'Optimization Priority',
                    })}
                  />
                )}
                getOptionLabel={(option) =>
                  ctIntl.formatMessage({
                    id: getPriorityOptionTranslationKeys(option.id).label,
                  })
                }
                renderOption={(props, item) => (
                  <PriorityOptionItem
                    id={item.id}
                    props={props}
                  />
                )}
              />
            )}
          />

          <Tooltip
            title={ctIntl.formatMessage({
              id: 'delivery.jobForm.optimizationPriority.tooltip',
            })}
          >
            <InfoIcon fontSize="small" />
          </Tooltip>

          {formState !== FORM_STATE.STARTED && (
            <IconButton
              onClick={handleClose}
              disableRipple
              size="small"
              disabled={isPending}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )}
        </Stack>
      </JobFieldContainer>

      {hasCustomPriority && <CustomPriorityInput control={form.control} />}
    </Stack>
  )
}

export default OptimizationPriorityField
