import { Stack, Typography } from '@karoo-ui/core'
import type { Control } from 'react-hook-form'

import { ITEM_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import { ctIntl } from 'src/util-components/ctIntl'

import type { JobDetailsFormType } from '../../../schema'
import ItemUnitHeaderForm from '../itemUnitHeaderForm'
import { ItemInputTextField, TotalWeight } from '../utils'

const PackageForm = ({
  index,
  control,
  deleteItem,
}: {
  index: number
  control: Control<JobDetailsFormType>
  deleteItem: () => void
}) => (
  <Stack spacing={2}>
    <ItemUnitHeaderForm
      control={control}
      index={index}
      type={ITEM_TYPE_ID.PACKAGE}
      deleteItem={deleteItem}
      canEdit={false}
    />
    <ItemInputTextField
      label={ctIntl.formatMessage({ id: 'Tracking Number' })}
      control={control}
      name={`items.${index}.trackingNumber`}
      formType="text"
    />

    <Typography variant="caption">
      {ctIntl.formatMessage({
        id: 'delivery.jobStops.item.package.weightAndDimensions',
      })}
    </Typography>
    <ItemInputTextField
      label={ctIntl.formatMessage({ id: 'Weight' })}
      control={control}
      name={`items.${index}.weight`}
      formType="number"
    />
    <Stack
      direction="row"
      spacing={2}
      alignItems="center"
    >
      <ItemInputTextField
        label={ctIntl.formatMessage({ id: 'Quantity' })}
        control={control}
        name={`items.${index}.quantity`}
        formType="number"
        sx={{
          flex: 1,
        }}
      />
      <Stack sx={{ flex: 1, alignItems: 'flex-end' }}>
        <Typography variant="caption">
          {ctIntl.formatMessage({ id: 'Total Weight' })}
        </Typography>
        <TotalWeight
          control={control}
          index={index}
        />
      </Stack>
    </Stack>
    <Stack
      direction="row"
      spacing={2}
    >
      <ItemInputTextField
        label={ctIntl.formatMessage({ id: 'Length' })}
        control={control}
        name={`items.${index}.length`}
        formType="number"
      />
      <ItemInputTextField
        label={ctIntl.formatMessage({ id: 'Width' })}
        control={control}
        name={`items.${index}.width`}
        formType="number"
      />
      <ItemInputTextField
        label={ctIntl.formatMessage({ id: 'Height' })}
        control={control}
        name={`items.${index}.height`}
        formType="number"
      />
    </Stack>
    <ItemInputTextField
      label={ctIntl.formatMessage({ id: 'SKU' })}
      control={control}
      name={`items.${index}.sku`}
      formType="text"
    />
    <ItemInputTextField
      label="UPC"
      control={control}
      name={`items.${index}.upc`}
      formType="text"
    />
  </Stack>
)

export default PackageForm
