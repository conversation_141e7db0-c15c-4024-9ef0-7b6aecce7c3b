import { useMemo } from 'react'
import {
  Autocomplete,
  Button,
  createFilterOptions,
  IconButton,
  OverflowTypography,
  Paper,
  Popper,
  Stack,
  TextField,
  Tooltip,
  type PaperProps,
  type PopperProps,
  type TextFieldProps,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import PermContactCalendarOutlinedIcon from '@mui/icons-material/PermContactCalendarOutlined'
import { useHistory, useLocation } from 'react-router-dom'
import { match, P } from 'ts-pattern'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import {
  useDeliveryCustomerOptions,
  type FetchDeliveryCustomers,
} from 'src/modules/deliveryRevamp/api/customers/useDeliveryCustomers'
import { getDeliverySettingsDialogMainPath } from 'src/modules/deliveryRevamp/components/SettingsDialog/utils'
import { SINGAPORE_COUNTRY_DATA } from 'src/modules/deliveryRevamp/constants/countries'
import { getContactCountryCode } from 'src/modules/deliveryRevamp/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import type { AddressSchema } from '../../schema'

type CustomerOptionValue = string | null

const filter = createFilterOptions<{
  value: string
  label: string
  name: string
}>()

type Props = {
  value: {
    id: CustomerOptionValue
    name: CustomerOptionValue
  }
  onChange: (
    customerId: CustomerOptionValue,
    address: AddressSchema | null,
    customerName: CustomerOptionValue,
  ) => void
  onOpenAddressForm: (mode: 'EDIT' | 'ADD') => void
  disabled?: boolean
  textFieldProps?: TextFieldProps
}

const CustomersAutocomplete = ({
  value: { id: customerId, name: customerName },
  onChange,
  onOpenAddressForm,
  disabled = false,
  textFieldProps,
}: Props) => {
  const { customerOptions, customerLists, isLoading } = useDeliveryCustomerOptions()

  const isDeletedCustomer = useMemo(() => {
    if (!customerId) return false

    const customerIds = new Set(customerOptions.map((item) => item.value))
    return !customerIds.has(customerId)
  }, [customerId, customerOptions])

  const customerOptionsWithCustomCustomer = useMemo(() => {
    if ((!customerId || isDeletedCustomer) && customerName) {
      return [
        ...customerOptions,
        {
          value: `custom_${customerName}`,
          label: customerName,
          name: customerName,
        },
      ]
    }

    return customerOptions
  }, [customerId, customerName, isDeletedCustomer, customerOptions])

  const { customerOptionsById, customerListById } = useMemo(() => {
    const customerOptionsById = customerOptionsWithCustomCustomer.reduce<
      Record<string, (typeof customerOptionsWithCustomCustomer)[number]>
    >((acc, option) => {
      acc[option.value] = option
      return acc
    }, {})

    const customerListById = customerLists.reduce<
      Record<string, (typeof customerLists)[number]>
    >((acc, customer) => {
      acc[customer.customerId] = customer
      return acc
    }, {})

    return {
      customerOptionsById,
      customerListById,
    }
  }, [customerOptionsWithCustomCustomer, customerLists])

  const selectedOption = useMemo(() => {
    if (customerId && !isDeletedCustomer) {
      const option = customerOptionsById[customerId]
      return match(option)
        .with(P.nullish, () => null)
        .with({ name: P.not(customerName) }, () => ({
          ...option,
          name: customerName || '',
        }))
        .otherwise(() => option)
    }

    if (customerName) {
      // Loop through the options in reverse order
      for (let i = customerOptionsWithCustomCustomer.length - 1; i >= 0; i--) {
        const option = customerOptionsWithCustomCustomer[i]
        if (option.value === `custom_${customerName}`) {
          return option
        }
      }
    }

    return null
  }, [
    customerId,
    customerName,
    isDeletedCustomer,
    customerOptionsById,
    customerOptionsWithCustomCustomer,
  ])

  const autocompleteProps = getAutocompleteVirtualizedProps({
    options: customerOptionsWithCustomCustomer,
    renderRowSingleItemContent: (option) => {
      const customerName = option.value ? customerOptionsById[option.value]?.name : ''

      return (
        <Stack
          overflow="hidden"
          key={option.value}
        >
          {customerName}

          <OverflowTypography
            typographyProps={{
              variant: 'caption',
              color: 'text.secondary',
            }}
            tooltipProps={{
              placement: 'left',
            }}
          >
            {option.label}
          </OverflowTypography>
        </Stack>
      )
    },
    customItemSize: 48,
  })

  return (
    <Autocomplete
      fullWidth
      loading={isLoading}
      loadingText={`${ctIntl.formatMessage({ id: 'Loading' })}...`}
      disabled={disabled}
      value={selectedOption}
      {...autocompleteProps}
      onChange={(_event, option) => {
        if (option === null) {
          onChange(null, null, null)
        } else {
          const customer = customerListById[option.value]

          if (customer) {
            onChange(
              customer.customerId,
              getCustomerAddress(customer),
              customer.customerName,
            )
          }
        }
      }}
      getOptionLabel={(option) => {
        if (typeof option === 'string') return option
        return option.name ?? ''
      }}
      filterOptions={(options, params) => {
        if (params.inputValue !== '') {
          const searchTerm = params.inputValue.toLowerCase()
          return options.filter((option) =>
            option.name.toLowerCase().includes(searchTerm),
          )
        }

        return filter(options, params)
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          {...textFieldProps}
        />
      )}
      PaperComponent={(props) => (
        <PaperWithFooter
          {...props}
          onOpenAddressForm={onOpenAddressForm}
        />
      )}
      PopperComponent={CustomPopper}
      selectOnFocus
      clearOnBlur
      handleHomeEndKeys
    />
  )
}

export default CustomersAutocomplete

const getCustomerAddress = (
  customer: FetchDeliveryCustomers.ParsedCustomerDetails,
): AddressSchema => ({
  addressLine1: customer.addressLine1 || '',
  addressLine2: customer.addressLine2 || '',
  latitude: customer.latitude || '',
  longitude: customer.longitude || '',
  latLng: customer.latLng || '',
  postalCode: customer.postalCode || '',
  countryId: customer.countryId || SINGAPORE_COUNTRY_DATA.country_code,
  phone: {
    countryCode: getContactCountryCode(customer.contactCode),
    number: customer.contactNumber || '',
  },
  email: customer.email || '',
  saveToAddressBook: false,
})

const PaperWithFooter = ({
  onOpenAddressForm,
  ...paperProps
}: PaperProps & { onOpenAddressForm: Props['onOpenAddressForm'] }) => {
  const history = useHistory()
  const location = useLocation()

  const handleNewRecordClick = () => {
    onOpenAddressForm('ADD')
  }

  return (
    <Paper {...paperProps}>
      {paperProps.children}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ px: 2, py: 1, borderTop: '1px solid', borderColor: 'divider' }}
      >
        <Button
          size="small"
          variant="text"
          startIcon={<AddIcon fontSize="small" />}
          onMouseDown={handleNewRecordClick}
        >
          {ctIntl.formatMessage({ id: 'delivery.addressBook.addNewRecord' })}
        </Button>

        <Tooltip title={ctIntl.formatMessage({ id: 'Open address book' })}>
          <IconButton
            onMouseDown={() => {
              history.push(getDeliverySettingsDialogMainPath(location, 'ADDRESS_BOOK'))
            }}
            size="small"
          >
            <PermContactCalendarOutlinedIcon
              fontSize="small"
              sx={{ color: 'action.active' }}
            />
          </IconButton>
        </Tooltip>
      </Stack>
    </Paper>
  )
}

const CustomPopper = (props: PopperProps) => (
  <Popper
    {...props}
    style={{ width: 250 }}
  />
)
