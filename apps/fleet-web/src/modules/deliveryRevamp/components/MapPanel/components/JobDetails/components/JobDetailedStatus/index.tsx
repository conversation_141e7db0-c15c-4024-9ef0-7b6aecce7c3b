import { useMemo, type ComponentProps } from 'react'
import { Box, Stack, Typography } from '@karoo-ui/core'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { match, P } from 'ts-pattern'

import type { FetchDeliveryJobDetails } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import JobStatusChip from 'src/modules/deliveryRevamp/components/JobCard/components/JobStatusChip'
import {
  JOB_TYPE_ID,
  STOP_STATUS_ID,
  type JobProgressStatus,
} from 'src/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import {
  formatTimeWindow,
  TIME_WINDOW_FORMAT,
} from 'src/modules/deliveryRevamp/helpers'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  job: FetchDeliveryJobDetails.Return[number]['formData']
}

const JobDetailedStatus = ({ job }: Props) => {
  const {
    data: { isPastDate, isToday },
  } = useDeliveryMainPageContext()

  const isTodayOrPastDay = useMemo(() => isPastDate || isToday, [isPastDate, isToday])

  const jobStatusChipProps = useMemo<ComponentProps<typeof JobStatusChip>>(() => {
    if (job.stops.jobTypeId === JOB_TYPE_ID.SINGLE) {
      return getJobStatusChipProps(job.stops.single, job)
    } else if (
      job.detailedStatus.status === 'STARTED_PICKUP' ||
      job.detailedStatus.status === 'ARRIVED_PICKUP' ||
      job.detailedStatus.status === 'COMPLETED_PICKUP'
    ) {
      return getJobStatusChipProps(job.stops.pickup, job)
    }

    return getJobStatusChipProps(job.stops.dropoff, job)
  }, [job])

  return match({
    inProgressOrRejected: job.inProgressOrRejected,
    driverId: job.driverId,
    isTodayOrPastDay,
  })
    .with({ inProgressOrRejected: true }, () => (
      <Stack
        sx={{
          mx: 2,
          mb: 1,
          p: 1,
          gap: 1,
          backgroundColor: JOB_DETAILED_STATUS_BG_COLORS[job.detailedStatus.status],
          borderRadius: 0.5,
        }}
      >
        <Typography variant="subtitle2">
          {ctIntl.formatMessage({
            id: JOB_DETAILED_STATUS_TRANSLATION_KEYS[job.detailedStatus.status],
          })}
        </Typography>
        <Stack
          direction="row"
          alignItems="center"
        >
          <JobStatusChip
            {...jobStatusChipProps}
            hideUnassignedChip
          />
          {jobStatusChipProps.isLate &&
            [STOP_STATUS_ID.ARRIVED, STOP_STATUS_ID.COMPLETED].includes(
              jobStatusChipProps.stopStatusId,
            ) && (
              <Stack
                direction="row"
                alignItems="center"
                gap={0.2}
              >
                <InfoOutlinedIcon sx={{ color: 'error.main', fontSize: '12px' }} />
                <Box
                  sx={{ color: 'text.secondary', fontSize: 10, whiteSpace: 'nowrap' }}
                >
                  {ctIntl.formatMessage({ id: 'late' })}
                </Box>
              </Stack>
            )}
        </Stack>
      </Stack>
    ))
    .with(
      { inProgressOrRejected: false, driverId: P.nonNullable, isTodayOrPastDay: true },
      () => (
        <Stack
          direction="row"
          alignItems="center"
          sx={{
            mx: 2,
          }}
        >
          <JobStatusChip
            {...jobStatusChipProps}
            formattedTimeWindow={null}
            hideUnassignedChip
          />
          {jobStatusChipProps.hasLateRiskBasedOnPreStop && (
            <Box sx={{ color: 'text.secondary', fontSize: 10, whiteSpace: 'nowrap' }}>
              {ctIntl.formatMessage({ id: 'might arrive late' })}
            </Box>
          )}
        </Stack>
      ),
    )
    .otherwise(() => null)
}

export default JobDetailedStatus

const getJobStatusChipProps = (
  stop: FetchDeliveryJobDetails.StopReturn,
  job: Props['job'],
): ComponentProps<typeof JobStatusChip> => {
  const activityTimestamp = job.detailedStatus.timestamp
    ? job.detailedStatus.timestamp.raw
    : null
  const arriveTime = stop.arriveAt
    ? { timeFrom: stop.arriveAt, timeTo: stop.arriveAt }
    : stop.arriveBetween

  return {
    jobStatusId: job.statusId,
    stopStatusId: stop.stopStatusId,
    stopTypeId: stop.stopTypeId,
    activityTimestamp,
    expectedDurationInMinutes: stop.duration,
    actualDurationInMinutes: stop.activityTimestamps.duration?.minutes,
    ...formatTimeWindow(
      arriveTime
        ? [
            {
              stopId: Number(stop.id),
              timeFrom: DeliveryDateTime.fromJSDate(arriveTime.timeFrom).toFormat(
                TIME_WINDOW_FORMAT,
              ),
              timeTo: DeliveryDateTime.fromJSDate(arriveTime.timeTo).toFormat(
                TIME_WINDOW_FORMAT,
              ),
            },
          ]
        : null,
      activityTimestamp,
    ),
    rejectedReason: stop.statusRemarks,
    hasLateRiskBasedOnPreStop: Boolean(
      job.stopIdWithLateRisk && stop.id && stop.id === job.stopIdWithLateRisk,
    ),
    deliveryDate: job.scheduledDate,
  }
}

const JOB_DETAILED_STATUS_TRANSLATION_KEYS = {
  NOT_STARTED: 'delivery.jobDetailedStatus.notStarted',
  STARTED_PICKUP: 'delivery.jobDetailedStatus.startedPickup',
  ARRIVED_PICKUP: 'delivery.jobDetailedStatus.arrivedPickup',
  COMPLETED_PICKUP: 'delivery.jobDetailedStatus.completedPickup',
  AWAITING_DROPOFF: 'delivery.jobDetailedStatus.awaitingDropoff',
  STARTED_DROPOFF: 'delivery.jobDetailedStatus.startedDropoff',
  ARRIVED_DROPOFF: 'delivery.jobDetailedStatus.arrivedDropoff',
  COMPLETED_DROPOFF: 'delivery.jobDetailedStatus.completed',
  REJECTED: 'delivery.jobDetailedStatus.rejected',
} satisfies Record<JobProgressStatus, string>

const JOB_DETAILED_STATUS_BG_COLORS = {
  NOT_STARTED: '#FEF4EF',
  STARTED_PICKUP: '#FEF4EF',
  ARRIVED_PICKUP: '#FEF4EF',
  COMPLETED_PICKUP: '#FEF4EF',
  AWAITING_DROPOFF: '#FEF4EF',
  STARTED_DROPOFF: '#FEF4EF',
  ARRIVED_DROPOFF: '#FEF4EF',
  COMPLETED_DROPOFF: '#F1F9F2',
  REJECTED: '#F9F1F1',
}
