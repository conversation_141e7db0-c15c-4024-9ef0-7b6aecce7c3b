import { useMemo, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON>, I<PERSON><PERSON><PERSON><PERSON>, Stack, Typography } from '@karoo-ui/core'
import {
  TextFieldControlled,
  useNumberFieldProps,
  type UseControlledFormReturn,
} from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import LocationOnIcon from '@mui/icons-material/LocationOnOutlined'
import NotesOutlinedIcon from '@mui/icons-material/NotesOutlined'
import TimelapseOutlinedIcon from '@mui/icons-material/TimelapseOutlined'
import { Controller, useController, useWatch } from 'react-hook-form'
import { match, P } from 'ts-pattern'

import type { FetchDeliveryJobDetails } from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import type { JOB_FORM_SOURCES } from 'src/modules/deliveryRevamp/constants'
import {
  FORM_STATE,
  STOP_STATUS_ID,
  STOP_TYPE_ID,
  type JOB_STATUS_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import SingleArrowIcon from 'assets/deliveryRevamp/single-arrow.svg'
import DropoffIcon from 'assets/deliveryRevamp/stop-type-drop-no-background.svg'
import PickupIcon from 'assets/deliveryRevamp/stop-type-pickup-no-background.svg'

import ExtraFieldsButton from '../../../../components/ExtraFieldsButton'
import JobFieldContainer from '../../../../components/JobFieldContainer'
import StopStatus from '../../../../components/StopStatus'
import AddressForm from '../../../components/AddressForm'
import CustomersAutocomplete from '../../../components/CustomersAutocomplete'
import ToDo from '../../../components/ToDo'
import type { JobDetailsFormType } from '../../../schema'
import { getStopAddressDefaultValues, getStopsBaseFieldName } from '../../../utils'
import TimeInput from '../TimeInput'

type Props = {
  type: STOP_TYPE_ID
  form: UseControlledFormReturn<JobDetailsFormType>
  jobStop?: {
    jobStatusId: JOB_STATUS_ID
    stop: FetchDeliveryJobDetails.StopReturn
  }
  formSource: JOB_FORM_SOURCES
}

const ENTITY_TYPE = { type: 'stop' } as const
const DISPLAY_APPLY_OPTIONS = false

const StopFormCard = ({ type, form, jobStop, formSource }: Props) => {
  const {
    data: { isPastDate, isToday },
  } = useDeliveryMainPageContext()

  const numberFieldProps = useNumberFieldProps()

  const {
    field: { value: duration },
  } = useController({
    control: form.control,
    name: `${getStopsBaseFieldName(type)}.duration` as const,
  })

  const {
    field: { value: notes },
  } = useController({
    control: form.control,
    name: `${getStopsBaseFieldName(type)}.notes` as const,
  })

  const stopIdWithLateRisk = useWatch({
    control: form.control,
    name: 'stopIdWithLateRisk',
  })

  const driverId = useWatch({
    control: form.control,
    name: 'driverId',
  })

  const formStateValue = useWatch({
    control: form.control,
    name: 'formState',
  })

  const scheduledDate = useWatch({
    control: form.control,
    name: 'scheduledDate',
  })

  const customerPath = `${getStopsBaseFieldName(type)}.customer` as const

  const [shouldDisplayDurationField, setShouldDisplayDurationField] = useState(
    () => duration !== null,
  )

  const [shouldDisplayNotesField, setShouldDisplayNotesField] = useState(
    () => notes !== '',
  )

  const [addressAnchorEl, setAddressAnchorEl] = useState<HTMLElement | null>(null)
  const [addressFormMode, setAddressFormMode] = useState<'EDIT' | 'ADD'>('ADD')

  // Create a ref for the container element
  const addressContainerRef = useRef<HTMLDivElement>(null)

  const hasLateRiskBasedOnPreStop = useMemo(
    () => Boolean(jobStop?.stop.id && stopIdWithLateRisk === jobStop.stop.id),
    [jobStop?.stop.id, stopIdWithLateRisk],
  )

  const showStopStatusChip = useMemo(() => {
    if (jobStop) {
      return match({ stopStatusId: jobStop.stop.stopStatusId })
        .with(
          {
            stopStatusId: P.when((stopStatusId) =>
              [
                STOP_STATUS_ID.STARTED,
                STOP_STATUS_ID.ARRIVED,
                STOP_STATUS_ID.COMPLETED,
                STOP_STATUS_ID.REJECTED,
              ].includes(stopStatusId),
            ),
          },
          () => true,
        )
        .with(
          { stopStatusId: STOP_STATUS_ID.CREATED },
          () => (isPastDate || isToday) && driverId,
        )
        .otherwise(() => false)
    }
    return false
  }, [driverId, isPastDate, isToday, jobStop])

  // Update the handler to always use the container ref
  const handleOpenAddressForm = (mode: 'EDIT' | 'ADD') => {
    setAddressAnchorEl(addressContainerRef.current)
    setAddressFormMode(mode)
  }

  return (
    <Stack
      key={`${getStopsBaseFieldName(type)}.address`}
      sx={{
        padding: 2,
        gap: 2,
        alignSelf: 'stretch',
        borderRadius: 0.5,
        backgroundColor: 'grey.100',
      }}
      divider={<Divider />}
    >
      <Stack gap={2}>
        <Stack
          direction="row"
          gap={1}
          alignItems="center"
        >
          <Typography
            variant="subtitle2"
            fontWeight={500}
            color="text.primary"
          >
            {ctIntl.formatMessage({
              id:
                type === STOP_TYPE_ID.SINGLE || type === STOP_TYPE_ID.DROPOFF
                  ? 'Deliver-to'
                  : 'Pick up',
            })}
          </Typography>

          {type === STOP_TYPE_ID.SINGLE ? (
            <svg
              {...makeSanitizedInnerHtmlProp({ dirtyHtml: SingleArrowIcon })}
              height="12px"
              width="12px"
            />
          ) : (
            <svg
              {...makeSanitizedInnerHtmlProp({
                dirtyHtml: type === STOP_TYPE_ID.DROPOFF ? DropoffIcon : PickupIcon,
              })}
              height="1rem"
              width="1rem"
            />
          )}
        </Stack>

        {jobStop && showStopStatusChip && (
          <StopStatus
            jobStatusId={jobStop.jobStatusId}
            stop={jobStop.stop}
            hasLateRiskBasedOnPreStop={hasLateRiskBasedOnPreStop}
            scheduledDate={scheduledDate}
          />
        )}

        <Stack gap={1}>
          <Stack ref={addressContainerRef}>
            <JobFieldContainer alignItems="start">
              <JobFieldContainer.Icon>
                <LocationOnIcon />
              </JobFieldContainer.Icon>

              <Controller
                name={customerPath}
                control={form.control}
                render={({ field, fieldState }) => (
                  <CustomersAutocomplete
                    value={field.value}
                    onChange={(customerId, address, customerName) => {
                      form.setValue(customerPath, {
                        id: customerId,
                        name: customerName ?? '',
                      })

                      form.setValue(
                        `${getStopsBaseFieldName(type)}.address`,
                        address ?? getStopAddressDefaultValues(),
                      )
                    }}
                    onOpenAddressForm={handleOpenAddressForm}
                    textFieldProps={{
                      placeholder: ctIntl.formatMessage({
                        id: 'Search/Add an address',
                      }),
                      helperText: ctIntl.formatMessage({
                        id: fieldState.error?.message ?? '',
                      }),
                      error: !!fieldState.error,
                      sx: {
                        '& .MuiInputBase-root': {
                          backgroundColor: 'common.white',
                        },
                      },
                    }}
                  />
                )}
              />
            </JobFieldContainer>

            <Controller
              name={`${getStopsBaseFieldName(type)}.address`}
              control={form.control}
              render={() => (
                <AddressForm
                  form={form}
                  baseFieldName={getStopsBaseFieldName(type)}
                  anchorEl={addressAnchorEl}
                  onOpenAddressForm={handleOpenAddressForm}
                  onClose={() => setAddressAnchorEl(null)}
                  mode={addressFormMode}
                />
              )}
            />
          </Stack>

          <TimeInput
            type={type}
            form={form}
            formSource={formSource}
          />

          {shouldDisplayDurationField && (
            <JobFieldContainer>
              <JobFieldContainer.Icon>
                <TimelapseOutlinedIcon />
              </JobFieldContainer.Icon>

              <Stack
                direction="row"
                alignItems="center"
                flex={1}
                gap={1}
              >
                <TextFieldControlled
                  ControllerProps={{
                    name: `${getStopsBaseFieldName(type)}.duration`,
                    control: form.control,
                  }}
                  label={ctIntl.formatMessage({
                    id: 'delivery.jobForm.durationInMinutes',
                  })}
                  {...numberFieldProps}
                  slotProps={{
                    input: {
                      inputProps: {
                        min: 1,
                        max: 10000,
                      },
                      sx: {
                        backgroundColor: 'common.white',
                      },
                    },
                  }}
                  fullWidth
                />
                {formStateValue !== FORM_STATE.STARTED && (
                  <IconButton
                    onClick={() => {
                      setShouldDisplayDurationField(false)
                      form.setValue(`${getStopsBaseFieldName(type)}.duration`, null)
                    }}
                    disableRipple
                    size="small"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                )}
              </Stack>
            </JobFieldContainer>
          )}

          {shouldDisplayNotesField && (
            <JobFieldContainer>
              <JobFieldContainer.Icon>
                <NotesOutlinedIcon />
              </JobFieldContainer.Icon>

              <Stack
                direction="row"
                flex={1}
                gap={1}
              >
                <TextFieldControlled
                  ControllerProps={{
                    name: `${getStopsBaseFieldName(type)}.notes`,
                    control: form.control,
                  }}
                  fullWidth
                  multiline
                  rows={3}
                  placeholder={ctIntl.formatMessage({ id: 'Add Notes' })}
                  slotProps={{
                    input: {
                      sx: {
                        backgroundColor: 'common.white',
                      },
                    },
                  }}
                />
                {formStateValue !== FORM_STATE.STARTED && (
                  <IconButton
                    onClick={() => {
                      setShouldDisplayNotesField(false)
                      form.setValue(`${getStopsBaseFieldName(type)}.notes`, '')
                    }}
                    sx={{ alignSelf: 'start', mt: 1 }}
                    disableRipple
                    size="small"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                )}
              </Stack>
            </JobFieldContainer>
          )}

          <Stack
            direction="row"
            flexWrap="wrap"
            columnGap={1}
          >
            {!shouldDisplayDurationField && formStateValue !== FORM_STATE.STARTED && (
              <ExtraFieldsButton onClick={() => setShouldDisplayDurationField(true)}>
                {ctIntl.formatMessage({ id: 'Duration' })}
              </ExtraFieldsButton>
            )}
            {!shouldDisplayNotesField && formStateValue !== FORM_STATE.STARTED && (
              <ExtraFieldsButton onClick={() => setShouldDisplayNotesField(true)}>
                {ctIntl.formatMessage({ id: 'Notes' })}
              </ExtraFieldsButton>
            )}
          </Stack>
        </Stack>
      </Stack>
      <ToDo
        control={form.control}
        isTodoFromStop
        formPathName={`${getStopsBaseFieldName(type)}.todos`}
        displayApplyOptions={DISPLAY_APPLY_OPTIONS}
        entity={ENTITY_TYPE}
      />
    </Stack>
  )
}

export default StopFormCard
