import { I<PERSON><PERSON><PERSON>on, Paper, Stack, Typography } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import { useController, useFieldArray, type Control } from 'react-hook-form'
import { match } from 'ts-pattern'

import JobSection from 'src/modules/deliveryRevamp/components/MapPanel/components/JobDetails/components/Section'
import {
  FORM_STATE,
  ITEM_TYPE_DESC,
  ITEM_TYPE_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import { ctIntl } from 'src/util-components/ctIntl'

import type { JobDetailsFormType } from '../../schema'
import Menu from '../Menu'
import Package from './Package'
import Person from './Person'
import Service from './Service'
import { createItemWithDefaults, ItemIcon } from './utils'

const itemsMenu = [
  {
    label: ITEM_TYPE_DESC[ITEM_TYPE_ID.PACKAGE],
    value: ITEM_TYPE_ID.PACKAGE,
    icon: ItemIcon(ITEM_TYPE_ID.PACKAGE, {
      color: 'text.secondary',
      backgroundColor: 'transparent',
    }),
  },
  {
    label: ITEM_TYPE_DESC[ITEM_TYPE_ID.SERVICE],
    value: ITEM_TYPE_ID.SERVICE,
    icon: ItemIcon(ITEM_TYPE_ID.SERVICE, {
      color: 'text.secondary',
      backgroundColor: 'transparent',
    }),
  },
  {
    label: ITEM_TYPE_DESC[ITEM_TYPE_ID.PERSON],
    value: ITEM_TYPE_ID.PERSON,
    icon: ItemIcon(ITEM_TYPE_ID.PERSON, {
      color: 'text.secondary',
      backgroundColor: 'transparent',
    }),
  },
] satisfies Array<{
  label: (typeof ITEM_TYPE_DESC)[keyof typeof ITEM_TYPE_DESC]
  value: ITEM_TYPE_ID
  icon: React.ReactNode
}>

const Items = ({ control }: { control: Control<JobDetailsFormType> }) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  })

  const { field } = useController({
    control,
    name: 'formState',
  })

  const isJobStarted = field.value === FORM_STATE.STARTED

  const handleAppendItem = (value: ITEM_TYPE_ID) => {
    const newItem = createItemWithDefaults(value)
    append(newItem)
  }

  return (
    <JobSection>
      <JobSection.Header>
        <JobSection.Title>
          {ctIntl.formatMessage({ id: 'delivery.rightPanel.jobView.items' })}
        </JobSection.Title>
        {!isJobStarted && (
          <Menu
            menuItems={itemsMenu}
            onItemClick={(value) => handleAppendItem(value as ITEM_TYPE_ID)}
            header={ctIntl.formatMessage({ id: 'delivery.rightPanel.jobView.items' })}
          >
            <IconButton size="small">
              <AddIcon fontSize="small" />
            </IconButton>
          </Menu>
        )}
      </JobSection.Header>
      {fields.length === 0 ? (
        <Typography
          variant="body2"
          color="text.secondary"
        >
          {ctIntl.formatMessage({ id: 'No items added' })}
        </Typography>
      ) : (
        <Stack gap={2}>
          {fields.map((item, index) => (
            <Paper
              key={item.id}
              elevation={0}
              sx={{ p: 2, backgroundColor: 'rgba(245, 245, 245, 1)' }}
            >
              <Stack spacing={2}>
                {match(item.itemTypeId)
                  .with(ITEM_TYPE_ID.PACKAGE, () => (
                    <Package
                      index={index}
                      control={control}
                      deleteItem={() => remove(index)}
                    />
                  ))
                  .with(ITEM_TYPE_ID.SERVICE, () => (
                    <Service
                      index={index}
                      control={control}
                      deleteItem={() => remove(index)}
                    />
                  ))
                  .with(ITEM_TYPE_ID.PERSON, () => (
                    <Person
                      index={index}
                      control={control}
                      deleteItem={() => remove(index)}
                    />
                  ))
                  .otherwise(() => null)}
              </Stack>
            </Paper>
          ))}
        </Stack>
      )}
    </JobSection>
  )
}

export default Items
