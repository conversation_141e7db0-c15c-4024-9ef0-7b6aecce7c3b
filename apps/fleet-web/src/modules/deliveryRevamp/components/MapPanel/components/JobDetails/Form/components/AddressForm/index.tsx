import { useMemo } from 'react'
import {
  Alert,
  Autocomplete,
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  OverflowTypography,
  Paper,
  Popper,
  Stack,
  styled,
  TextField,
  Tooltip,
  tooltipClasses,
  Typography,
  type TooltipProps,
} from '@karoo-ui/core'
import { TextFieldControlled, type UseControlledFormReturn } from '@karoo-ui/core-rhf'
import CloseIcon from '@mui/icons-material/Close'
import LocationOn from '@mui/icons-material/LocationOn'
import SearchIcon from '@mui/icons-material/Search'
import { Controller, useController, useWatch } from 'react-hook-form'
import {
  formatPhoneNumberIntl,
  getCountryCallingCode,
  type Country,
} from 'react-phone-number-input'
import { pathOr } from 'remeda'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import SearchPlaces from 'src/modules/deliveryRevamp/components/ReactHookFormComponents/SearchPlaces'
import { COUNTRIES } from 'src/modules/deliveryRevamp/constants/countries'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { getFormattedAddress } from 'src/modules/deliveryRevamp/utils'
import {
  reverseGeocoding,
  type AddressDetail,
} from 'src/modules/deliveryRevamp/utils/google-maps-api'
import PhoneNumberInput from 'src/shared/react-hook-form/PhoneNumberInput'
import { ctIntl } from 'src/util-components/ctIntl'

import { useJobDetailsContext } from '../../../contexts/JobDetailsContext'
import type { JobDetailsFormType, StopsFieldPath } from '../../schema'
import { handleAddressChange } from '../../utils'
import GPSInput from '../GPSInput'

type Props = {
  form: UseControlledFormReturn<JobDetailsFormType>
  baseFieldName: StopsFieldPath
  anchorEl: HTMLElement | null
  mode: 'EDIT' | 'ADD'
  onOpenAddressForm: (mode: 'EDIT' | 'ADD') => void
  onClose: () => void
}

function AddressForm({
  form,
  baseFieldName,
  anchorEl,
  mode,
  onOpenAddressForm,
  onClose,
}: Props) {
  const {
    data: { isMobile },
  } = useDeliveryMainPageContext()

  const { mapCenter } = useJobDetailsContext()

  const addressFieldName: `${StopsFieldPath}.address` = `${baseFieldName}.address`

  const customerField = useWatch({
    control: form.control,
    name: `${baseFieldName}.customer`,
  })

  const watchAddressValues = useWatch({
    control: form.control,
    name: addressFieldName,
  })

  const { field, fieldState } = useController({
    control: form.control,
    name: addressFieldName,
  })

  const addressValues = useMemo(
    () => watchAddressValues ?? field.value,
    [watchAddressValues, field.value],
  )

  const { countryOptions, countryOptionsByValue } = useMemo(() => {
    const countryOptions: Array<{ label: string; value: string }> = []
    const countryOptionsByValue: Record<string, { label: string; value: string }> = {}

    for (const country of COUNTRIES) {
      const option = {
        label: country.name,
        value: country.country_id,
      }

      countryOptions.push(option)
      countryOptionsByValue[country.country_id] = option
    }

    return { countryOptions, countryOptionsByValue }
  }, [])

  const formattedPhoneNumber = useMemo(() => {
    if (addressValues === undefined) {
      return ''
    }

    if (addressValues.phone.number === null) {
      return ''
    }

    return formatPhoneNumberIntl(
      `+${getCountryCallingCode(addressValues.phone.countryCode as Country)}${
        addressValues.phone.number
      }`,
    )
  }, [addressValues])

  const formattedAddress = useMemo(() => {
    if (addressValues === undefined) {
      return ''
    }

    return getFormattedAddress({
      ...addressValues,
      country: countryOptionsByValue[addressValues.countryId]?.label ?? '',
    })
  }, [countryOptionsByValue, addressValues])

  const formattedAddressDetails = useMemo(
    () =>
      formattedPhoneNumber && (addressValues?.email ?? '')
        ? `${formattedPhoneNumber} • ${addressValues?.email ?? ''}`
        : formattedPhoneNumber || (addressValues?.email ?? '') || '',
    [addressValues, formattedPhoneNumber],
  )

  const closeAddressPopper = () => {
    onClose()
  }

  const open = Boolean(anchorEl)

  const hasDirtyFields = useMemo(() => {
    const { dirtyFields } = form.formState
    const customerField = pathOr(
      dirtyFields,
      [...baseFieldName.split('.'), 'customer'] as any,
      false,
    )
    const addressField = pathOr(
      dirtyFields,
      [...baseFieldName.split('.'), 'address'] as any,
      false,
    )
    return Boolean(customerField || addressField)
  }, [form.formState, baseFieldName])

  const shouldDisplayAddressBox =
    formattedAddress ||
    formattedAddressDetails ||
    (hasDirtyFields && customerField?.name)
  const shouldDisplayError =
    fieldState.invalid || (hasDirtyFields && customerField?.name && !formattedAddress)

  return (
    <>
      <Box
        display="grid"
        sx={{
          ...(!shouldDisplayAddressBox && {
            display: 'none',
          }),
          p: 1,
          ml: 5,
          borderRadius: 0.5,
          '&:hover': {
            cursor: 'pointer',
            backgroundColor: (theme) => theme.palette.action.selected,
          },
          ...(addressValues.phone.number === null && {
            backgroundColor: '#fff7ed',
          }),
          ...(shouldDisplayError && {
            backgroundColor: '#FFCDD2',
          }),
        }}
        onClick={() => {
          onOpenAddressForm('EDIT')
        }}
      >
        <CustomTooltip
          title={(() => {
            if (shouldDisplayError) {
              return ctIntl.formatMessage({
                id: 'Click here to provide a valid address',
              })
            } else if (addressValues.phone.number === null) {
              return <PhoneNumberWarning />
            } else {
              return ''
            }
          })()}
          {...(!shouldDisplayError &&
            addressValues.phone.number === null && {
              sx: {
                [`& .${tooltipClasses.tooltip}`]: {
                  padding: 0,
                },
              },
            })}
        >
          <Stack overflow="hidden">
            <Typography
              variant="caption"
              color={shouldDisplayError ? 'error.main' : 'text.primary'}
            >
              {formattedAddress ||
                ctIntl.formatMessage({
                  id: 'Click here to provide a valid address',
                })}
            </Typography>

            <OverflowTypography
              typographyProps={{
                variant: 'caption',
                color: shouldDisplayError ? 'error.main' : 'text.secondary',
                fontSize: '12px',
              }}
            >
              {formattedAddressDetails}
            </OverflowTypography>
          </Stack>
        </CustomTooltip>
      </Box>

      {open && (
        <Box
          onClick={closeAddressPopper}
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: (theme) => theme.zIndex.modal,
          }}
        />
      )}
      <Popper
        open={open}
        anchorEl={anchorEl}
        {...(!isMobile && { placement: 'left' })}
        sx={{
          zIndex: (theme) => theme.zIndex.modal + 1,
        }}
        modifiers={[
          ...(isMobile
            ? [
                {
                  name: 'preventOverflow',
                  enabled: true,
                  options: {
                    altAxis: true,
                  },
                },
              ]
            : []),
          {
            name: 'offset',
            options: {
              offset: [0, 8],
            },
          },
        ]}
      >
        <Paper
          sx={{ width: 313, p: 2 }}
          tabIndex={-1}
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="subtitle2">
              {mode === 'EDIT'
                ? ctIntl.formatMessage({ id: 'Edit Address' })
                : ctIntl.formatMessage({ id: 'delivery.addressBook.addNewRecord' })}
            </Typography>
            <IconButton
              size="small"
              onClick={closeAddressPopper}
              disableRipple
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Stack>
          <Stack spacing={1.5}>
            <SearchPlaces
              name={`${addressFieldName}.addressLine1`}
              control={form.control}
              label={ctIntl.formatMessage({ id: 'Search Address' })}
              onSelectAddress={(address) => {
                if (address.latitude !== '' && address.longitude !== '') {
                  handleAddressChange({ form, name: addressFieldName, address })
                }
              }}
              textFieldProps={{
                slotProps: {
                  input: {
                    startAdornment: (
                      <InputAdornment
                        position="start"
                        sx={{ m: 0 }}
                      >
                        <IconButton size="small">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                    ...(!(
                      watchAddressValues.latitude && watchAddressValues.longitude
                    ) &&
                      mapCenter && {
                        endAdornment: (
                          <InputAdornment
                            position="end"
                            sx={{ m: 0 }}
                          >
                            <Tooltip
                              title={ctIntl.formatMessage({
                                id: 'Add marker on the map',
                              })}
                            >
                              <IconButton
                                size="small"
                                onClick={async () => {
                                  const address = (await reverseGeocoding(
                                    mapCenter.lat,
                                    mapCenter.lng,
                                  )) as AddressDetail
                                  if (address) {
                                    handleAddressChange({
                                      form,
                                      name: addressFieldName,
                                      address,
                                    })
                                  }
                                }}
                              >
                                <LocationOn />
                              </IconButton>
                            </Tooltip>
                          </InputAdornment>
                        ),
                      }),
                  },
                },
              }}
              required
            />

            <GPSInput
              name={`${addressFieldName}.latLng`}
              control={form.control}
              preLatLng={addressValues.latLng}
              onCoordinatesConfirm={(address) => {
                if (address) {
                  handleAddressChange({ form, name: addressFieldName, address })
                }
              }}
              required
            />

            <TextFieldControlled
              ControllerProps={{
                control: form.control,
                name: `${baseFieldName}.customer`,
              }}
              value={customerField?.name ?? ''}
              label={ctIntl.formatMessage({ id: 'Customer Name' })}
              onChange={(e, { controller }) => {
                controller.onChange({
                  id: customerField?.id ?? '',
                  name: e.target.value,
                })
              }}
              fullWidth
              required
            />

            <TextFieldControlled
              ControllerProps={{
                control: form.control,
                name: `${addressFieldName}.addressLine1`,
              }}
              label={ctIntl.formatMessage({ id: 'Address Line 1' })}
              fullWidth
            />

            <TextFieldControlled
              ControllerProps={{
                control: form.control,
                name: `${addressFieldName}.addressLine2`,
              }}
              label={ctIntl.formatMessage({ id: 'Address Line 2' })}
              fullWidth
            />

            <Grid
              container
              spacing={1.5}
            >
              <Grid size={5}>
                <TextFieldControlled
                  ControllerProps={{
                    control: form.control,
                    name: `${addressFieldName}.postalCode`,
                  }}
                  label={ctIntl.formatMessage({ id: 'Postal Code' })}
                  fullWidth
                />
              </Grid>
              <Grid size={7}>
                <Controller
                  control={form.control}
                  name={`${addressFieldName}.countryId`}
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      {...getAutocompleteVirtualizedProps({
                        options: countryOptions,
                      })}
                      value={countryOptionsByValue[field.value] || null}
                      onChange={(_e, newOption) => {
                        if (newOption) {
                          field.onChange(newOption.value)
                        }
                      }}
                      disablePortal
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          required
                          label={ctIntl.formatMessage({ id: 'Country' })}
                          helperText={ctIntl.formatMessage({
                            id: fieldState.error?.message ?? '',
                          })}
                          error={!!fieldState.error}
                        />
                      )}
                      size="small"
                    />
                  )}
                />
              </Grid>
            </Grid>

            <Stack>
              <PhoneNumberInput
                ControllerProps={{
                  control: form.control,
                  name: `${addressFieldName}.phone`,
                }}
                label={ctIntl.formatMessage({ id: 'editUser.cellPhone.label' })}
                required
              />

              {addressValues.phone.number === null && <PhoneNumberWarning />}
            </Stack>

            <TextFieldControlled
              ControllerProps={{
                control: form.control,
                name: `${addressFieldName}.email`,
              }}
              label={ctIntl.formatMessage({ id: 'Email' })}
              fullWidth
            />

            <Controller
              control={form.control}
              name={`${addressFieldName}.saveToAddressBook`}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      size="small"
                      checked={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.checked)
                      }}
                      sx={{ pl: 0, pr: 1 }}
                    />
                  }
                  label={
                    <Typography variant="body2">
                      {ctIntl.formatMessage({ id: 'deliver.addressBook.save' })}
                    </Typography>
                  }
                />
              )}
            />
          </Stack>
        </Paper>
      </Popper>
    </>
  )
}

export default AddressForm

const PhoneNumberWarning = () => (
  <Alert severity="warning">
    <span style={{ fontWeight: '500' }}>
      {ctIntl.formatMessage({ id: 'Without phone number added:' })}
    </span>
    <ul style={{ paddingLeft: '1rem', margin: 0 }}>
      <li>
        {ctIntl.formatMessage({
          id: "The customer won't receive tracking notifications via SMS",
        })}
      </li>
      <li>
        {ctIntl.formatMessage({
          id: "Driver can't contact the customer during delivery",
        })}
      </li>
    </ul>
  </Alert>
)

const CustomTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip
    {...props}
    classes={{ popper: className }}
    disableFocusListener
    arrow={false}
  />
))(() => ({}))
