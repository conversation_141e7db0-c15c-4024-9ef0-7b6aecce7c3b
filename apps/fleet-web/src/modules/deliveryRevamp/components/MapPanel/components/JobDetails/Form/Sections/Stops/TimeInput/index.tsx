import { useEffect, useMemo, useRef, useState } from 'react'
import { isNil } from 'lodash'
import {
  Button,
  MenuItem,
  pickersLayoutClasses,
  Select,
  Stack,
  TimePicker,
  Typography,
  type TimePickerProps,
} from '@karoo-ui/core'
import type { UseControlledFormReturn } from '@karoo-ui/core-rhf'
import AccessTimeIcon from '@mui/icons-material/AccessTimeOutlined'
import type { DateTime } from 'luxon'
import { Controller, useController, useWatch } from 'react-hook-form'
import { match } from 'ts-pattern'

import { JOB_FORM_SOURCES } from 'src/modules/deliveryRevamp/constants'
import { STOP_TYPE_ID } from 'src/modules/deliveryRevamp/constants/job'
import { useOnboardingActionsTooltip } from 'src/modules/deliveryRevamp/contexts/OnboardingTooltipContext'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import OnBoardingTriggerEvents from 'src/modules/deliveryRevamp/utils/onBoarding/onBoardingTriggerEvents'
import { DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS } from 'src/modules/deliveryRevamp/utils/onBoarding/useOnBoardingState'
import { ctIntl } from 'src/util-components/ctIntl'

import JobFieldContainer from '../../../../components/JobFieldContainer'
import type { JobDetailsFormType } from '../../../schema'
import { getStopsBaseFieldName } from '../../../utils'

enum ArriveAtOrBetween {
  ARRIVE_AT = 'arriveAt',
  ARRIVE_BETWEEN = 'arriveBetween',
}

type Props = {
  type: STOP_TYPE_ID
  form: UseControlledFormReturn<JobDetailsFormType>
  formSource: JOB_FORM_SOURCES
}
const TimeInput = ({ type, form, formSource }: Props) => {
  const elementRef = useRef<HTMLDivElement>(null)
  const { removeTooltip } = useOnboardingActionsTooltip()

  const arriveAtFieldName = `${getStopsBaseFieldName(type)}.arriveAt` as const
  const arriveBetweenFieldName = `${getStopsBaseFieldName(type)}.arriveBetween` as const

  const scheduledDateField = useWatch({
    control: form.control,
    name: 'scheduledDate',
  })
  const arriveBetweenValue = useWatch({
    control: form.control,
    name: arriveBetweenFieldName,
  })

  const arriveBetweenField = useController({
    control: form.control,
    name: arriveBetweenFieldName,
  })
  const arriveAtField = useController({
    control: form.control,
    name: arriveAtFieldName,
  })

  const [isArriveAtOrBetween, setIsArriveAtOrBetween] = useState<ArriveAtOrBetween>(
    arriveBetweenValue ? ArriveAtOrBetween.ARRIVE_BETWEEN : ArriveAtOrBetween.ARRIVE_AT,
  )

  const minTime = useMemo(() => {
    // If scheduled date is for today, min time must be now
    if (
      scheduledDateField !== null &&
      DeliveryDateTime.fromJSDate(scheduledDateField).hasSame(
        DeliveryDateTime.now(),
        'day',
      )
    ) {
      return DeliveryDateTime.now()
    }

    return DeliveryDateTime.fromObject({ hour: 0, minute: 0 })
  }, [scheduledDateField])

  // Watch for changes in the optimize button's disabled state
  useEffect(
    () => () => {
      removeTooltip(
        DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS.jobFormArrivalTimeField,
      )
    },
    [removeTooltip],
  )

  return (
    <>
      <JobFieldContainer
        id={
          type === STOP_TYPE_ID.PICKUP || type === STOP_TYPE_ID.SINGLE
            ? `${DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS.jobFormArrivalTimeField}-${type}`
            : undefined
        }
        ref={elementRef}
      >
        <JobFieldContainer.Icon>
          <AccessTimeIcon />
        </JobFieldContainer.Icon>
        <Stack gap={1}>
          <Stack
            sx={{
              flexDirection: 'row',
              backgroundColor: 'common.white',
              borderRadius: 1,
              border: '1px solid',
              py: 0.5,
              pl: 1.5,
              gap: 0.5,
              alignItems: 'center',
              borderColor:
                arriveBetweenField.fieldState.error?.message ||
                arriveAtField.fieldState.error?.message
                  ? 'error.main'
                  : 'rgba(0, 0, 0, 0.23)',
            }}
          >
            {isArriveAtOrBetween === ArriveAtOrBetween.ARRIVE_AT ? (
              <Controller
                name={arriveAtFieldName}
                control={form.control}
                render={({ field }) => (
                  <StyledTimePicker
                    value={field.value}
                    defaultValue={minTime.toJSDate()}
                    minTime={minTime}
                    onChange={(value) => {
                      form.setValue(arriveAtFieldName, value)
                    }}
                  />
                )}
              />
            ) : (
              <Stack
                sx={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 0.5,
                }}
              >
                <Controller
                  name={arriveBetweenFieldName}
                  control={form.control}
                  render={({ field }) => (
                    <>
                      <StyledTimePicker
                        value={field.value?.timeFrom ?? null}
                        defaultValue={minTime.toJSDate()}
                        minTime={minTime}
                        hideActionBar
                        onChange={(value) => {
                          let newTimeToValue = field.value?.timeTo ?? null

                          if (
                            value !== null && //Check if value is greater than timeFrom
                            (isNil(field.value?.timeFrom) ||
                              value > field.value?.timeFrom)
                          ) {
                            // add 5 minutes to end
                            newTimeToValue = DeliveryDateTime.fromJSDate(value)
                              .plus({ minutes: 5 })
                              .toJSDate()
                          }
                          form.setValue(arriveBetweenFieldName, {
                            timeFrom: value,
                            timeTo: newTimeToValue,
                          })
                        }}
                      />
                      <Dash />
                      <StyledTimePicker
                        minTime={
                          field.value?.timeFrom
                            ? DeliveryDateTime.fromJSDate(field.value?.timeFrom)
                            : minTime
                        }
                        hideActionBar
                        defaultValue={
                          field.value?.timeFrom
                            ? field.value?.timeFrom
                            : minTime.toJSDate()
                        }
                        value={field.value?.timeTo ?? null}
                        onChange={(value) => {
                          form.setValue(arriveBetweenFieldName, {
                            timeFrom: field.value?.timeFrom ?? null,
                            timeTo: value,
                          })
                        }}
                      />
                    </>
                  )}
                />
              </Stack>
            )}
            <Select
              sx={{
                '& .MuiSelect-select': {
                  color: 'info.main',
                  fontSize: '0.875rem',
                  py: 0.5,
                  px: 0,
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none !important',
                },
                '& .Mui-focused': {
                  border: 'none !important',
                },
                '& .MuiSvgIcon-root': {
                  color: 'info.main',
                },
              }}
              value={isArriveAtOrBetween}
              onChange={(e) => {
                setIsArriveAtOrBetween(e.target.value as ArriveAtOrBetween)
                match(e.target.value as ArriveAtOrBetween)
                  .with(ArriveAtOrBetween.ARRIVE_AT, () => {
                    form.setValue(arriveBetweenFieldName, null)
                  })
                  .with(ArriveAtOrBetween.ARRIVE_BETWEEN, () => {
                    form.setValue(arriveAtFieldName, null)
                  })
                  .otherwise(() => null)
              }}
            >
              <MenuItem value={ArriveAtOrBetween.ARRIVE_AT}>
                {ctIntl.formatMessage({ id: 'Arrive at' })}
              </MenuItem>
              <MenuItem value={ArriveAtOrBetween.ARRIVE_BETWEEN}>
                {ctIntl.formatMessage({ id: 'Arrive between' })}
              </MenuItem>
            </Select>
          </Stack>
          <Typography
            variant="caption"
            color="error"
            px={2}
          >
            {arriveBetweenField.fieldState.error?.message ||
              arriveAtField.fieldState.error?.message}
          </Typography>
        </Stack>
      </JobFieldContainer>
      {formSource === JOB_FORM_SOURCES.MAIN_FORM && (
        <OnBoardingTriggerEvents
          eventType={
            DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS.jobFormArrivalTimeField
          }
          instanceId={`${DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS.jobFormArrivalTimeField}-${type}`}
          onTrigger={(event) => {
            if (
              event.id ===
              DELIVERY_ONETIME_TRIGGER_ONBOARDING_ELEMENT_IDS.jobFormArrivalTimeField
            ) {
              return event.triggerEvent(elementRef)
            }
            return false
          }}
        />
      )}
    </>
  )
}

const Dash = () => <Typography variant="body1"> - </Typography>

const StyledTimePicker = ({
  value,
  defaultValue,
  hideActionBar = false,
  onChange,
  minTime,
}: {
  value: Date | null
  defaultValue?: Date | null
  hideActionBar?: boolean
  onChange: (newValue: Date | null) => void
  minTime: TimePickerProps<DateTime>['minTime']
}) => {
  const [isTimePickerOpen, setIsTimePickerOpen] = useState(false)

  return (
    <TimePicker
      minTime={minTime}
      open={isTimePickerOpen}
      onOpen={() => setIsTimePickerOpen(true)}
      onClose={() => setIsTimePickerOpen(false)}
      ampm={false}
      ampmInClock={false}
      slots={{
        ...(!hideActionBar && {
          actionBar: () => (
            <Button
              variant="text"
              onClick={() => {
                onChange(null)
                setIsTimePickerOpen(false)
              }}
            >
              {ctIntl.formatMessage({
                id: 'delivery.jobForm.timeInput.anytimeDuringTheDay',
              })}
            </Button>
          ),
        }),
      }}
      slotProps={{
        actionBar: { actions: [] },
        textField: {
          fullWidth: true,
          InputProps: {
            value: value
              ? DeliveryDateTime.fromJSDate(value).toFormat('HH:mm')
              : ctIntl.formatMessage({ id: 'Anytime' }),
            size: 'small',
            endAdornment: <></>,
          },
          sx: {
            '& .MuiOutlinedInput': {
              '&-root': {
                fontSize: '0.9rem',
              },

              '&-notchedOutline': {
                padding: 0,
                border: 'none !important',
                outline: 'none !important',
              },
            },
            '& .MuiInputBase-root': {
              padding: 0,
              backgroundColor: 'transparent',
              '& input': {
                flex: 1,
                cursor: 'pointer',
                padding: 0,
              },
            },
          },
          onClick: () => setIsTimePickerOpen(true),
        },
        layout: {
          sx: {
            [`.${pickersLayoutClasses.contentWrapper}`]: {
              gridColumn: 1,
              gridRow: 1,

              '& .MuiMultiSectionDigitalClock-root': {
                justifyContent: 'center',
              },
            },
          },
        },
      }}
      value={value ? DeliveryDateTime.fromJSDate(value) : null}
      defaultValue={defaultValue ? DeliveryDateTime.fromJSDate(defaultValue) : null}
      onChange={(newValue) => {
        onChange(newValue && newValue.isValid ? newValue.toJSDate() : null)
        // Don't close the picker here
      }}
      onAccept={(newValue) => {
        onChange(newValue && newValue.isValid ? newValue.toJSDate() : null)
        setIsTimePickerOpen(false)
      }}
    />
  )
}

export default TimeInput
