import { useCallback, useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Box,
  Chip,
  FormControl,
  IconButton,
  ListItem,
  ListItemIcon,
  MenuItem,
  OverflowTypography,
  Select,
  Stack,
  styled,
  TextField,
  Typography,
  type ChipProps,
  type TypographyProps,
} from '@karoo-ui/core'
import DeleteIcon from '@mui/icons-material/DeleteOutlined'
import DragIndicatorOutlinedIcon from '@mui/icons-material/DragIndicatorOutlined'
import PopupState, { bindHover } from 'material-ui-popup-state'
import { Draggable, type DraggableProvided } from 'react-beautiful-dnd'
import {
  Controller,
  useController,
  type Control,
  type FieldArrayPath,
} from 'react-hook-form'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import type {
  FetchDeliveryJobDetails,
  TodoImageDetails,
} from 'src/modules/deliveryRevamp/api/jobs/useDeliveryJobDetails'
import { getJobDetails } from 'src/modules/deliveryRevamp/components/MapPanel/slice'
import {
  FORM_STATE,
  ITEM_TYPE_ID,
  JOB_TYPE_ID,
  TODO_STATUS_ID,
  TODO_TO_APPLY_TO_TYPE_ID,
  TODO_TYPE_DESC,
  TODO_TYPE_ID,
  todoStatusTranslationKey,
} from 'src/modules/deliveryRevamp/constants/job'
import { useTypedSelector } from 'src/redux-hooks'
import type { ExcludeStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import type { JobDetailsFormType } from '../../schema'
import { AssetBox, PopoverAssets, PopoverText, ToDoIcon } from './utils'

type Assets =
  | FetchDeliveryJobDetails.Return[number]['additionalData']['toDoData'][number]
  | undefined

type TodoState =
  FetchDeliveryJobDetails.Return[number]['additionalData']['toDoData'][number]['todoStatus'][TODO_TO_APPLY_TO_TYPE_ID]

const AssetPopover = ({
  asset,
  assets,
  description,
  showAllAssets,
  possibleAssetsId,
  todoTypeId,
}: {
  description: string
  showAllAssets: boolean
  asset: TodoImageDetails
  assets: Array<TodoImageDetails>
  possibleAssetsId: string
  todoTypeId: TODO_TYPE_ID
}) => {
  const possibleDate = asset.date ? new Date(asset.date) : null

  return (
    <PopupState
      key={asset.url}
      variant="popover"
      popupId="assetPopover"
    >
      {(popupState) => (
        <Box sx={{ pointerEvents: 'all' }}>
          <AssetBox
            {...bindHover(popupState)}
            assetUrl={asset.url}
          />
          <PopoverAssets
            popupState={popupState}
            asset={asset}
            assets={assets}
            description={description}
            showAllAssets={showAllAssets}
            date={possibleDate}
            coords={asset.coords}
            possibleAssetsId={possibleAssetsId}
            todoTypeId={todoTypeId}
          />
        </Box>
      )}
    </PopupState>
  )
}

const chipLabel = ({
  todoState,
  provided,
  message,
  todoTypeId,
  noteMessage,
}: {
  todoState: TodoState
  provided: DraggableProvided
  message: string
  todoTypeId: TODO_TYPE_ID
  noteMessage?: string | null
}) => {
  if (!todoState) return null
  const chipCommonProps: ChipProps = {
    size: 'small',
    sx: {
      height: '18px',
      '& .MuiChip-label': {
        fontSize: '12px',
      },
      pointerEvents: 'none',
    },
    variant: 'outlined',
  }

  const possibleReason =
    todoState &&
    todoStatusTranslationKey[todoState.status as keyof typeof todoStatusTranslationKey]

  const possibleDate = todoState.date ? new Date(todoState.date) : null

  return match({ status: todoState.status, typeId: todoTypeId })
    .with({ status: TODO_STATUS_ID.COMPLETED, typeId: TODO_TYPE_ID.NOTE }, () => {
      if (!noteMessage || noteMessage === '') {
        return (
          <PopupState
            key={todoState.status}
            variant="popover"
            popupId="chipErrorPopover"
          >
            {(popupState) => (
              <Box sx={{ pointerEvents: 'all' }}>
                <span {...bindHover(popupState)}>
                  <Chip
                    {...chipCommonProps}
                    label={ctIntl.formatMessage({ id: 'None' })}
                    color="error"
                    {...provided.dragHandleProps}
                  />
                </span>
                <PopoverText
                  title={message}
                  message={ctIntl.formatMessage({ id: 'None' })}
                  date={possibleDate}
                  coords={todoState.coords}
                  popupState={popupState}
                />
              </Box>
            )}
          </PopupState>
        )
      }
      return (
        <Chip
          {...chipCommonProps}
          label={ctIntl.formatMessage({ id: 'Completed' })}
          color="success"
          {...provided.dragHandleProps}
        />
      )
    })
    .with({ status: TODO_STATUS_ID.COMPLETED }, () => (
      <Chip
        {...chipCommonProps}
        label={ctIntl.formatMessage({ id: 'Completed' })}
        color="success"
        {...provided.dragHandleProps}
      />
    ))
    .with(
      { status: TODO_STATUS_ID.CUSTOMER_NOT_SHOW },
      { status: TODO_STATUS_ID.REFUSE_TO_SIGN },
      { status: TODO_STATUS_ID.SIGNATURE_OTHERS },
      { status: TODO_STATUS_ID.NO_RESPONSE },
      { status: TODO_STATUS_ID.NO_PERSON_AT_HOME },
      { status: TODO_STATUS_ID.PHOTO_OTHERS },
      { status: TODO_STATUS_ID.TECHNICAL_ISSUES },
      { status: TODO_STATUS_ID.ATTACH_CODE_OTHERS },
      () => (
        <PopupState
          key={todoState.status}
          variant="popover"
          popupId="chipErrorPopover"
        >
          {(popupState) => (
            <Box sx={{ pointerEvents: 'all' }}>
              <span {...bindHover(popupState)}>
                <Chip
                  {...chipCommonProps}
                  label={ctIntl.formatMessage({
                    id: possibleReason ?? 'Others',
                  })}
                  color="error"
                  {...provided.dragHandleProps}
                />
              </span>
              <PopoverText
                title={message}
                message={
                  todoState.message && todoState.message !== ''
                    ? todoState.message
                    : ctIntl.formatMessage({
                        id: possibleReason ?? 'Others',
                      })
                }
                date={possibleDate}
                coords={todoState.coords}
                popupState={popupState}
              />
            </Box>
          )}
        </PopupState>
      ),
    )
    .otherwise(() => (
      <Chip
        {...chipCommonProps}
        label={ctIntl.formatMessage({ id: 'Incomplete' })}
        {...provided.dragHandleProps}
      />
    ))
}

const ToDoItem = ({
  control,
  formPathName,
  todoIndex,
  itemId,
  itemTypeId,
  removeEntry,
  displayApplyOptions,
  jobTypeId,
  formState,
  isTodoFromStop,
}: {
  control: Control<JobDetailsFormType>
  formPathName: ExcludeStrict<
    FieldArrayPath<JobDetailsFormType>,
    'items' | 'appointmentFields' | 'appointmentFiles'
  >
  todoIndex: number
  itemId: string
  itemTypeId: ITEM_TYPE_ID | undefined
  removeEntry: () => void
  displayApplyOptions: boolean
  jobTypeId: JOB_TYPE_ID
  formState: FORM_STATE
  isTodoFromStop?: boolean
}) => {
  const jobDetails = useTypedSelector(getJobDetails)
  const path = `${formPathName}.${todoIndex}` as const

  const [isEditing, setIsEditing] = useState(false)
  const { field: todoField } = useController({
    control,
    name: path,
  })

  const possibleTodoItems = useMemo(() => {
    if (jobDetails && todoField.value.id) {
      return jobDetails.additionalData.toDoData[todoField.value.id]
    }
    return undefined
  }, [jobDetails, todoField.value.id])

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    todoField.onChange({ ...todoField.value, description: event.target.value })
  }

  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' || event.key === 'Escape') {
      setIsEditing(false)
    }
  }

  const getTodoInfo = useCallback(
    (
      id: string | undefined,
    ): {
      isReadOnly: boolean
    } & (
      | {
          pickup: {
            todoState: TodoState
          }
          dropoff: {
            todoState: TodoState
          }
        }
      | {
          single: {
            todoState: TodoState
          }
        }
    ) => {
      const todoData = id ? jobDetails?.additionalData.toDoData[id] : undefined
      // For testing, should use the api values
      if (jobTypeId === JOB_TYPE_ID.PD && !isTodoFromStop) {
        return {
          isReadOnly: formState === FORM_STATE.STARTED,
          pickup: {
            todoState:
              todoData?.todoStatus[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP],
          },
          dropoff: {
            todoState:
              todoData?.todoStatus[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP],
          },
        }
      }
      return {
        isReadOnly: formState === FORM_STATE.STARTED,
        single: {
          todoState: todoData?.todoStatus[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP],
        },
      }
    },
    [formState, isTodoFromStop, jobDetails?.additionalData.toDoData, jobTypeId],
  )

  const todoInfo = useMemo(
    () => getTodoInfo(todoField.value.id),
    [todoField.value.id, getTodoInfo],
  )

  return (
    <Draggable
      draggableId={itemId}
      index={todoIndex}
    >
      {(provided, snapshot) => (
        <StyledListItem
          ref={provided.innerRef}
          {...provided.draggableProps}
          isDragging={snapshot.isDragging}
        >
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{ alignSelf: 'stretch', overflow: 'hidden' }}
          >
            <Stack
              direction="row"
              alignItems="center"
              sx={{ overflow: 'hidden' }}
            >
              <ListItemIcon sx={{ minWidth: '28px' }}>
                {ToDoIcon(todoField.value.todoTypeId, {
                  color: 'text.secondary',
                  backgroundColor: 'transparent',
                })}
              </ListItemIcon>
              {isEditing ? (
                <TextField
                  value={todoField.value.description}
                  onChange={handleInputChange}
                  onKeyDown={handleInputKeyDown}
                  onBlur={() => setIsEditing(false)}
                  autoFocus
                  fullWidth
                  sx={{ flex: 1 }}
                />
              ) : (
                <Stack sx={{ overflow: 'hidden' }}>
                  <OverflowTypography
                    typographyProps={{
                      variant: 'body2',
                      onDoubleClick: () => !todoInfo.isReadOnly && setIsEditing(true),
                      sx: {
                        cursor: todoInfo.isReadOnly ? 'default' : 'pointer',
                        flex: 1,
                      },
                    }}
                  >
                    {ctIntl.formatMessage({
                      id:
                        todoField.value.description ||
                        TODO_TYPE_DESC[todoField.value.todoTypeId],
                    })}
                  </OverflowTypography>
                </Stack>
              )}
            </Stack>

            {todoInfo.isReadOnly &&
              'single' in todoInfo &&
              chipLabel({
                todoState: todoInfo.single.todoState,
                provided,
                message:
                  todoField.value.description ||
                  TODO_TYPE_DESC[todoField.value.todoTypeId],
                todoTypeId: todoField.value.todoTypeId,
                noteMessage:
                  possibleTodoItems?.notes[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP],
              })}
            {!todoInfo.isReadOnly && (
              <Stack direction="row">
                <IconButton
                  size="small"
                  onClick={removeEntry}
                  sx={{
                    visibility: 'hidden',
                  }}
                >
                  <DeleteIcon
                    fontSize="small"
                    sx={{ color: 'error.main' }}
                  />
                </IconButton>
                <IconButton
                  size="small"
                  sx={{
                    visibility: 'hidden',
                  }}
                  {...provided.dragHandleProps}
                >
                  <DragIndicatorOutlinedIcon
                    fontSize="small"
                    sx={{ cursor: 'grab' }}
                  />
                </IconButton>
              </Stack>
            )}
          </Stack>

          {displayApplyOptions && (
            <Controller
              control={control}
              name={`${formPathName}.${todoIndex}.stopTypeId`}
              render={({ field }) => (
                <FormControl
                  size="small"
                  sx={{ minWidth: 0 }}
                >
                  <Select
                    sx={{
                      ml: '28px',
                      '& .MuiSelect-select': {
                        color: 'info.main',
                        fontSize: '0.875rem',
                        py: 0.5,
                        px: 0,
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none !important',
                      },
                      '& .Mui-focused': {
                        border: 'none !important',
                      },
                      '& .MuiSvgIcon-root': {
                        color: 'info.main',
                      },
                    }}
                    value={field.value}
                    onChange={(e) =>
                      field.onChange(e.target.value as TODO_TO_APPLY_TO_TYPE_ID)
                    }
                  >
                    <MenuItem value={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP}>
                      {ctIntl.formatMessage({ id: 'Required at Pick-up' })}
                    </MenuItem>
                    <MenuItem value={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP}>
                      {ctIntl.formatMessage({ id: 'Required at Deliver-to' })}
                    </MenuItem>
                    <MenuItem value={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP}>
                      {ctIntl.formatMessage({ id: 'Both stops' })}
                    </MenuItem>
                  </Select>
                </FormControl>
              )}
            />
          )}
          {todoInfo.isReadOnly && (
            <Stack
              width="100%"
              gap={1}
              sx={{
                pl: '28px',
              }}
            >
              {'pickup' in todoInfo && !R.isNullish(todoInfo.pickup.todoState) && (
                <Stack
                  direction="row"
                  justifyContent="space-between"
                >
                  <Typography variant="caption">
                    {ctIntl.formatMessage({ id: 'Pick up' })}
                  </Typography>
                  {chipLabel({
                    todoState: todoInfo.pickup.todoState,
                    provided,
                    message:
                      todoField.value.description ||
                      TODO_TYPE_DESC[todoField.value.todoTypeId],
                    todoTypeId: todoField.value.todoTypeId,
                    noteMessage:
                      possibleTodoItems?.notes[
                        TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
                      ],
                  })}
                </Stack>
              )}
              {possibleTodoItems?.notes[
                TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
              ] && (
                <NoteText variant="caption">
                  {
                    possibleTodoItems.notes[
                      TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
                    ]
                  }
                </NoteText>
              )}
              {todoField.value.todoTypeId === TODO_TYPE_ID.SCAN_TO_ATTACH &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
                ]?.code &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
                ].status === TODO_STATUS_ID.COMPLETED && (
                  <TrackingNumberText
                    code={
                      possibleTodoItems.todoStatus[
                        TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP
                      ].code
                    }
                    itemTypeId={itemTypeId}
                  />
                )}
              <AssetManager
                todoTypeId={todoField.value.todoTypeId}
                source={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_FIRST_STOP}
                possibleTodoItems={possibleTodoItems}
                description={
                  todoField.value.description ||
                  TODO_TYPE_DESC[todoField.value.todoTypeId]
                }
              />
              {'dropoff' in todoInfo && !R.isNullish(todoInfo.dropoff.todoState) && (
                <Stack
                  direction="row"
                  justifyContent="space-between"
                >
                  <Typography variant="caption">
                    {ctIntl.formatMessage({ id: 'Deliver to' })}
                  </Typography>
                  {chipLabel({
                    todoState: todoInfo.dropoff.todoState,
                    provided,
                    message:
                      todoField.value.description ||
                      TODO_TYPE_DESC[todoField.value.todoTypeId],
                    todoTypeId: todoField.value.todoTypeId,
                    noteMessage:
                      possibleTodoItems?.notes[
                        TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP
                      ],
                  })}
                </Stack>
              )}
              {possibleTodoItems?.notes[
                TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP
              ] && (
                <NoteText variant="caption">
                  {possibleTodoItems.notes[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP]}
                </NoteText>
              )}
              {todoField.value.todoTypeId === TODO_TYPE_ID.SCAN_TO_ATTACH &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP
                ]?.code &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP
                ].status === TODO_STATUS_ID.COMPLETED && (
                  <TrackingNumberText
                    code={
                      possibleTodoItems.todoStatus[
                        TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP
                      ].code
                    }
                    itemTypeId={itemTypeId}
                  />
                )}
              <AssetManager
                todoTypeId={todoField.value.todoTypeId}
                source={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_LAST_STOP}
                possibleTodoItems={possibleTodoItems}
                description={
                  todoField.value.description ||
                  TODO_TYPE_DESC[todoField.value.todoTypeId]
                }
              />
              {possibleTodoItems?.notes[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP] && (
                <NoteText variant="body2">
                  {possibleTodoItems.notes[TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP]}
                </NoteText>
              )}
              {todoField.value.todoTypeId === TODO_TYPE_ID.SCAN_TO_ATTACH &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP
                ]?.code &&
                possibleTodoItems?.todoStatus[
                  TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP
                ].status === TODO_STATUS_ID.COMPLETED && (
                  <TrackingNumberText
                    code={
                      possibleTodoItems.todoStatus[
                        TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP
                      ].code
                    }
                    itemTypeId={itemTypeId}
                  />
                )}
              <AssetManager
                todoTypeId={todoField.value.todoTypeId}
                source={TODO_TO_APPLY_TO_TYPE_ID.APPLY_TO_ALL_STOP}
                possibleTodoItems={possibleTodoItems}
                description={
                  todoField.value.description ||
                  TODO_TYPE_DESC[todoField.value.todoTypeId]
                }
              />
            </Stack>
          )}
        </StyledListItem>
      )}
    </Draggable>
  )
}

export default ToDoItem

const AssetManager = ({
  source,
  possibleTodoItems,
  description,
  todoTypeId,
}: {
  source: TODO_TO_APPLY_TO_TYPE_ID
  possibleTodoItems: Assets
  description: string
  todoTypeId: TODO_TYPE_ID
}) => {
  const assets = possibleTodoItems?.images[source]

  if (!assets) return null
  const assetsMetadata = possibleTodoItems?.todoStatus[source]
  const code = assetsMetadata?.code ?? assetsMetadata?.ordering
  const possibleAssetsId = assetsMetadata
    ? `${!isEmpty(code) ? code + '-' : ''}${assetsMetadata.todoId}`
    : 'assets'

  const maxAssetsToShow = 4
  const additionalAssetsCount = assets.length - maxAssetsToShow

  return (
    <AssetsWrapper>
      {assets.slice(0, maxAssetsToShow).map((asset) => (
        <AssetPopover
          asset={asset}
          assets={assets}
          key={asset.url}
          todoTypeId={todoTypeId}
          description={description}
          showAllAssets={assets.length > 1}
          possibleAssetsId={possibleAssetsId}
        />
      ))}
      {additionalAssetsCount > 0 && (
        <Typography
          variant="caption"
          sx={{ ml: 1 }}
        >
          +{additionalAssetsCount}
        </Typography>
      )}
    </AssetsWrapper>
  )
}

const StyledListItem = styled(ListItem, {
  shouldForwardProp: (prop) => prop !== 'isDragging',
})<{ isDragging: boolean }>(({ theme, isDragging }) =>
  theme.unstable_sx({
    backgroundColor: isDragging ? '#f6f6f6' : 'background.paper',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
      '& .MuiIconButton-root': {
        visibility: 'visible',
      },
    },
    borderRadius: 1,
    mb: 1,
    border: isDragging ? '1px solid #dddddd' : 'none',
    width: '100%',
    flexDirection: 'column',
    alignItems: 'baseline',
    p: 1.5,
  }),
)

const NoteText = (props: TypographyProps) => (
  <Typography
    variant="body2"
    {...props}
    sx={{
      color: 'text.secondary',
      ...props.sx,
    }}
  />
)

const TrackingNumberText = ({
  code,
  itemTypeId,
}: {
  code: string
  itemTypeId: ITEM_TYPE_ID | undefined
}) => (
  <Typography variant="caption">
    {`${ctIntl.formatMessage({
      id: itemTypeId === ITEM_TYPE_ID.PACKAGE ? 'Scan Tracking Code' : 'Scan Code',
    })}: ${code}`}
  </Typography>
)

const AssetsWrapper = styled(Stack)({
  flexDirection: 'row',
  gap: 0.5,
  flexWrap: 'wrap',
  alignItems: 'center',
})
