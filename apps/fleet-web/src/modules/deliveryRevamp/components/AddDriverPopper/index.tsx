import type React from 'react'
import { useC<PERSON>back, useMemo, useRef, useState } from 'react'
import {
  Box,
  Button,
  CircularProgressDelayedAbsolute,
  Divider,
  InputAdornment,
  ListItemButton,
  Stack,
  TextField,
  Typography,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import SearchIcon from '@mui/icons-material/Search'
import { Link, useHistory, useLocation } from 'react-router-dom'
import { match } from 'ts-pattern'
import { VList } from 'virtua'

import type { DriverId } from 'api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import Popover, {
  type PopoverHandles,
} from 'src/modules/deliveryRevamp/components/Popover'
import { driversModalParamsSchema } from 'src/modules/lists/Drivers/DriversModal'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  children: React.ReactNode
}

function FleetDriversList({ onClose }: { onClose?: () => void }) {
  const history = useHistory()
  const location = useLocation()

  const [search, setSearch] = useState('')

  const driversListQuery = useDriversQuery()

  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value)
  }, [])

  const handleClickDriver = useCallback(
    (driverId: DriverId) => {
      history.push(getDriverDetailsModalMainPath(location, driverId, 'DELIVERY', true))
    },
    [history, location],
  )

  const driversList = useMemo(
    () =>
      (driversListQuery.data?.activeDrivers ?? []).filter(
        (driver) =>
          !driver.deliveryDriver.isActive &&
          (search ? driver.name.toLowerCase().includes(search.toLowerCase()) : true),
      ),
    [driversListQuery.data, search],
  )

  return (
    <Stack
      sx={{
        height: '400px',
        width: '320px',
      }}
    >
      {match(driversListQuery)
        .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, ({ data: { activeDrivers } }) => (
          <Stack
            spacing={1}
            flex={1}
            sx={{ pt: 2, pb: 1 }}
          >
            <Typography
              variant="caption"
              sx={{ color: 'text.secondary', px: 2 }}
            >
              {ctIntl.formatMessage({ id: 'delivery.addDriver.popover.selectDriver' })}
            </Typography>
            {activeDrivers.length === 0 ? (
              <Typography
                variant="caption"
                sx={{ color: 'text.secondary', px: 2, pt: 2, flex: 1 }}
              >
                {ctIntl.formatMessage({
                  id: 'No drivers added yet. You can add a new driver.',
                })}
              </Typography>
            ) : (
              <>
                <TextField
                  onChange={handleSearch}
                  value={search}
                  placeholder={ctIntl.formatMessage({ id: 'Search Drivers' })}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    },
                  }}
                  sx={{ px: 2 }}
                />
                <Stack flex={1}>
                  {driversList.length === 0 && (
                    <Typography
                      variant="caption"
                      sx={{ color: 'text.secondary', px: 2, textAlign: 'center' }}
                    >
                      {ctIntl.formatMessage({
                        id: 'No data found',
                      })}
                    </Typography>
                  )}
                  <VList>
                    {driversList.map((driver) => (
                      <ListItemButton
                        key={driver.id}
                        onClick={() => handleClickDriver(driver.id)}
                      >
                        <Typography>{driver.name}</Typography>
                      </ListItemButton>
                    ))}
                  </VList>
                </Stack>
              </>
            )}
            <Divider />
            <Box sx={{ px: 2 }}>
              <Button
                variant="text"
                startIcon={<AddIcon />}
                component={Link}
                to={`${
                  location.pathname
                }?${buildRouteQueryStringKeepingExistingSearchParams({
                  location: location,
                  schema: driversModalParamsSchema,
                  searchParams: {
                    driversModal: 'add',
                    withDeliveryDetails: 'true',
                  },
                })}`}
                onClick={onClose}
              >
                {ctIntl.formatMessage({ id: 'Add New Driver' })}
              </Button>
            </Box>
          </Stack>
        ))
        .exhaustive()}
    </Stack>
  )
}

export default function AddDriverPopper({ children }: Props) {
  const popoverRef = useRef<PopoverHandles>(null)

  return (
    <Popover
      ref={popoverRef}
      content={<FleetDriversList onClose={() => popoverRef.current?.closePopover()} />}
      popoverProps={{
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'left',
        },
      }}
    >
      {children}
    </Popover>
  )
}
