import { FormControlLabel, Radio, RadioGroup, Stack } from '@karoo-ui/core'
import { match, P } from 'ts-pattern'

import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { ctIntl } from 'src/util-components/ctIntl'

import type { JobId } from '../api/jobs/types'
import type { RouteId } from '../api/routes/types'
import type { RoutesList } from '../api/routes/useRoutesList'
import { useDriversAndRoutesContext } from '../components/MapPanel/DriversAndRoutesProvider'

export enum UPDATE_ROUTE_OPTION {
  ROUTE = 'plan',
  RECURRING = 'driver',
}

type Return = UPDATE_ROUTE_OPTION

type UseConfirmUpdateRecurringRouteParams = {
  operatingJobIds: Array<JobId>
  assignToRouteId?: RouteId // Means assignment if passing
}

export default async function confirmUpdateRecurringRoute(
  disableOption?: UPDATE_ROUTE_OPTION,
): Promise<Return> {
  return new Promise((resolve, reject) => {
    let checked: UPDATE_ROUTE_OPTION =
      disableOption === UPDATE_ROUTE_OPTION.ROUTE
        ? UPDATE_ROUTE_OPTION.RECURRING
        : UPDATE_ROUTE_OPTION.ROUTE

    Dialog.alert({
      title: ctIntl.formatMessage({ id: 'delivery.route.update' }),
      onResult: () => {
        resolve(checked)
      },
      onClose: reject,
      content: (
        <Stack gap={2}>
          <RadioGroup
            defaultValue={checked}
            onChange={(_, val) => {
              checked = val as UPDATE_ROUTE_OPTION
            }}
          >
            {disableOption !== UPDATE_ROUTE_OPTION.ROUTE && (
              <FormControlLabel
                value={UPDATE_ROUTE_OPTION.ROUTE}
                control={<Radio size="small" />}
                label={ctIntl.formatMessage({
                  id: 'Update only this route',
                })}
              />
            )}
            {disableOption !== UPDATE_ROUTE_OPTION.RECURRING && (
              <FormControlLabel
                value={UPDATE_ROUTE_OPTION.RECURRING}
                control={<Radio size="small" />}
                label={ctIntl.formatMessage({
                  id: 'Update recurring setup',
                })}
              />
            )}
          </RadioGroup>
        </Stack>
      ),
    })
  })
}

export async function warningForUpdateRoutesOnce(): Promise<UPDATE_ROUTE_OPTION.ROUTE> {
  return new Promise((resolve, reject) => {
    Dialog.alert({
      title: ctIntl.formatMessage({
        id: 'Update routes once',
      }),
      onResult: () => {
        resolve(UPDATE_ROUTE_OPTION.ROUTE)
      },
      onClose: reject,
      content: (
        <Stack>
          <Stack>
            {ctIntl.formatMessage({
              id: 'This action will affect these routes only once.',
            })}
          </Stack>
          <Stack>
            {ctIntl.formatMessage({
              id: 'Changes will not impact the recurring setup or future routes.',
            })}
          </Stack>
        </Stack>
      ),
    })
  })
}

export function useConfirmUpdateRecurringRoute() {
  const { routesList, normalizedRoutesList } = useDriversAndRoutesContext()

  return async ({
    operatingJobIds,
    assignToRouteId,
  }: UseConfirmUpdateRecurringRouteParams): Promise<Return> => {
    const affectedFullRecurringRoutes: Array<RoutesList.Route> = []
    let jobIdsNotFoundInFullRecurringRoute = [...operatingJobIds]

    for (const route of routesList ?? []) {
      if (jobIdsNotFoundInFullRecurringRoute.length === 0) break

      const routeRecurringJobIds = new Set(
        route.orderedStops.flatMap((stop) => (stop.recurrence ? [stop.jobId] : [])),
      )
      if (routeRecurringJobIds.size > 0) {
        const jobIdsNotBelongToThisRoute = jobIdsNotFoundInFullRecurringRoute.filter(
          (jobId) => !routeRecurringJobIds.has(jobId),
        )
        if (
          jobIdsNotBelongToThisRoute.length !==
          jobIdsNotFoundInFullRecurringRoute.length
        ) {
          affectedFullRecurringRoutes.push(route)
          jobIdsNotFoundInFullRecurringRoute = jobIdsNotBelongToThisRoute
        }
      }
    }

    const assignToRoute = assignToRouteId
      ? normalizedRoutesList?.byId[assignToRouteId]
      : undefined

    if (assignToRoute?.isFullRecurring) {
      affectedFullRecurringRoutes.push(assignToRoute)
    }

    // Logic for showing the dialog
    try {
      return match({ affectedFullRecurringRoutes, assignToRoute })
        .with(
          { affectedFullRecurringRoutes: P.when((routes) => routes.length === 0) },
          () => Promise.resolve(UPDATE_ROUTE_OPTION.ROUTE),
        )
        .with(
          { affectedFullRecurringRoutes: P.when((routes) => routes.length === 1) },
          async ({ affectedFullRecurringRoutes }) =>
            await match(affectedFullRecurringRoutes[0])
              .with(
                { deliveryDriverId: P.nonNullable },
                async () => await confirmUpdateRecurringRoute(),
              )
              .otherwise(
                async () =>
                  await confirmUpdateRecurringRoute(
                    assignToRoute ? UPDATE_ROUTE_OPTION.ROUTE : undefined,
                  ),
              ),
        )
        .otherwise(
          async ({ assignToRoute }) =>
            await match(assignToRoute)
              .with(
                { deliveryDriverId: P.nullish },
                async () =>
                  await confirmUpdateRecurringRoute(UPDATE_ROUTE_OPTION.ROUTE),
              )
              .otherwise(async () => await warningForUpdateRoutesOnce()),
        )
    } catch {
      return Promise.reject()
    }
  }
}
