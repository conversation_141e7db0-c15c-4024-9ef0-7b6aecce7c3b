import { ctIntl } from 'src/util-components/ctIntl'

// Create Job
const createJobInternetError = 'deliver.error.createJob.internetError'
const createJobSystemError = 'deliver.error.createJob.systemError'
const createJobDriverNotExist = 'deliver.error.createJob.driverNotExist'
const createJobInvalidAddress = 'deliver.error.createJob.invalidAddress'
const createJobDriverOnBreak = 'deliver.error.createJob.driverOnBreak'

export const getCreateJobError = (errMsg: string) => {
  const msg = errMsg.trim()

  switch (true) {
    case msg === 'Check your network connection.': {
      return createJobInternetError
    }
    case msg === 'STATUS_500': {
      return createJobSystemError
    }
    case msg.includes('Driver does not exist'): {
      return createJobDriverNotExist
    }
    case msg.includes('The address is invalid'): {
      return createJobInvalidAddress
    }
    case msg.includes('customerId is invalid'): {
      return msg
    }
    case msg.includes('Drivers that are on break cannot get jobs assigned'): {
      return createJobDriverOnBreak
    }
    default: {
      return ''
    }
  }
}

// Create Driver
const createDriverInternetError = 'deliver.error.createDriver.internetError'
const createDriverSystemError = 'deliver.error.createDriver.systemError'
const createDriverPhoneTaken = 'deliver.error.createDriver.phoneNumberTaken'
const createDriverUserName = 'deliver.error.createDriver.userNameTaken'
const createDriverEmailTaken = 'deliver.error.createDriver.emailTaken'
const createDriverPhoneNumberTaken = 'delivery.error.createDriver.phoneNumberTaken'

// Some error message contains dynamic value, so we need check if it match
export const getCreateDriverError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case 'Check your network connection.' === msg: {
      return createDriverInternetError
    }
    case 'STATUS_500' === msg: {
      return createDriverSystemError
    }
    case msg.includes('should be unique'): {
      return createDriverPhoneTaken
    }
    case msg.includes('The loginUsername has already been taken'): {
      return createDriverUserName
    }
    case msg.includes('The phoneNumber has already been taken.'): {
      return createDriverPhoneNumberTaken
    }
    case msg.includes('The email has already been taken'): {
      return createDriverEmailTaken
    }
    default: {
      return ''
    }
  }
}

// Edit Job
const editJobInternetError = 'deliver.error.editJob.internetError'
const editJobSystemError = 'deliver.error.editJob.systemError'
const editCompletedJob = 'deliver.error.editJob.completedJob'
const editNonExistJob = 'deliver.error.editJob.jobNotExist'
const editJobDriverNotExist = 'deliver.error.editJob.driverNotExist'
const editJobInvalidAddress = 'deliver.error.editJob.invalidAddress'
const editJobDriverOnBreak = 'deliver.error.editJob.driverOnBreak'
const editJobAppointmentSlotNotAvailable =
  'delivery.error.editJob.appointmentSlotNotAvailable'
const editJobAppointmentRequiredTimeWindow =
  'delivery.error.editJob.appointmentRequiredTimeWindow'

export const getEditJobError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case msg === 'Check your network connection.': {
      return editJobInternetError
    }
    case msg === 'STATUS_500': {
      return editJobSystemError
    }
    case msg.includes('Unable to edit completed jobs'): {
      return editCompletedJob
    }
    case msg.includes('Job does not exist'): {
      return editNonExistJob
    }
    case msg.includes('Driver does not exist'): {
      return editJobDriverNotExist
    }
    case msg.includes('The address is invalid'): {
      return editJobInvalidAddress
    }
    case msg.includes('Drivers that are on break cannot get jobs assigned'): {
      return editJobDriverOnBreak
    }
    case msg.includes('The requested appointment slot is not available'): {
      return editJobAppointmentSlotNotAvailable
    }
    case msg.includes(
      'Delivery time windows are required for jobs linked to appointments',
    ): {
      return editJobAppointmentRequiredTimeWindow
    }
    default: {
      return ''
    }
  }
}

// Cancel Job
const cancelJobInternetError = 'deliver.error.cancelJob.internetError'
const cancelJobSystemError = 'deliver.error.cancelJob.systemError'
const cancelInvalidId = 'deliver.error.cancelJob.invalidId'

export const getCancelJobError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case msg === 'Check your network connection.': {
      return cancelJobInternetError
    }
    case msg === 'STATUS_500': {
      return cancelJobSystemError
    }
    case msg.includes('Invalid job id'): {
      return cancelInvalidId
    }
    default: {
      return cancelJobSystemError
    }
  }
}

// Restore Job
const restoreJobInternetError = 'deliver.error.restoreJob.internetError'
const restoreJobSystemError = 'deliver.error.restoreJob.systemError'
const restoreInvalidId = 'deliver.error.restoreJob.invalidId'

export const getRestoreJobError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case msg === 'Check your network connection.': {
      return restoreJobInternetError
    }
    case msg === 'STATUS_500': {
      return restoreJobSystemError
    }
    case msg.includes('Invalid job id'): {
      return restoreInvalidId
    }
    default: {
      return restoreJobSystemError
    }
  }
}

// Edit Driver
const editDriverInternetError = 'deliver.error.editDriver.internetError'
const editDriverSystemError = 'deliver.error.editDriver.systemError'
const editDriverUniquePhoneNo = 'deliver.error.editDriver.phoneNoMustBeUnique'
const editDriverUserNameTaken = 'deliver.error.editDriver.userNameTaken'
const editDriverEmailTaken = 'deliver.error.editDriver.emailTaken'

export const getEditDriverError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case 'Check your network connection.' === msg: {
      return editDriverInternetError
    }
    case 'STATUS_500' === msg: {
      return editDriverSystemError
    }
    case msg.includes('should be unique'): {
      return editDriverUniquePhoneNo
    }
    case msg.includes('The loginUsername has already been taken'): {
      return editDriverUserNameTaken
    }
    case msg.includes('The phoneNumber has already been taken.'): {
      return createDriverPhoneNumberTaken
    }
    case msg.includes('The email has already been taken'): {
      return editDriverEmailTaken
    }
    default: {
      return ''
    }
  }
}

// AssignJob
const assignDriverInternetError = 'deliver.error.assignJob.internetError'
const assignDriverSystemError = 'deliver.error.assignJob.systemError'
const assignJobStatus = 'deliver.error.assignJob.status'
const assignJobDriverNotExist = 'deliver.error.assignJob.driverNotExist'
const assignJobDriverOnBreak = 'deliver.error.assignJob.driverOnBreak'

export const getAssignJobError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (true) {
    case msg === 'Check your network connection.': {
      return assignDriverInternetError
    }
    case msg === 'STATUS_500': {
      return assignDriverSystemError
    }
    case msg.includes(
      'Only jobs with failed rejected unassigned and unstarted can be assigned',
    ): {
      return assignJobStatus
    }
    case msg.includes('Job does not exist'): {
      return editNonExistJob
    }
    case msg.includes('Driver does not exist'): {
      return assignJobDriverNotExist
    }
    case msg.includes('Drivers that are on break cannot get jobs assigned'): {
      return assignJobDriverOnBreak
    }
    default: {
      return ''
    }
  }
}

// Create Plan
const createPlanInternetError = 'delivery.error.createRecurringRoute.internetError'
const createPlanSystemError = 'delivery.error.createRecurringRoute.systemError'
const createPlanNameRequiredError = 'delivery.error.createRecurringRoute.nameRequired'

export const getCreatePlanError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (msg) {
    case 'Check your network connection.': {
      return createPlanInternetError
    }
    case 'STATUS_500': {
      return createPlanSystemError
    }
    case 'The name is required.': {
      return createPlanNameRequiredError
    }
    default: {
      return createPlanSystemError
    }
  }
}

// Delete Plan
const deletePlanInternetError = 'deliver.error.deleteRecurringRoute.internetError'
const deletePlanSystemError = 'deliver.error.deleteRecurringRoute.systemError'
const deletePlanInvalidIdError = 'deliver.error.deleteRecurringRoute.invalidId'

export const getDeletePlanError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (msg) {
    case 'Check your network connection.': {
      return deletePlanInternetError
    }
    case 'STATUS_500': {
      return deletePlanSystemError
    }
    case 'deliver.error.deleteRecurringRoute.cannotModified': {
      return deletePlanInvalidIdError
    }
    default: {
      return deletePlanSystemError
    }
  }
}

// Edit Plan
const editPlanInternetError = 'delivery.error.editRecurringRoute.internetError'
const editPlanSystemError = 'deliver.error.editRecurringRoute.systemError'
const editPlanInvalidIdError = 'deliver.error.editRecurringRoute.invalidId'

export const getEditPlanError = (errMsg: string) => {
  const msg = errMsg.trim()
  switch (msg) {
    case 'Check your network connection.': {
      return editPlanInternetError
    }
    case 'STATUS_500': {
      return editPlanSystemError
    }
    case 'Not found': {
      return editPlanInvalidIdError
    }
    default: {
      return editPlanSystemError
    }
  }
}

// Error Type
export const createJobFail = 'deliver.error.createJobFail'
export const createDriverFail = 'deliver.error.createDriverFail'
export const editJobFail = 'deliver.error.editJobFail'
export const editDriverFail = 'deliver.error.editDriverFail'
export const assignFail = 'deliver.error.assignFail'

//UPDATE CUSTOMER
const updateCustomerInternetError = 'deliver.error.updateCustomer.internetError'
const updateCustomerSystemError = 'deliver.error.updateCustomer.systemError'
const updateCustomerInvalidIdError = 'deliver.error.updateCustomer.invalidId'
const updateCustomerInvalidAddress = 'deliver.error.updateCustomer.invalidAddress'
const updateCustomerInvalidGPS = 'deliver.error.updateCustomer.invalidGPS'

export const getUpdateCustomerError = (errMsg: string) => {
  const msg = errMsg.trim()
  if (msg.includes('The addressLine1 is required')) {
    return updateCustomerInvalidAddress
  } else if (msg.includes('The countryId is required')) {
    return updateCustomerInvalidAddress
  } else if (msg.includes('The longitude is required')) {
    return updateCustomerInvalidGPS
  } else if (msg.includes('The latitude is required')) {
    return updateCustomerInvalidGPS
  }
  switch (msg) {
    case 'Check your network connection.': {
      return updateCustomerInternetError
    }
    case 'STATUS_500': {
      return updateCustomerSystemError
    }
    case 'Not found': {
      return updateCustomerInvalidIdError
    }
    default: {
      return updateCustomerSystemError
    }
  }
}

//Fix this generic error
const addSpecialEquipmentInternetError =
  'delivery.error.addSpecialEquipment.internetError'
const addSpecialEquipmentGenericError =
  'delivery.error.addSpecialEquipment.genericError'
const addSpecialEquipmentNotFoundError = 'delivery.error.addSpecialEquipment.notFound'
const addSpecialEquipmentAlreadyExist =
  'delivery.error.addSpecialEquipment.alreadyExist'

export const getSpecialEquipmentError = (errMsg: string) => {
  const msg = errMsg.trim()
  if (msg.includes('The capabilityNames.0 has already been taken')) {
    return addSpecialEquipmentAlreadyExist
  } else {
    switch (msg) {
      case 'Check your network connection.': {
        return addSpecialEquipmentInternetError
      }
      case 'STATUS_500': {
        return addSpecialEquipmentGenericError
      }
      case 'Not found': {
        return addSpecialEquipmentNotFoundError
      }
      default: {
        return addSpecialEquipmentGenericError
      }
    }
  }
}

const jobLabels = ctIntl.unsafe_formatMessage({ id: 'Job labels' })
const jobLabelsInternetError = `${jobLabels} ${ctIntl.unsafe_formatMessage({
  id: 'delivery.error.internetError',
})}`
const jobLabelsGenericError = `${jobLabels} ${ctIntl.unsafe_formatMessage({
  id: 'delivery.error.genericError',
})}`
const jobLabelApiNotFound = `${jobLabels} ${ctIntl.unsafe_formatMessage({
  id: 'delivery.error.apiNotFound',
})}`
const jobLabelAlreadyExist = `${jobLabels} ${ctIntl.unsafe_formatMessage({
  id: 'delivery.error.alreadyExist',
})}`

export const getLabelsError = (errMsg: string) => {
  const msg = errMsg.trim()
  if (msg.includes('The labels.0 has already been taken.')) {
    return jobLabelAlreadyExist
  } else {
    switch (msg) {
      case 'Check your network connection.': {
        return jobLabelsInternetError
      }
      case 'STATUS_500': {
        return jobLabelsGenericError
      }
      case 'Not found': {
        return jobLabelApiNotFound
      }
      default: {
        return jobLabelsGenericError
      }
    }
  }
}

const subUserAssignmentError =
  'At this moment, delivery drivers can only be assigned to one sub-user'

const subUserGenericError = 'delivery.error.genericError'

export const SUBUSER_ASSIGNMENT_ERROR = 'ERR_DRIVER_TAKEN_BY_ANOTHER_SUBUSER'
export const getSubuserError = (errMsg: string) => {
  const msg = errMsg.trim()

  switch (msg) {
    case SUBUSER_ASSIGNMENT_ERROR: {
      return subUserAssignmentError
    }
    default: {
      return subUserGenericError
    }
  }
}
