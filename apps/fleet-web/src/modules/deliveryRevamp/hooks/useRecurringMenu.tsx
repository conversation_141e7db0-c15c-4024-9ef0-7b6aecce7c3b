import { ListItemIcon, ListItemText, Stack } from '@karoo-ui/core'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import CreateIcon from '@mui/icons-material/CreateOutlined'
import { useHistory, useLocation } from 'react-router'

import Dialog from 'src/modules/deliveryRevamp/contexts/dialog/dialog-configurator'
import { useRefreshDeliveryData } from 'src/modules/deliveryRevamp/hooks/useRefresh'
import { ctIntl } from 'src/util-components/ctIntl'

import type { PlanDetailReturn } from '../api/plans/types'
import { useDeleteDeliveryPlanMutation } from '../api/plans/useDeletePlanMutation'
import { getDeliveryRecurringDialogMainPath } from '../components/RecurringDialog/helpers'
import { FORM_STATE } from '../constants/job'

type Props = {
  plan: PlanDetailReturn
}

export const useRecurringMenu = ({ plan }: Props) => {
  const location = useLocation()
  const history = useHistory()
  const { refresh } = useRefreshDeliveryData()
  const deletePlanMutation = useDeleteDeliveryPlanMutation()

  const menuItems = [
    {
      value: 'editRecurring',
      label: (
        <Stack
          direction="row"
          alignItems="center"
        >
          <ListItemIcon sx={{ color: 'inherit' }}>
            <CreateIcon />
          </ListItemIcon>
          <ListItemText>
            {ctIntl.formatMessage({ id: 'delivery.editRecurringSettings' })}
          </ListItemText>
        </Stack>
      ),
      onClick: () =>
        history.push(getDeliveryRecurringDialogMainPath(location, { id: plan.planId })),
    },
    {
      value: 'duplicateRecurring',
      label: (
        <Stack
          direction="row"
          alignItems="center"
        >
          <ListItemIcon sx={{ color: 'inherit' }}>
            <ContentCopyIcon />
          </ListItemIcon>
          <ListItemText>
            {ctIntl.formatMessage({ id: 'global.duplicate' })}
          </ListItemText>
        </Stack>
      ),
      onClick: () =>
        history.push(
          getDeliveryRecurringDialogMainPath(location, {
            formParams: {
              existingFormField: { ...plan, formState: FORM_STATE.DUPLICATE },
              additionalFormField: {
                jobIds: plan.orderedJobIds,
              },
            },
          }),
        ),
    },
    {
      value: 'deleteRecurring',
      label: (
        <ListItemText sx={{ color: 'error.main' }}>
          {ctIntl.formatMessage({ id: 'delivery.deleteRecurringRoute' })}
        </ListItemText>
      ),
      onClick: () =>
        Dialog.alert({
          title: ctIntl.formatMessage({
            id: 'delivery.deleteRecurringRoute.confirmation.title',
          }),
          content: ctIntl.formatMessage({
            id: 'delivery.deleteRecurringRoute.confirmation.body',
          }),
          confirmButtonLabel: ctIntl.formatMessage({ id: 'Confirm' }),
          rejectButtonLabel: ctIntl.formatMessage({ id: 'Cancel' }),
          onResult: () => {
            deletePlanMutation.mutate(
              { planId: plan.planId },
              {
                onSuccess() {
                  refresh()
                },
              },
            )
          },
        }),
    },
  ]

  return {
    menuItems,
  }
}
