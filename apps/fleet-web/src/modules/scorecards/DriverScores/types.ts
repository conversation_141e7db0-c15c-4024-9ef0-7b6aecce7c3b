import { z } from 'zod/v4'

import {
  driverIdSchema,
  type DriverGroupId,
  type DriverScoresComparisonGroupId,
  type VehicleGroupId,
} from 'src/api/types'

export const driverScoresTableTabs = ['overview', 'ranking'] as const

export type DriverScoreTableTab = (typeof driverScoresTableTabs)[number]

export const driverScoresSearchParamsSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('tab'),
    tab: z.enum(driverScoresTableTabs),
  }),
  z.object({
    type: z.literal('driver'),
    id: driverIdSchema,
    start: z.iso.datetime({ offset: true }),
    end: z.iso.datetime({ offset: true }),
  }),
])

export type DriverScoresSearchParams = z.infer<typeof driverScoresSearchParamsSchema>

export type SelectedComparisonGroup = Readonly<{
  id: DriverScoresComparisonGroupId
  name: string
  driverGroupIds: ReadonlySet<DriverGroupId> | 'all'
  vehicleGroupIds: ReadonlySet<VehicleGroupId> | 'all'
  vehicleTypeId: string | null
  color: string
}>
