import { useCallback, useMemo, useState } from 'react'
import {
  Box,
  DateRangePicker,
  SingleInputDateRangeField,
  type DateRange,
  type DateRangePickerProps,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'

import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import type { ExcludeStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'

export type ActivityDateRangeFilter = {
  value: DateRange<DateTime>
  formatted: {
    from: string
    to: string
  }
}

type Props = {
  label?: string
  selectedDateRange: DateRange<DateTime>
  rangeMaxSizeInDays?: number
  onChange: ExcludeStrict<DateRangePickerProps<DateTime>['onChange'], undefined>
} & DateRangePickerProps<DateTime>

function ActivityDateRangePicker({
  label,
  selectedDateRange,
  rangeMaxSizeInDays,
  onChange,
  ...rest
}: Props) {
  const [hasExceedDateRange, setHasExceedDateRange] = useState(false)

  const minDate = useMemo(
    () => DateTime.local().minus({ years: 2 }).startOf('year'),
    [],
  )
  const shortcuts = useDateRangeShortcutItems()

  const shortcutsItems = useMemo(
    () => [
      shortcuts.thisWeek,
      shortcuts.lastWeekTillDate,
      shortcuts.lastWeek,
      shortcuts.last2Weeks,
      shortcuts.thisMonth,
      shortcuts.lastMonth,
      shortcuts.reset,
    ],
    [shortcuts],
  )

  const handleChange = useCallback<
    NonNullable<DateRangePickerProps<DateTime>['onChange']>
  >(
    (...params) => {
      if (isDateRangeValid(params[0], rangeMaxSizeInDays)) {
        setHasExceedDateRange(false)
        onChange(...params)
      } else {
        setHasExceedDateRange(true)
      }
    },
    [onChange, rangeMaxSizeInDays],
  )

  return (
    <Box
      sx={{
        '.MuiFormControl-root': {
          minWidth: '200px',
        },
      }}
    >
      <DateRangePicker
        label={
          label ??
          ctIntl.formatMessage({
            id: 'Date Range',
          })
        }
        defaultValue={selectedDateRange}
        disableFuture
        minDate={minDate}
        onChange={handleChange}
        slots={{
          field: SingleInputDateRangeField,
        }}
        {...rest}
        slotProps={{
          actionBar: { actions: ['accept'] },
          shortcuts: { items: shortcutsItems },
          textField: {
            error: rangeMaxSizeInDays !== undefined && hasExceedDateRange,
            helperText:
              rangeMaxSizeInDays !== undefined && hasExceedDateRange
                ? ctIntl.formatMessage(
                    {
                      id: 'tachograph.activityDateRangeFilter.error',
                    },
                    {
                      values: { rangeMaxSizeInDays },
                    },
                  )
                : undefined,
            ...rest.slotProps?.textField,
          },
        }}
      />
    </Box>
  )
}
export default ActivityDateRangePicker

const isDateRangeValid = (
  dateRange: DateRange<DateTime>,
  maxDays: number | undefined,
): boolean => {
  if (maxDays === undefined) {
    return true
  }

  const [start, end] = dateRange
  if (start === null || end === null) {
    return true
  }

  const diffInDays = end.diff(start, 'days').days
  return diffInDays <= maxDays
}
