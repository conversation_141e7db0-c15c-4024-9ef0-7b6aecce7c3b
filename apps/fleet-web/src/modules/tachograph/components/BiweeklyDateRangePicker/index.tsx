import { useMemo, useRef } from 'react'
import { Box, DateRangePicker, TextField, type DateRange } from '@karoo-ui/core'
import type { PayloadAction } from '@reduxjs/toolkit'
import type { DateTime } from 'luxon'

// import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
// import IntlTypography from 'src/util-components/IntlTypography'
import useEnhancedReducer from 'src/hooks/use-enhanced-reducer'
import { ctIntl } from 'src/util-components/ctIntl'
import { getBiweeklyRangeFromDate } from 'src/util-functions/luxon/utils'

function BiweeklyDateRangePicker() {
  const [{ isDatePickerOpen, selectedDateRange }, actions] = useEnhancedReducer({
    initialState,
    reducers,
  })

  const containerRef = useRef<HTMLDivElement>()

  const formattedDateRange = useMemo(
    () =>
      selectedDateRange
        .map((date) => {
          if (date !== null) {
            return date.toLocaleString({
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })
          }

          return ''
        })
        .join(' - '),
    [selectedDateRange],
  )

  return (
    <Box
      py={2}
      px={1}
      boxShadow="0 1px 0 0 rgb(221 221 221)"
      ref={containerRef}
    >
      <TextField
        fullWidth
        readOnly
        label={ctIntl.formatMessage({
          id: 'Biweekly Range',
        })}
        value={formattedDateRange}
        defaultValue={formattedDateRange}
        onClick={() => {
          actions.clickedTextField()
          if (containerRef.current !== undefined) {
            const startDateInput = containerRef.current.lastChild
              ?.firstChild as HTMLElement

            startDateInput.click()
          }
        }}
      ></TextField>
      {/* TODO: Use SingleInputDateRangeField with calendar once it is ready
      https://github.com/mui/mui-x/issues/7606 */}
      <DateRangePicker
        sx={{
          '.MuiFormControl-root:first-child': {
            display: 'none',
          },
          '.MuiFormControl-root:last-child': {
            display: 'none',
          },
        }}
        calendars={1}
        open={isDatePickerOpen}
        value={selectedDateRange}
        disableFuture
        onChange={(value) =>
          actions.handleDateRangeChange({
            changedDateRange: value,
            containerRef,
          })
        }
        onClose={() => {
          actions.handleCalendarClose()
        }}
        closeOnSelect={false}
        slots={{
          fieldSeparator: () => <></>,
          // layout: ({ children }) => (
          //   <Stack width="min-content">
          //     {children}
          //     <Stack
          //       direction="row"
          //       px={2}
          //       pb={2}
          //       gap={1}
          //     >
          //       <InfoOutlinedIcon color="info" />
          //       <IntlTypography
          //         variant="body2"
          //         msgProps={{
          //           id: 'Select a day to automatically select a biweekly range to do a biweekly analysis.',
          //         }}
          //       ></IntlTypography>
          //     </Stack>
          //   </Stack>
          // ),
        }}
        slotProps={{
          actionBar: { actions: ['accept'] },
        }}
      />
    </Box>
  )
}

export default BiweeklyDateRangePicker

type State = {
  isDatePickerOpen: boolean
  selectedDateRange: DateRange<DateTime>
}

const initialState = (): State => ({
  isDatePickerOpen: false,
  selectedDateRange: getBiweeklyRangeFromDate(),
})

const reducers = {
  clickedTextField(draft: State) {
    draft.isDatePickerOpen = true
  },
  handleDateRangeChange(
    draft: State,
    {
      payload: { changedDateRange, containerRef },
    }: PayloadAction<{
      changedDateRange: DateRange<DateTime>
      containerRef: any
    }>,
  ) {
    const [newStartDate] = changedDateRange
    const [selectedDateRangeStart] = draft.selectedDateRange

    if (
      newStartDate !== null &&
      selectedDateRangeStart !== null &&
      newStartDate.toUnixInteger() !== selectedDateRangeStart.toUnixInteger()
    ) {
      draft.selectedDateRange = getBiweeklyRangeFromDate(newStartDate)
    }

    // draft.isDatePickerOpen = false
    if (containerRef.current !== undefined) {
      const startDateInput = containerRef.current.lastChild?.firstChild as HTMLElement

      startDateInput.click()
    }
  },
  handleCalendarClose(draft: State) {
    draft.isDatePickerOpen = false
  },
}
