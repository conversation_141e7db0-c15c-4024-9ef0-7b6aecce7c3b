import { useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Radio,
  TextField,
  Typography,
  type KarooUiInternalTheme,
  type SxProps,
} from '@karoo-ui/core'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import type {
  QueryObserverPlaceholderResult,
  QueryObserverSuccessResult,
} from '@tanstack/react-query'
import { useHistory } from 'react-router'
import * as R from 'remeda'

import type { ClientUserId } from 'api/types'
import { IGNORE_FIELD_UPDATE_META } from 'api/utils'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import type { UseUsersQueryData } from 'src/modules/api/useUsersQuery'
import { getBookingPermissionsByDepartmentsModalPath } from 'src/modules/carpool/Settings/BookingPermissions/Departments/utils'
import AutocompleteInputWithLimit from 'src/util-components/autocomplete-input-with-limit'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import type { UseBookingApprovalSettingsMutation } from '../api/mutations'
import type { UseBookingApprovalSettingsQueryData } from '../api/queries'
import { getPreciseAutocompleteOnChangeReason } from '../utils'
import {
  HeadlessControlWithLabel,
  RadioSectionCardUnit,
  SubRadioWithLabel,
} from './components'

type Props = {
  bookingApprovalSettingsSuccessQuery:
    | QueryObserverSuccessResult<UseBookingApprovalSettingsQueryData, Error>
    | QueryObserverPlaceholderResult<UseBookingApprovalSettingsQueryData, Error>
  bookingApprovalSettingsMutation: UseBookingApprovalSettingsMutation
  usersQueryData: UseUsersQueryData
}

export function ManuallyApproveSectionCard({
  bookingApprovalSettingsSuccessQuery,
  bookingApprovalSettingsMutation,
  usersQueryData,
}: Props) {
  const history = useHistory()
  const queryApprovalMeta = bookingApprovalSettingsSuccessQuery.data

  const isSectionSelected = queryApprovalMeta.approvalType === 'manual'
  const selectedApproverType =
    queryApprovalMeta.approvalType === 'manual' ? queryApprovalMeta.approver.type : null

  const noOptionsSelectedBoldChunksFn = (chunks: Array<React.ReactNode>) => (
    <Typography
      variant="inherit"
      component="span"
      fontWeight="500"
    >
      {chunks}
    </Typography>
  )
  return (
    <RadioSectionCardUnit
      sx={{
        display: 'grid',
        pt: 2,
        pb: 3,
        gridTemplateAreas: `
        'radio radio-label'
        '. right-content'
        `,
        gridTemplateColumns: 'auto 1fr',
        gridTemplateRows: 'auto 1fr',
        columnGap: 1.5,
      }}
      isSelected={isSectionSelected}
    >
      <HeadlessControlWithLabel label="carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.title">
        {({ controlAccessibilityId, label }) => (
          <>
            <Radio
              id={controlAccessibilityId}
              disabled={bookingApprovalSettingsMutation.isPending}
              sx={{ gridArea: 'radio' }}
              checked={isSectionSelected}
              value="manual"
              onChange={(_, checked) => {
                if (checked) {
                  bookingApprovalSettingsMutation.mutate({
                    approvalType: 'manual',
                    approver: {
                      type: 'single',
                      approverIds: IGNORE_FIELD_UPDATE_META,
                    },
                  })
                }
              }}
            />
            <Typography
              sx={{
                gridArea: 'radio-label',
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
              }}
              variant="subtitle2"
              component="label"
              htmlFor={controlAccessibilityId}
            >
              {ctIntl.formatMessage({ id: label })}
            </Typography>
          </>
        )}
      </HeadlessControlWithLabel>

      <Box
        sx={{
          gridArea: 'right-content',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
          <SubRadioWithLabel
            labelMsgId="carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.singleApprover"
            selectedHelperText={ctIntl.formatMessage({
              id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.singleApprover.selected.helperText',
            })}
            disabled={bookingApprovalSettingsMutation.isPending}
            value="single"
            checked={selectedApproverType === 'single'}
            onChange={(_, checked) => {
              if (checked) {
                bookingApprovalSettingsMutation.mutate({
                  approvalType: 'manual',
                  approver: { type: 'single', approverIds: IGNORE_FIELD_UPDATE_META },
                })
              }
            }}
          />
          <SubRadioWithLabel
            labelMsgId="carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.multiApprovers"
            selectedHelperText={ctIntl.formatMessage({
              id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.multiApprovers.selected.helperText',
            })}
            disabled={bookingApprovalSettingsMutation.isPending}
            value="multiple"
            checked={selectedApproverType === 'multiple'}
            onChange={(_, checked) => {
              if (checked) {
                bookingApprovalSettingsMutation.mutate({
                  approvalType: 'manual',
                  approver: {
                    type: 'multiple',
                    approverIds: IGNORE_FIELD_UPDATE_META,
                  },
                })
              }
            }}
          />
        </Box>

        {!isSectionSelected && (
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <IntlTypography
              msgProps={{
                id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.noOptionSelectedText.singleApprover',
                values: {
                  b: noOptionsSelectedBoldChunksFn,
                },
              }}
              variant="caption"
              color="textSecondary"
            />
            <IntlTypography
              msgProps={{
                id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.noOptionSelectedText.multiApprovers',
                values: {
                  b: noOptionsSelectedBoldChunksFn,
                },
              }}
              variant="caption"
              color="textSecondary"
            />
          </Box>
        )}

        <MultiApproversAutocomplete
          // Reset state when the approver type changes
          key={selectedApproverType}
          disabled={bookingApprovalSettingsMutation.isPending || !selectedApproverType}
          loading={bookingApprovalSettingsMutation.isPending}
          sx={{
            mt: 2,
          }}
          usersQueryData={usersQueryData}
          bookingApprovalSettingsMutation={bookingApprovalSettingsMutation}
          label={ctIntl.formatMessage({
            id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.multiApprovers.autocomplete.label',
          })}
          bookingApprovalSettingsSuccessQuery={bookingApprovalSettingsSuccessQuery}
        />

        <Button
          sx={{ width: 'max-content', mt: 2.5 }}
          size="small"
          variant="text"
          color="primary"
          onClick={() => {
            history.push(
              getBookingPermissionsByDepartmentsModalPath(history.location, {
                action: 'edit-departments',
              }),
            )
          }}
          endIcon={<EditOutlinedIcon />}
        >
          {ctIntl.formatMessage({
            id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.editDepartmentsAndApproversButton.label',
          })}
        </Button>
      </Box>
    </RadioSectionCardUnit>
  )
}

type UserAsApproverOption = {
  type: 'sub-user'
  value: ClientUserId
  label: string
}
function MultiApproversAutocomplete({
  usersQueryData,
  bookingApprovalSettingsMutation,
  bookingApprovalSettingsSuccessQuery,
  label,
  sx,
  disabled,
  loading,
}: {
  usersQueryData: UseUsersQueryData
  bookingApprovalSettingsMutation: UseBookingApprovalSettingsMutation
  bookingApprovalSettingsSuccessQuery:
    | QueryObserverSuccessResult<UseBookingApprovalSettingsQueryData, Error>
    | QueryObserverPlaceholderResult<UseBookingApprovalSettingsQueryData, Error>
  label: string
  sx?: SxProps<KarooUiInternalTheme>
  disabled: boolean
  loading: boolean
}) {
  const persistedSelectedValues = useMemo((): ReadonlyArray<ClientUserId> => {
    if (bookingApprovalSettingsSuccessQuery.data.approvalType === 'manual') {
      return bookingApprovalSettingsSuccessQuery.data.approver.approverIds
    }
    return []
  }, [bookingApprovalSettingsSuccessQuery.data])

  const [nonPersistedSelectedValues, setNonPersistedSelectedValues] = useState<
    ReadonlyArray<ClientUserId>
  >(persistedSelectedValues)
  const [formState, setFormState] = useState<{
    blurCount: number
  }>({
    blurCount: 0,
  })

  const usersData = usersQueryData.users

  const autocompleteOptionsMeta = useMemo(
    () =>
      (usersData ?? []).reduce(
        (acc, user) => {
          if (user.status === 'active') {
            const option: UserAsApproverOption = {
              type: 'sub-user',
              value: user.id,
              label: user.username,
            }
            acc.array.push(option)
            acc.byId.set(user.id, option)
          }
          return acc
        },
        {
          array: [] as Array<UserAsApproverOption>,
          byId: new Map<ClientUserId, UserAsApproverOption>(),
        },
      ),
    [usersData],
  )

  useEffectExceptOnMount(() => {
    setNonPersistedSelectedValues(persistedSelectedValues)
  }, [persistedSelectedValues, setNonPersistedSelectedValues])

  const selectedValues = useMemo((): Array<UserAsApproverOption> => {
    const values: Array<UserAsApproverOption> = []
    for (const value of nonPersistedSelectedValues) {
      const option = autocompleteOptionsMeta.byId.get(value)
      if (option) {
        values.push(option)
      }
    }
    return values
  }, [nonPersistedSelectedValues, autocompleteOptionsMeta])

  // Only show error if the field has been touched
  const errorMsg =
    formState.blurCount > 0 &&
    bookingApprovalSettingsSuccessQuery.data.approvalType === 'manual' &&
    selectedValues.length === 0
      ? ctIntl.formatMessage({
          id: 'carpool.settings.tab.bookingManagement.section.bookingApprovalSettings.card.manual.option.multiApprovers.autocomplete.errorMin',
        })
      : ''

  const tryPersistSelectedValues = (values: ReadonlyArray<ClientUserId>) => {
    if (bookingApprovalSettingsSuccessQuery.data.approvalType === 'automatic') {
      return // make ts happy
    }
    if (R.isDeepEqual(values, persistedSelectedValues) || values.length === 0) {
      return
    }

    const approverType = bookingApprovalSettingsSuccessQuery.data.approver.type
    bookingApprovalSettingsMutation.mutate({
      approvalType: 'manual',
      approver: { type: approverType, approverIds: values },
    })
  }

  return (
    <Autocomplete
      loading={loading}
      sx={sx}
      slotProps={{
        listbox: {
          // The user will probably want to see a lot of options we we increase the max height of the listbox
          style: { maxHeight: 'min(50vh, 700px)' },
        },
      }}
      {...getAutocompleteVirtualizedProps({
        options: autocompleteOptionsMeta.array,
        renderRowSingleItemContent: ({ label, state }) => (
          <>
            <Checkbox
              checked={state.selected}
              color="primary"
            />
            {label}
          </>
        ),
      })}
      multiple
      value={selectedValues}
      onChange={(event, value, rawReason) => {
        const reason = getPreciseAutocompleteOnChangeReason(event, rawReason)
        const newSelectedValues = value.map((v) => v.value)
        setNonPersistedSelectedValues(newSelectedValues)
        if (reason === 'removeOptionFromChip') {
          tryPersistSelectedValues(newSelectedValues)
        }
      }}
      onClose={() => {
        tryPersistSelectedValues(nonPersistedSelectedValues)
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          error={!!errorMsg}
          helperText={errorMsg}
          label={label}
          onBlur={() => {
            setFormState({ blurCount: formState.blurCount + 1 })
          }}
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <>
                  {loading ? (
                    <CircularProgress
                      color="inherit"
                      size={20}
                    />
                  ) : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
      disableCloseOnSelect
      disabled={disabled}
      renderTags={(value, getTagProps) => (
        <AutocompleteInputWithLimit<(typeof autocompleteOptionsMeta.array)[number]>
          value={value}
          getTagProps={getTagProps}
          // We allow more showing tags than usual because this autocomplete is large enough for it
          limitTags={12}
        />
      )}
    />
  )
}
