import { useState } from 'react'
import {
  But<PERSON>,
  <PERSON>box,
  DateRangePicker,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormGroup,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  styled,
  Typography,
  type DateRange,
  type TypographyProps,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import { DateTime } from 'luxon'
import * as R from 'remeda'

import { useTypedSelector } from 'src/redux-hooks'
import type { TripsDownloadFileExtension } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'

import { getActivityDateRange } from '../FleetMapView/DetailsPanel/slice'

type Props = {
  fileExtensionSelectorOnly?: boolean
  onClose: () => void
  handleDownloadRequest: (
    activeFileTab: TripsDownloadFileExtension,
    zeroKmTrips: boolean,
    flaggedOnly: boolean,
    selectedDateRange: { from: Date; to: Date },
  ) => void
}

const ReTripDownloadModal = ({
  onClose,
  handleDownloadRequest,
  fileExtensionSelectorOnly = false,
}: Props) => {
  const fileExportOptions = [
    {
      label: ctIntl.formatMessage({ id: 'Excel File' }),
      id: 'xls',
    },
    {
      label: ctIntl.formatMessage({ id: 'KML File' }),
      id: 'kml',
    },
    {
      label: ctIntl.formatMessage({ id: 'GPX File' }),
      id: 'gpx',
    },
  ]

  const activityDateRange = useTypedSelector(getActivityDateRange)

  const [dateRange, setDateRange] = useState<DateRange<DateTime>>(
    () =>
      [
        DateTime.fromJSDate(activityDateRange.from),
        DateTime.fromJSDate(activityDateRange.to),
      ] as DateRange<DateTime>,
  )
  const [isDateRangeValid, setIsDateRangeValid] = useState<boolean>(true)

  const [zeroKmTrips, setZeroKmTrips] = useState(false)
  const [flaggedOnly, setFlaggedOnly] = useState(false)
  const [activeFileTab, setActiveFileTab] = useState<TripsDownloadFileExtension>('xls')

  const dateFrom = dateRange[0]
  const dateTo = dateRange[1]
  const isIncompleteDateRange = R.isNullish(dateFrom) || R.isNullish(dateTo)

  return (
    <Dialog
      open
      onClose={onClose}
    >
      <Stack>
        <DialogTitle
          sx={{
            p: 3,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {ctIntl.formatMessage({ id: 'map.detailsPanel.download.trips' })}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Stack gap={3}>
            {!fileExtensionSelectorOnly && (
              <>
                <Section>
                  <StyledDescription>
                    {ctIntl.formatMessage({
                      id: 'map.detailsPanel.download.description',
                    })}
                  </StyledDescription>
                  <DateRangePicker
                    value={dateRange}
                    onChange={(newDateRange, { validationError }) => {
                      setIsDateRangeValid(
                        R.isNullish(validationError[0]) &&
                          R.isNullish(validationError[1]),
                      )

                      setDateRange(newDateRange)
                    }}
                    disableFuture
                  />
                </Section>
                <Section>
                  <StyledDescription>
                    {ctIntl.formatMessage({ id: 'map.detailsPanel.download.export' })}
                  </StyledDescription>
                  <FormGroup
                    sx={{ flexDirection: 'row', justifyContent: 'space-between' }}
                  >
                    <FormControlLabel
                      control={
                        <StyledCheckbox
                          onChange={() => setZeroKmTrips((prevState) => !prevState)}
                          value={zeroKmTrips}
                        />
                      }
                      label={ctIntl.formatMessage({
                        id: 'map.detailsPanel.download.0kmtrips',
                      })}
                    />
                    <FormControlLabel
                      control={
                        <StyledCheckbox
                          onChange={() => setFlaggedOnly((prevState) => !prevState)}
                          value={flaggedOnly}
                        />
                      }
                      label={ctIntl.formatMessage({
                        id: 'map.detailsPanel.download.flaggedonly',
                      })}
                    />
                  </FormGroup>
                </Section>
              </>
            )}
            <StyledDescription>
              {ctIntl.formatMessage({ id: 'map.detailsPanel.download.selectExport' })}
            </StyledDescription>
            <FormControl>
              <RadioGroup
                row
                name="export-file-type"
                value={activeFileTab}
                onChange={(_, id) => setActiveFileTab(id as TripsDownloadFileExtension)}
              >
                {fileExportOptions.map((option) => (
                  <FormControlLabel
                    key={option.id}
                    value={option.id}
                    control={
                      <Radio
                        sx={{
                          '&.MuiRadio-root': {
                            paddingX: 1,
                            paddingY: 0,
                          },
                          '& .MuiSvgIcon-root': {
                            fontSize: '20px',
                          },
                        }}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </RadioGroup>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'space-between',
            paddingX: 3,
            paddingY: 2,
            borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          }}
        >
          <Button
            variant="outlined"
            color="secondary"
            onClick={onClose}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            variant="contained"
            color="success"
            startIcon={<DownloadOutlinedIcon />}
            onClick={() => {
              if (isIncompleteDateRange) {
                return
              }
              handleDownloadRequest(activeFileTab, zeroKmTrips, flaggedOnly, {
                from: dateFrom.toJSDate(),
                to: dateTo.toJSDate(),
              })
            }}
            disabled={!isDateRangeValid}
          >
            {ctIntl.formatMessage({ id: 'Download' })}
          </Button>
        </DialogActions>
      </Stack>
    </Dialog>
  )
}

export default ReTripDownloadModal

const StyledDescription = (props: TypographyProps) => (
  <Typography
    variant="body2"
    {...props}
  />
)

const Section = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    gap: 2,
  }),
)

const StyledCheckbox = styled(Checkbox)(({ theme }) =>
  theme.unstable_sx({
    '&.MuiCheckbox-root': {
      paddingX: 1,
      paddingY: 0,
    },
    '& .MuiSvgIcon-root': {
      fontSize: '20px',
    },
  }),
)
