import { useEffect, useMemo, useState } from 'react'
import {
  Box,
  DateRangePicker,
  IconButton,
  SingleInputDateRangeField,
  type DateRange,
  type DateRangePickerProps,
} from '@karoo-ui/core'
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined'
import { DateTime } from 'luxon'
import { useDispatch } from 'react-redux'
import * as R from 'remeda'
import { match } from 'ts-pattern'

import {
  getMaxTripLookupDaysSetting,
  getVehiclesActivityDatePickerUpperBaselineDateTime,
} from 'duxs/user'
import {
  getComputedPrivacyHideLocationsFromDay,
  getMaxAllActivityDaysSetting,
  getMaxDailyActivityDaysSetting,
} from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import { ctIntl } from 'src/util-components/ctIntl'
import { getMinAndMaxDateTimesForSpecifiedDateRangeConfig } from 'src/util-functions/luxon/utils'

import {
  changedActivityDateRange,
  getActivityActiveTab,
  getActualActivityDateRange,
  selectDefaultActivityDateRangeGetter,
} from '../../DetailsPanel/slice'

type Props = DateRangePickerProps<DateTime>

const DetailsPanelDateRangePicker = (props: Props) => {
  const dispatch = useDispatch()

  const activityActiveTab = useTypedSelector(getActivityActiveTab)
  const activityDateRange = useTypedSelector(getActualActivityDateRange)
  const maxTripLookupDaysSetting = useTypedSelector(getMaxTripLookupDaysSetting)
  const privacyHideLocationsFromDay = useTypedSelector(
    getComputedPrivacyHideLocationsFromDay,
  )
  const getDefaultActivityDateRange = useTypedSelector(
    selectDefaultActivityDateRangeGetter,
  )

  const maxDailyActivityDays = useTypedSelector(getMaxDailyActivityDaysSetting)
  const maxAllActivityDays = useTypedSelector(getMaxAllActivityDaysSetting)

  const [dateRange, setDateRange] = useState((): DateRange<DateTime> => {
    if (!activityDateRange) {
      const defaultActivityDateRange = getDefaultActivityDateRange()
      return [
        DateTime.fromJSDate(defaultActivityDateRange.from),
        DateTime.fromJSDate(defaultActivityDateRange.to),
      ]
    }
    return [
      DateTime.fromJSDate(activityDateRange.from),
      DateTime.fromJSDate(activityDateRange.to),
    ]
  })

  useEffect(() => {
    if (activityDateRange) {
      setDateRange([
        DateTime.fromJSDate(activityDateRange.from),
        DateTime.fromJSDate(activityDateRange.to),
      ])
    }
  }, [activityDateRange])

  const handleActivityDateRangeChange = (newDateRange: { from: Date; to: Date }) =>
    dispatch(
      changedActivityDateRange({
        activityDateRange: newDateRange,
        tabId: activityActiveTab,
      }),
    )

  const {
    minDate,
    maxDate,
    parseSuggestedNewDateRangeOnChange,
    getDateRangePickerDayProps,
  } = useMemo(() => {
    const upperOuterLimit = getVehiclesActivityDatePickerUpperBaselineDateTime({
      privacyHideLocationsFromDay,
    })

    const lowerOuterLimit = maxTripLookupDaysSetting
      ? DateTime.local().minus({ days: maxTripLookupDaysSetting })
      : undefined

    const fallbackMaxSizeInDays = 31

    const { min, max, parseSuggestedNewDateRangeOnChange, getDateRangePickerDayProps } =
      getMinMaxDateTimesForDateRangePickerUIConfig(dateRange, {
        rangeMaxSizeInDays: match(activityActiveTab)
          .with('daily', 'booking', () => maxDailyActivityDays ?? fallbackMaxSizeInDays)
          .with('multipleDays', () => maxAllActivityDays ?? fallbackMaxSizeInDays)
          .exhaustive(),
        outerLimits: { upper: upperOuterLimit, lower: lowerOuterLimit },
      })

    return {
      minDate: min,
      maxDate: max,
      parseSuggestedNewDateRangeOnChange,
      getDateRangePickerDayProps,
    }
  }, [
    activityActiveTab,
    dateRange,
    maxAllActivityDays,
    maxDailyActivityDays,
    maxTripLookupDaysSetting,
    privacyHideLocationsFromDay,
  ])

  return (
    <Box display="grid">
      <DateRangePicker
        disableDragEditing
        label={ctIntl.formatMessage({ id: 'Date Range' })}
        value={dateRange}
        onChange={(suggestedNewDateRange, context) => {
          const newDateRange = parseSuggestedNewDateRangeOnChange(suggestedNewDateRange)

          setDateRange([newDateRange[0], newDateRange[1]])

          if (
            R.isNullish(context.validationError[0]) &&
            R.isNullish(context.validationError[1]) &&
            newDateRange[0] &&
            newDateRange[1]
          ) {
            GA4.event({
              category: 'Vehicle',
              action: 'Vehicle Details - Trip Date Range Filter',
            })

            handleActivityDateRangeChange({
              from: newDateRange[0].toJSDate(),
              to: newDateRange[1].toJSDate(),
            })
          }
        }}
        slots={{
          field: SingleInputDateRangeField,
        }}
        slotProps={{
          day: ({ day }) => getDateRangePickerDayProps({ day }),
          actionBar: { actions: ['clear'] },
          textField: {
            InputProps: {
              endAdornment: (
                <IconButton
                  disableRipple
                  edge="end"
                >
                  <CalendarTodayOutlinedIcon />
                </IconButton>
              ),
            },
          },
        }}
        minDate={minDate}
        maxDate={maxDate}
        {...props}
      />
    </Box>
  )
}

export default DetailsPanelDateRangePicker

const isDateRangeLargerThanMaxSize = (
  range: [DateTime, DateTime],
  maxSizeInDays: number,
) => {
  const rangeStartBaseLine = range[0].startOf('day')
  const rangeEndBaseLine = range[1].startOf('day')

  const days = rangeEndBaseLine.diff(rangeStartBaseLine, 'days').as('days')
  return days > maxSizeInDays
}

type DateRangePickerDaySlotProps = Partial<
  NonNullable<NonNullable<DateRangePickerProps<DateTime>['slotProps']>['day']>
>

const getMinMaxDateTimesForDateRangePickerUIConfig = (
  ...params: Parameters<typeof getMinAndMaxDateTimesForSpecifiedDateRangeConfig>
): {
  min: DateTime | undefined
  max: DateTime | undefined
  parseSuggestedNewDateRangeOnChange: (
    suggestedNewDateRange: DateRange<DateTime>,
  ) => DateRange<DateTime>
  getDateRangePickerDayProps: ({
    day,
  }: {
    day: DateTime
  }) => DateRangePickerDaySlotProps
} => {
  const dateRange = params[0]
  const { outerLimits, rangeMaxSizeInDays } = params[1] ?? {}
  const upperOuterLimit = outerLimits?.upper
  const lowerOuterLimit = outerLimits?.lower

  const parseSuggestedNewDateRangeOnChange = (
    suggestedNewDateRange: DateRange<DateTime>,
  ): DateRange<DateTime> => {
    if (!suggestedNewDateRange[0] || !suggestedNewDateRange[1]) {
      return suggestedNewDateRange
    }
    if (
      isDateRangeLargerThanMaxSize(
        [suggestedNewDateRange[0], suggestedNewDateRange[1]],
        rangeMaxSizeInDays,
      )
    ) {
      // See https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/1522
      return [suggestedNewDateRange[0], null]
    }
    return suggestedNewDateRange
  }

  const getDateRangePickerDayProps = ({
    day,
  }: {
    day: DateTime
  }): DateRangePickerDaySlotProps => {
    if (!dateRange[0] || !dateRange[1]) {
      return {}
    }
    const dayBaseLineUnix = day.startOf('day').toMillis()
    const rangeStartBaseline = dateRange[0].startOf('day')
    const rangeEndBaseline = dateRange[1].startOf('day')
    const currentMaxRangeDayUnix = rangeStartBaseline
      .plus({ days: rangeMaxSizeInDays })
      .toMillis()
    const currentMinRangeDayUnix = rangeEndBaseline
      .minus({ days: rangeMaxSizeInDays })
      .toMillis()

    if (dayBaseLineUnix === currentMinRangeDayUnix) {
      // To make sure the minimum day is correctly styled
      return {
        isStartOfPreviewing: true,
      }
    }
    if (dayBaseLineUnix === currentMaxRangeDayUnix) {
      // To make sure the maximum day is correctly styled
      return {
        isEndOfPreviewing: true,
      }
    }

    if (
      dayBaseLineUnix < currentMinRangeDayUnix ||
      dayBaseLineUnix > currentMaxRangeDayUnix
    ) {
      return {
        // Make sure we disable previewing styling (dashed border around a day) user tries to select a day outside of current range limit.
        isPreviewing: false,
      }
    }

    return {} // Let the default styling be applied
  }

  if (dateRange[0] && dateRange[1]) {
    // If a range is already selected, we can allow the user to select any date within the outer limits.
    // If we don't do this, the user would be forced to select a day within the rangeMaxSizeInDays only (which would force them to clear the current range first)
    // See https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/1522
    return {
      min: lowerOuterLimit,
      max: upperOuterLimit,
      parseSuggestedNewDateRangeOnChange,
      getDateRangePickerDayProps,
    }
  }

  const { min, max } = getMinAndMaxDateTimesForSpecifiedDateRangeConfig(...params)
  return { min, max, parseSuggestedNewDateRangeOnChange, getDateRangePickerDayProps }
}
