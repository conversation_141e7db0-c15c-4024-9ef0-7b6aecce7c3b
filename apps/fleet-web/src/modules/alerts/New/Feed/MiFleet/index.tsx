import { useMemo, useState } from 'react'
import {
  Badge,
  Button,
  Chip,
  DataGrid,
  DateTimeRangePicker,
  GridActionsCellItem,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  Typography,
  useC<PERSON>backBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
} from '@karoo-ui/core'
import ClearIcon from '@mui/icons-material/ClearOutlined'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import MarkAsUnreadOutlinedIcon from '@mui/icons-material/MarkAsUnreadOutlined'
import MarkEmailReadIcon from '@mui/icons-material/MarkEmailReadOutlined'
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { DateTime } from 'luxon'
import { useHistory } from 'react-router'

import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { useMiFleetAlertConfigurationQuery } from 'src/modules/alerts/api/MiFleet/useMiFleetAlertConfigurationQuery'
import {
  useMiFleetAlertsQuery,
  type FetchMiFleetAlerts,
} from 'src/modules/alerts/api/MiFleet/useMiFleetAlertsQuery'
import { useUpdateMiFleetAlertMutation } from 'src/modules/alerts/api/MiFleet/useUpdateMiFleetAlertMutation'
import { COSTS } from 'src/modules/app/components/routes/costs'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import AlertsTabGroup from '../../components/AlertsTabGroup'
import DismissConfirmationModal from './DismissConfirmationModal'
import AlertsMiFleetFeedFiltersDrawer from './Drawer'
import { useAlertsMiFleetFeedFilters } from './Drawer/AlertsMiFleetFeedFiltersContext'

type Props = {
  onChangeSelectedTab: () => void
}

type DataGridRow = FetchMiFleetAlerts.Return['alerts'][number]

const AlertsMiFleetFeed = ({ onChangeSelectedTab }: Props) => {
  const apiRef = useGridApiRef()

  const history = useHistory()

  const alertsDefaultDateRange: [DateTime, DateTime] = [
    DateTime.local().minus({ hours: 8 }),
    DateTime.local(),
  ]

  const [selectedDateRange, setSelectedDateRange] =
    useState<[DateTime, DateTime]>(alertsDefaultDateRange)
  const [isFiltersDrawerOpen, setIsFiltersDrawerOpen] = useState(false)
  const [alertToDismiss, setAlertToDismiss] = useState<FetchMiFleetAlerts.AlertReturn>()

  const alertsFeedData = useMiFleetAlertsQuery({
    startDate: selectedDateRange[0].toFormat('yyyy-MM-dd HH:mm:ss'),
    endDate: selectedDateRange[1].toFormat('yyyy-MM-dd HH:mm:ss'),
  })

  const miFleetAlertConfigurationQuery = useMiFleetAlertConfigurationQuery()
  const updateMiFleetAlertMutation = useUpdateMiFleetAlertMutation()

  const { filters, setFilters, clearFilters } = useAlertsMiFleetFeedFilters()

  const hasFilters = useMemo(
    () =>
      filters.type.value !== 'all' ||
      filters.status.value !== 'all' ||
      filters.vehicles[0].value !== 'all' ||
      filters.driver.value !== 'all',
    [filters],
  )

  const numberOfFilters = useMemo(
    () =>
      (filters.type.value !== 'all' ? 1 : 0) +
      (filters.status.value !== 'all' ? 1 : 0) +
      (filters.vehicles[0].value !== 'all' ? 1 : 0) +
      (filters.driver.value !== 'all' ? 1 : 0),
    [filters],
  )

  const warningTypes = useMemo(() => {
    if (miFleetAlertConfigurationQuery.data?.warningTypes !== undefined) {
      return miFleetAlertConfigurationQuery.data.warningTypes.reduce<{
        options: Array<{ label: string; value: number }>
        byId: Record<number, { label: string; value: number }>
      }>(
        (acc, type) => {
          const warningType = {
            label: ctIntl.formatMessage({ id: type.description }),
            value: type.warningTypeId,
          }

          acc.options.push(warningType)
          acc.byId[type.warningTypeId] = warningType

          return acc
        },
        { options: [], byId: {} },
      )
    }

    return { options: [], byId: {} }
  }, [miFleetAlertConfigurationQuery.data?.warningTypes])

  const alertTypeFilterOptions = useMemo(
    () =>
      warningTypes.options
        .map((type) => ({
          name: type.label ?? '',
          value: type.value.toString(),
        }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [warningTypes.options],
  )

  const vehicleFilterOptions = useMemo(
    () =>
      miFleetAlertConfigurationQuery.data?.vehicles
        .map((vehicle) => ({
          name: vehicle.plate ?? '',
          value: vehicle.vehicle_id.toString(),
          label: vehicle.plate ?? '',
        }))
        .sort((a, b) => a.name.localeCompare(b.name)) ?? [],
    [miFleetAlertConfigurationQuery.data?.vehicles],
  )

  const driverFilterOptions = useMemo(
    () =>
      miFleetAlertConfigurationQuery.data?.drivers
        .map((driver) => ({
          name: driver.short_name ?? '',
          value: driver.driver_id.toString(),
        }))
        .sort((a, b) => a.name.localeCompare(b.name)) ?? [],
    [miFleetAlertConfigurationQuery.data?.drivers],
  )

  const allOption = {
    name: ctIntl.formatMessage({ id: 'All' }),
    value: 'all',
    label: ctIntl.formatMessage({ id: 'All' }),
  } as const

  const filteredAlertsFeedData = useMemo(() => {
    if (alertsFeedData.data === undefined) {
      return []
    }

    return hasFilters
      ? alertsFeedData.data.alerts.filter((alert) => {
          const { type, status, vehicles, driver } = filters

          if (
            type.value !== 'all' &&
            alert.warning_type_id.toString() !== type.value.toString()
          ) {
            return false
          }

          if (
            status.value !== 'all' &&
            ((status.value === 'unread' && alert.is_read) ||
              (status.value === 'dismissed' && !alert.is_dismissed) ||
              (status.value === 'active' && alert.is_dismissed))
          ) {
            return false
          }

          const selectedVehiclesValues = vehicles.map((v) => v.value)

          if (
            vehicles[0].value !== 'all' &&
            !selectedVehiclesValues.includes(alert?.vehicle_id?.toString() ?? '')
            // (alert.vehicle_id?.toString() ?? '') !== vehicles.value.toString()
          ) {
            return false
          }

          if (
            driver.value !== 'all' &&
            (alert.driver_id?.toString() ?? '') !== driver.value.toString()
          ) {
            return false
          }

          return true
        })
      : alertsFeedData.data.alerts
  }, [alertsFeedData.data, filters, hasFilters])

  const columnHelper = useDataGridColumnHelper<DataGridRow>({
    filterMode: 'client',
  })

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      columnHelper.dateTime({
        field: 'warningDate',
        headerName: ctIntl.formatMessage({ id: 'Date/Time' }),
        valueGetter: (_, row) => row.warning_date.raw,
        valueFormatter: (_, row) => row.warning_date.formatted,
      }),
      columnHelper.string(
        (_, row) => {
          const warningType = warningTypes.byId[row.warning_type_id]

          return warningType?.label ?? ''
        },
        {
          field: 'warningType',
          headerName: ctIntl.formatMessage({ id: 'Type' }),
          flex: 0.5,
        },
      ),
      columnHelper.string((_, row) => row.vehicle?.plate ?? '', {
        field: 'vehicle',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        minWidth: 150,
      }),
      columnHelper.string((_, row) => row.driver?.short_name ?? '', {
        field: 'driver',
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
        minWidth: 150,
      }),
      {
        valueGetter: (_, row) => row.message,
        field: 'message',
        headerName: ctIntl.formatMessage({ id: 'Message' }),
        flex: 1,
        resizable: false,
        minWidth: 300,
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({
          id: 'Actions',
        }),
        minWidth: 150,
        getActions: ({ row }) => [
          row.is_read ? (
            <Tooltip
              key="markAsUnread"
              title={ctIntl.formatMessage({ id: 'alerts.feed.mifleet.markAsUnread' })}
            >
              <GridActionsCellItem
                icon={<MarkAsUnreadOutlinedIcon />}
                label={ctIntl.formatMessage({
                  id: 'alerts.feed.mifleet.markAsUnread',
                })}
                onClick={() => {
                  updateMiFleetAlertMutation.mutate({
                    ...row,
                    is_read: false,
                  })
                }}
              />
            </Tooltip>
          ) : (
            <Tooltip
              key="markAsRead"
              title={ctIntl.formatMessage({ id: 'alerts.feed.mifleet.markAsRead' })}
            >
              <GridActionsCellItem
                icon={<MarkEmailReadIcon />}
                label={ctIntl.formatMessage({ id: 'alerts.feed.mifleet.markAsRead' })}
                onClick={() => {
                  updateMiFleetAlertMutation.mutate({
                    ...row,
                    is_read: true,
                  })
                }}
              />
            </Tooltip>
          ),
          <Tooltip
            key="dismiss"
            title={ctIntl.formatMessage({
              id: row.is_dismissed
                ? 'alerts.feed.mifleet.alertAlreadyDismissed'
                : 'Dismiss',
            })}
          >
            <span>
              <GridActionsCellItem
                icon={<ClearIcon />}
                disabled={row.is_dismissed}
                label={ctIntl.formatMessage({ id: 'Dismiss' })}
                onClick={() => {
                  setAlertToDismiss(row)
                }}
              />
            </span>
          </Tooltip>,
          <Tooltip
            key="view"
            title={ctIntl.formatMessage({
              id: row.document_id
                ? 'View Document'
                : 'alerts.feed.mifleet.viewDocument.button.tooltip.noDocumentAvailable',
            })}
          >
            <span>
              <GridActionsCellItem
                icon={<VisibilityIcon />}
                disabled={!row.document_id}
                label={ctIntl.formatMessage({ id: 'View Document' })}
                onClick={() =>
                  history.push({
                    pathname: `${COSTS.DOCUMENTS.path}/${row.document_id}`,
                  })
                }
              />
            </span>
          </Tooltip>,
        ],
      },
    ],
    [columnHelper, history, updateMiFleetAlertMutation, warningTypes.byId],
  )

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="AlertsMiFleetFeed"
        getRowId={useCallbackBranded((row: DataGridRow) => row.alert_id, [])}
        getRowHeight={useCallbackBranded(() => 'auto', [])}
        getRowClassName={({ row }) => (!row.is_read ? 'util-textBold' : '')}
        getEstimatedRowHeight={() => 100}
        apiRef={apiRef}
        columns={columns}
        rows={filteredAlertsFeedData}
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
          sorting: {
            sortModel: [{ field: 'eventDate', sort: 'desc' }],
          },
        }}
        loading={alertsFeedData.isFetching}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => (
            <DataStatePlaceholder
              label={
                hasFilters ? 'global.filters.noDataAvailable' : 'No data available'
              }
            />
          ),
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
            },
            slotProps: {
              right: { gap: 0 },
            },
            extraContent: {
              left: (
                <>
                  <AlertsTabGroup
                    value="MIFLEET"
                    onChange={onChangeSelectedTab}
                  />
                  <DateTimeRangePicker
                    disableFuture
                    closeOnSelect
                    value={selectedDateRange}
                    onChange={([start, end]) => {
                      if (start !== null && end !== null) {
                        setSelectedDateRange([start, end])
                      }
                    }}
                  />
                </>
              ),
              middle: (
                <IconButton onClick={() => setIsFiltersDrawerOpen(true)}>
                  <Badge
                    color="primary"
                    badgeContent={numberOfFilters}
                  >
                    <FilterListOutlinedIcon />
                  </Badge>
                </IconButton>
              ),
              right: (
                <Stack direction="row">
                  <Tooltip title={ctIntl.formatMessage({ id: 'Refresh' })}>
                    <span>
                      <IconButton
                        disabled={alertsFeedData.isFetching}
                        aria-label="refresh"
                        onClick={() => {
                          setSelectedDateRange(alertsDefaultDateRange)
                          alertsFeedData.refetch()
                        }}
                      >
                        <RefreshOutlinedIcon />
                      </IconButton>
                    </span>
                  </Tooltip>

                  <Tooltip title={ctIntl.formatMessage({ id: 'Download' })}>
                    <IconButton
                      aria-label="download"
                      onClick={() => {
                        apiRef.current?.exportDataAsCsv({
                          fileName: `${ctIntl.formatMessage({
                            id: 'MiFleet Alerts',
                          })} | ${selectedDateRange[0].toFormat(
                            'D',
                          )} - ${selectedDateRange[1].toFormat('D')}`,
                        })
                      }}
                    >
                      <DownloadOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              ),
              bottom: hasFilters && (
                <Stack
                  direction="row"
                  alignItems="center"
                  flexWrap="wrap"
                  gap={1}
                >
                  <Typography
                    variant="caption"
                    color="text.secondary"
                  >
                    {ctIntl.formatMessage({
                      id: 'Filters selected',
                    })}
                    :
                  </Typography>

                  {filters.status.value !== 'all' && (
                    <Chip
                      size="small"
                      variant="filled"
                      label={
                        <Stack direction="row">
                          <FilterChipLabel>
                            {ctIntl.formatMessage({ id: 'Status' })}
                          </FilterChipLabel>
                          {': '}
                          {ctIntl.formatMessage({ id: filters.status.name })}
                        </Stack>
                      }
                      onDelete={() => {
                        setFilters((prev) => ({
                          ...prev,
                          status: allOption,
                        }))
                      }}
                    />
                  )}

                  {filters.type.value !== 'all' && (
                    <Chip
                      size="small"
                      variant="filled"
                      label={
                        <Stack direction="row">
                          <FilterChipLabel>
                            {ctIntl.formatMessage({ id: 'Alert Type' })}
                          </FilterChipLabel>
                          {': '}
                          {filters.type.name}
                        </Stack>
                      }
                      onDelete={() => {
                        setFilters((prev) => ({
                          ...prev,
                          type: allOption,
                        }))
                      }}
                    />
                  )}

                  {filters.vehicles[0].value !== 'all' &&
                    filters.vehicles.map((vehicle) => (
                      <Chip
                        key={vehicle.label}
                        size="small"
                        variant="filled"
                        label={
                          <Stack direction="row">
                            <FilterChipLabel>
                              {ctIntl.formatMessage({ id: 'Vehicle' })}
                            </FilterChipLabel>
                            {': '}
                            {vehicle.label}
                          </Stack>
                        }
                        onDelete={() => {
                          if (filters.vehicles.length === 1) {
                            setFilters((prev) => ({
                              ...prev,
                              vehicles: [allOption],
                            }))
                          } else {
                            const newArray = filters.vehicles.filter(
                              (v) => v.value !== vehicle.value,
                            )
                            setFilters((prev) => ({
                              ...prev,
                              vehicles: newArray,
                            }))
                          }
                        }}
                      />
                    ))}

                  {filters.driver.value !== 'all' && (
                    <Chip
                      size="small"
                      variant="filled"
                      label={
                        <Stack direction="row">
                          <FilterChipLabel>
                            {ctIntl.formatMessage({ id: 'Driver' })}
                          </FilterChipLabel>
                          {': '}
                          {filters.driver.name}
                        </Stack>
                      }
                      onDelete={() => {
                        setFilters((prev) => ({
                          ...prev,
                          driver: allOption,
                        }))
                      }}
                    />
                  )}

                  <Button
                    variant="text"
                    size="small"
                    onClick={clearFilters}
                  >
                    {ctIntl.formatMessage({ id: 'Clear All' })}
                  </Button>
                </Stack>
              ),
            },
          }),
          pagination: { showFirstButton: true, showLastButton: true },
        }}
        sx={{
          '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': {
            py: 1,
          },
          '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': {
            py: 2,
          },
          '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': {
            py: 2.5,
          },
        }}
      />

      {isFiltersDrawerOpen && (
        <AlertsMiFleetFeedFiltersDrawer
          alertTypeFilterOptions={alertTypeFilterOptions}
          vehicleFilterOptions={vehicleFilterOptions}
          driverFilterOptions={driverFilterOptions}
          onClose={() => setIsFiltersDrawerOpen(false)}
        />
      )}

      {alertToDismiss !== undefined && (
        <DismissConfirmationModal
          alert={alertToDismiss}
          onClose={() => {
            setAlertToDismiss(undefined)
          }}
        />
      )}
    </>
  )
}

export default AlertsMiFleetFeed

const FilterChipLabel = styled('span')(({ theme }) => ({
  color: theme.palette.text.secondary,
}))
