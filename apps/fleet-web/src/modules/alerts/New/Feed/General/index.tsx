import { Fragment, useCallback, useContext, useMemo, useState } from 'react'
import { capitalize, isEmpty } from 'lodash'
import {
  Badge,
  Box,
  Button,
  Chip,
  DataGrid,
  DateTimeRangePicker,
  GridActionsCellItem,
  IconButton,
  LinearProgress,
  Menu,
  MenuItem,
  NativeLink,
  Stack,
  styled,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
} from '@karoo-ui/core'
import CampaignOutlinedIcon from '@mui/icons-material/CampaignOutlined'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import MapOutlinedIcon from '@mui/icons-material/MapOutlined'
import MoreVertOutlinedIcon from '@mui/icons-material/MoreVertOutlined'
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined'
import RssFeedOutlinedIcon from '@mui/icons-material/RssFeedOutlined'
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined'
import VideocamOutlinedIcon from '@mui/icons-material/VideocamOutlined'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import WbTwilightOutlinedIcon from '@mui/icons-material/WbTwilightOutlined'
import { DateTime } from 'luxon'
import { useIntl } from 'react-intl'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router'
import { match, P } from 'ts-pattern'

import { jumpToEvent } from 'duxs/map'
import { getAlertsNew } from 'duxs/user-sensitive-selectors'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import type { AlertEvent } from 'src/modules/alerts/api/types'
import { ContactType, SpecialContactType } from 'src/modules/alerts/utils'
import DriverWithChip from 'src/modules/app/components/driverChip/DriverWithChip'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useUserAvailableCameraTerminalTypes } from 'src/modules/vision/api/queries'
import { useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import { isTrue } from 'src/util-functions/validation'

import AlertsTabGroup from '../../components/AlertsTabGroup'
import { getActiveAlertEditPath } from '../../ManageAlertsSetup/General'
import { getReAlertDetailsModalPath } from '../../Revamp/AlertDetailsModal/utils'
import { AlertNotificationType } from '../../Revamp/schema'
import { RevampAlertContext } from '../../RevampAlertContext'
import { useAlertsFeedData, type FetchAlertsFeed } from '../useAlertsFeedData'
import { useAlertsGeneralFeedFilters } from './AlertsGeneralFeedFiltersContext'
import AlertsGeneralFeedFiltersDrawer from './Drawer'

type Props = {
  onChangeSelectedTab: () => void
}

const NOTIFICATION_OTHER_CHANNELS_POSSIBLE_VALUES = [
  'rss',
  'alertCenter',
  'coaching',
  'visionFootage',
  'controlRoom',
]

const AlertsGeneralFeed = ({ onChangeSelectedTab }: Props) => {
  const { formatList } = useIntl()

  const apiRef = useGridApiRef()
  const dispatch = useDispatch()

  const alertsNew = useTypedSelector(getAlertsNew)

  const alertsDefaultDateRange: [DateTime, DateTime] = [
    DateTime.local().minus({ hours: 8 }),
    DateTime.local(),
  ]

  const [selectedDateRange, setSelectedDateRange] =
    useState<[DateTime, DateTime]>(alertsDefaultDateRange)
  const [isFiltersDrawerOpen, setIsFiltersDrawerOpen] = useState(false)
  const [anchorSetupEl, setAnchorSetupEl] = useState<null | HTMLElement>(null)
  const [selectedRow, setSelectedRow] = useState<
    FetchAlertsFeed.Return['data'][number] | undefined
  >(undefined)

  const openSetupMenu = Boolean(anchorSetupEl)
  const history = useHistory()
  const context = useContext(RevampAlertContext)

  const alertsFeedData = useAlertsFeedData({
    startDate: selectedDateRange[0],
    endDate: selectedDateRange[1],
  })

  const { filters, setFilters, clearFilters } = useAlertsGeneralFeedFilters()

  const hasFilters = useMemo(
    () =>
      filters.selectedAlertType.value !== 'all' ||
      filters.selectedContactType.value !== 'all' ||
      filters.selectedVehicles[0].value !== 'all',
    [filters],
  )

  const numberOfFilters = useMemo(
    () =>
      (filters.selectedAlertType.value === 'all' ? 0 : 1) +
      (filters.selectedContactType.value === 'all' ? 0 : 1) +
      (filters.selectedVehicles[0].value === 'all' ? 0 : 1),
    [filters],
  )

  const matchNotification = (key: string, value: Record<string, any>) =>
    match(key)
      .with(
        P.union(
          AlertNotificationType.EMAIL,
          AlertNotificationType.LINE,
          AlertNotificationType.TELEGRAM,
        ),
        () => ({
          label: capitalize(key),
          MSG: isTrue(value.status) ? 'Sent' : value.message,
          icon: '',
          status: value.status,
          id: ContactType[key.toUpperCase() as keyof typeof ContactType],
        }),
      )
      .with(AlertNotificationType.WHATSAPP, () => ({
        label: 'WhatsApp',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: '',
        status: value.status,
        id: ContactType[key.toUpperCase() as keyof typeof ContactType],
      }))
      .with(AlertNotificationType.SMS, () => ({
        label: 'SMS',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: '',
        status: value.status,
        id: ContactType[key.toUpperCase() as keyof typeof ContactType],
      }))
      .with(AlertNotificationType.RSS, () => ({
        label: 'RSS',
        MSG: isTrue(value.status)
          ? 'alerts.feed.fleet.alert.status.processed'
          : value.message,
        icon: (
          <RssFeedOutlinedIcon
            fontSize="small"
            sx={{
              color: 'action.active',
            }}
          />
        ),
        status: value.status,
        id: ContactType.RSS,
      }))
      .with(AlertNotificationType.ALERT_CENTER, () => ({
        label: 'global.module.alertCenter',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: (
          <CampaignOutlinedIcon
            fontSize="small"
            sx={{
              color: 'action.active',
            }}
          />
        ),
        status: value.status,
        id: ContactType.ALERT_CENTER,
      }))
      .with(AlertNotificationType.COACHING, () => ({
        label: 'global.module.coaching',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: (
          <SchoolOutlinedIcon
            fontSize="small"
            sx={{
              color: 'action.active',
            }}
          />
        ),
        status: value.status,
        id: SpecialContactType.COACHING,
      }))
      .with(AlertNotificationType.VISION_FOOTAGE, () => ({
        label: 'alert.contactType.visionFootage',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: (
          <VideocamOutlinedIcon
            fontSize="small"
            sx={{
              color: 'action.active',
            }}
          />
        ),
        status: value.status,
        id: SpecialContactType.COACHING,
      }))
      .with(AlertNotificationType.CONTROL_ROOM, () => ({
        label: 'alert.contactType.camsControlRoom',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: (
          <WbTwilightOutlinedIcon
            fontSize="small"
            sx={{
              color: 'action.active',
            }}
          />
        ),
        status: value.status,
        id: ContactType.CAMS_CONTROL_ROOM,
      }))
      .with(AlertNotificationType.PUSH_NOTIFICATIONS, () => ({
        label: 'alert.contactType.pushNotifications',
        MSG: isTrue(value.status) ? 'Sent' : value.message,
        icon: '',
        status: value.status,
        id: SpecialContactType.PUSH_NOTIFICATIONS,
      }))
      .otherwise(() => ({
        label: '',
        MSG: '',
        icon: '',
        status: true,
        id: '',
      }))

  const filteredAlertsFeedData = useMemo(
    () =>
      hasFilters
        ? alertsFeedData.data.filter((alert) => {
            const { selectedAlertType, selectedContactType, selectedVehicles } = filters

            if (
              selectedAlertType.value !== 'all' &&
              (alert.type === 'alert' && alert.triggerDescription) !==
                selectedAlertType.value
            ) {
              return false
            }

            const alertTypeValues = Object.entries(alert.notificationContact)
              .filter(([_, value]) => !isEmpty(value))
              .map(([key, value]) => matchNotification(key, value).id)
            if (
              selectedContactType.value !== 'all' &&
              alert.type === 'alert' &&
              !alertTypeValues.includes(selectedContactType.value)
            ) {
              return false
            }

            const selectedVehiclesValues = selectedVehicles.map((v) => v.value)

            if (
              selectedVehicles[0].value !== 'all' &&
              !selectedVehiclesValues.includes(alert.vehicleId)
            ) {
              return false
            }

            return true
          })
        : alertsFeedData.data,
    [alertsFeedData.data, filters, hasFilters],
  )
  const userAvailableCameraTerminalTypes = useUserAvailableCameraTerminalTypes()

  const showFacilitiesColumn = useMemo(
    () =>
      match(userAvailableCameraTerminalTypes)
        .with({ status: 'pending' }, () => false)
        .with({ status: 'error' }, () => false)
        .with(
          { status: 'success' },
          ({ data }) => data === 'FACILITIES' || data === 'FACILITIES_AND_VEHICLES',
        )
        .exhaustive(),
    [userAvailableCameraTerminalTypes],
  )

  const vehicleFilterOptions = useMemo(() => {
    const uniqueVehiclesMap = new Map()

    for (const { vehicleId, registration } of alertsFeedData.data) {
      if (!uniqueVehiclesMap.has(vehicleId)) {
        uniqueVehiclesMap.set(vehicleId, registration)
      }
    }

    return Array.from(uniqueVehiclesMap, ([value, name]) => ({
      value,
      name,
      label: name,
    })).sort((a, b) => a.name.localeCompare(b.name))
  }, [alertsFeedData.data])

  const allOption = {
    name: ctIntl.formatMessage({ id: 'All' }),
    value: 'all',
    label: ctIntl.formatMessage({ id: 'All' }),
  }

  const notificationDisplay = useCallback((data: AlertEvent['notificationContact']) => {
    const filteredNotifications = Object.entries(data).filter(
      ([key, value]) =>
        !NOTIFICATION_OTHER_CHANNELS_POSSIBLE_VALUES.includes(key) && !isEmpty(value),
    )
    return filteredNotifications.map(([key, value], index) => {
      const notifications = matchNotification(key, value)
      return (
        <Typography
          noWrap
          overflow="unset"
          sx={{ display: 'flex', alignItems: 'center', gap: 0.3 }}
          key={index as number}
        >
          {notifications.status ? (
            <CheckOutlinedIcon
              fontSize="small"
              sx={{
                color: 'success.main',
              }}
            />
          ) : (
            <WarningAmberOutlinedIcon
              fontSize="small"
              sx={{
                color: 'warning.main',
              }}
            />
          )}
          {ctIntl.formatMessage({ id: notifications.label })}
          {filteredNotifications.length > index + 1 ? ',' : ''}
        </Typography>
      )
    })
  }, [])

  const notificationChannelDisplay = useCallback(
    (data: AlertEvent['notificationContact']) => {
      const filteredNotifications = Object.entries(data).filter(
        ([key, value]) =>
          NOTIFICATION_OTHER_CHANNELS_POSSIBLE_VALUES.includes(key) && !isEmpty(value),
      )
      return filteredNotifications.map(([key, value], index) => {
        const notifications = matchNotification(key, value)
        return (
          <Typography
            noWrap
            overflow="unset"
            sx={{ display: 'flex', alignItems: 'center', gap: 0.3 }}
            key={index as number}
          >
            {notifications.status ? (
              <CheckOutlinedIcon
                fontSize="small"
                sx={{
                  color: 'success.main',
                }}
              />
            ) : (
              <WarningAmberOutlinedIcon
                fontSize="small"
                sx={{
                  color: 'warning.main',
                }}
              />
            )}
            {ctIntl.formatMessage({ id: notifications.label })}
            {notifications.icon}
            {filteredNotifications.length > index + 1 ? ',' : ''}
          </Typography>
        )
      })
    },
    [],
  )

  const notificationTooltip = useCallback(
    (data: AlertEvent['notificationContact']) => (
      <Box
        sx={{
          width: 'max-content',
          display: 'grid',
          alignItems: 'center',
          gridTemplateColumns: 'minmax(20%, 1fr) 3fr',
          columnGap: 2,
          rowGap: 0.7,
          '.MuiSvgIcon-root': { width: '15px !important', height: 'auto' },
        }}
      >
        {Object.entries(data)
          .filter(([_, value]) => !isEmpty(value))
          .map(([key, value], index) => {
            const notifications = matchNotification(key, value)
            return (
              <Fragment key={index as number}>
                <Typography
                  noWrap
                  overflow="unset"
                  sx={{ textAlign: 'right' }}
                >
                  {ctIntl.formatMessage({ id: notifications.label })}:
                </Typography>
                <Typography
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                  }}
                >
                  {notifications.status ? (
                    <CheckOutlinedIcon
                      sx={{
                        color: 'success.main',
                        mr: '5px',
                      }}
                    />
                  ) : (
                    <WarningAmberOutlinedIcon
                      sx={{
                        color: 'warning.main',
                        mr: '5px',
                      }}
                    />
                  )}
                  {ctIntl.formatMessage({ id: notifications.MSG || '' })}
                </Typography>
              </Fragment>
            )
          })}
      </Box>
    ),
    [],
  )

  const handleClickOnMapButton = useCallback(
    (row: FetchAlertsFeed.Return['data'][number] | undefined) => {
      if (row) dispatch(jumpToEvent(row.eventDate.raw, row.vehicleId))
    },
    [dispatch],
  )

  const handleOpenSetupMenu = (
    event: React.MouseEvent<HTMLButtonElement>,
    row: FetchAlertsFeed.Return['data'][number],
  ) => {
    setAnchorSetupEl(event.currentTarget)
    setSelectedRow(row)
  }
  const handleCloseSetupMenu = () => {
    setAnchorSetupEl(null)
    setSelectedRow(undefined)
  }

  const handleClickOnSetupButton = (
    row: FetchAlertsFeed.Return['data'][number] | undefined,
  ) => {
    if (row) {
      history.push(
        context && context.newAlertEnabled
          ? getReAlertDetailsModalPath(history.location, {
              alertDetails: {
                params: {
                  action: 'edit',
                  alertId: row.alertId,
                  parentId: row.parentId,
                  mifleetId: null,
                },
              },
            })
          : getActiveAlertEditPath(row.triggers, row.id, history.location),
      )
    }
  }
  const columnHelper = useDataGridColumnHelper<FetchAlertsFeed.Return['data'][number]>({
    filterMode: 'client',
  })
  const columns = useMemo(
    (): Array<GridColDef<FetchAlertsFeed.Return['data'][number]>> => [
      columnHelper.dateTime({
        field: 'eventDate',
        headerName: ctIntl.formatMessage({ id: 'Incident Time' }),
        valueGetter: (_, row) => row.eventDate.raw,
        valueFormatter: (_, row) => row.eventDate.formatted,
      }),
      columnHelper.string((_, row) => row.groupDescription, {
        field: 'groupDescription',
        headerName: ctIntl.formatMessage({ id: 'alerts.modal.section.name' }),
        minWidth: 190,
      }),
      columnHelper.string(
        (_, row) =>
          row.type === 'alert' && row.triggerDescription
            ? ctIntl.formatMessage({
                id: `alert.${row.triggerDescription}`,
                defaultMessage: ctIntl.formatMessage({
                  id: row.triggerDescription,
                }),
              })
            : '',
        {
          field: 'triggerDescription',
          headerName: ctIntl.formatMessage({ id: 'Type' }),
          minWidth: 170,
        },
      ),
      ...(showFacilitiesColumn
        ? [
            columnHelper.string(
              (_, row) =>
                row.type === 'alert' && row.isFacility ? null : row.registration,
              {
                field: 'device',
                headerName: ctIntl.formatMessage({
                  id: 'vision.global.columnHeader.device',
                }),
                minWidth: 150,
              },
            ),
          ]
        : []),
      columnHelper.string(
        (_, row) => (row.type === 'alert' && row.isFacility ? null : row.registration),
        {
          field: 'registration',
          headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
          minWidth: 100,
        },
      ),
      columnHelper.string(
        (_, row) =>
          row.linkedDriver && row.linkedDriver.driverName.status === 'PUBLIC'
            ? row.linkedDriver.driverName.name
            : null,
        {
          field: 'linkedDriver',
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          minWidth: 185,
          renderCell: ({ row }) => (
            <DriverWithChip
              driverName={row.linkedDriver.driverName}
              driverId={row.linkedDriver.id}
              linkingMethod={row.linkedDriver.linkingMethod}
            />
          ),
        },
      ),
      columnHelper.string((_, row) => row.geofence, {
        field: 'geofence',
        headerName: ctIntl.formatMessage({ id: 'Geofence' }),
        minWidth: 190,
      }),
      {
        valueGetter: (_, row) => {
          if (row.notificationMsg) {
            return row.notificationMsg.replaceAll(
              // eslint-disable-next-line sonarjs/slow-regex
              /<([^>]+?)([^>]*?)>(.*?)<\/\1>/gi, // Remove HTML tags
              '',
            )
          }

          return ''
        },
        field: 'notificationMsg',
        headerName: ctIntl.formatMessage({ id: 'Message' }),
        flex: 1,
        minWidth: 185,
        resizable: false,
        renderCell: ({ value }) => <ExpandableMessageCell message={value} />,
      },
      columnHelper.string(
        (_, row) => {
          const test = Object.entries(row.notificationContact)
            .filter(([_, value]) => !isEmpty(value))
            .map(([key, value]) => {
              const notifications = matchNotification(key, value)
              return ctIntl.formatMessage({ id: notifications.label })
            })
          return formatList(test, {
            type: 'unit',
          })
        },
        {
          field: 'notificationContact',
          headerName: `${ctIntl.formatMessage({
            id: 'Notifications',
          })}/${ctIntl.formatMessage({
            id: 'Actions',
          })}`,
          minWidth: 250,
          renderCell: ({ row }) => (
            <Tooltip
              title={notificationTooltip(row.notificationContact)}
              slotProps={{
                tooltip: {
                  sx: {
                    minWidth: '490px',
                    width: 'max-content',
                    py: 1,
                    pl: 5,
                  },
                },
              }}
            >
              <Stack gap={0.7}>
                <Stack
                  flexWrap="wrap"
                  direction={'row'}
                  alignItems={'center'}
                  gap={0.7}
                >
                  {notificationDisplay(row.notificationContact)}
                </Stack>
                <Stack
                  flexWrap="wrap"
                  direction="row"
                  alignItems={'center'}
                  gap={0.7}
                >
                  {notificationChannelDisplay(row.notificationContact)}
                </Stack>
              </Stack>
            </Tooltip>
          ),
        },
      ),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        getActions: ({ row }) => [
          <Tooltip
            title={ctIntl.formatMessage({ id: 'map.poi.viewOnMap' })}
            key="view-map"
          >
            <GridActionsCellItem
              id="action-map-button"
              icon={<MapOutlinedIcon />}
              label={ctIntl.formatMessage({ id: 'map.poi.viewOnMap' })}
              onClick={() => handleClickOnMapButton(row)}
            />
          </Tooltip>,
          <Tooltip
            key="setup"
            title={ctIntl.formatMessage({
              id: match([alertsNew, row.groupIsDeleted])
                .with([false, P.boolean], () => 'global.noPermission')
                .with([true, false], () => 'View alert setup')
                .with([true, true], () => 'alerts.feed.fleet.alert.deleted')
                .exhaustive(),
            })}
          >
            <span>
              <GridActionsCellItem
                id="action-setup-button"
                aria-controls={openSetupMenu ? 'action-setup-menu' : undefined}
                icon={<MoreVertOutlinedIcon />}
                label={ctIntl.formatMessage({ id: 'View alert setup' })}
                disabled={!alertsNew || row.groupIsDeleted}
                onClick={(event) => handleOpenSetupMenu(event, row)}
              />
            </span>
          </Tooltip>,
        ],
      },
    ],
    [
      columnHelper,
      showFacilitiesColumn,
      formatList,
      notificationTooltip,
      notificationDisplay,
      notificationChannelDisplay,
      alertsNew,
      openSetupMenu,
      handleClickOnMapButton,
    ],
  )

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="AlertsFeed"
        getRowHeight={useCallbackBranded(() => 'auto', [])}
        getEstimatedRowHeight={() => 100}
        apiRef={apiRef}
        columns={columns}
        rows={filteredAlertsFeedData}
        checkboxSelection
        pagination
        pageSizeOptions={[25, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: { pageSize: 25, page: 0 },
          },
          sorting: {
            sortModel: [{ field: 'eventDate', sort: 'desc' }],
          },
        }}
        loading={alertsFeedData.isFetching}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => (
            <DataStatePlaceholder
              label={
                hasFilters ? 'global.filters.noDataAvailable' : 'No data available'
              }
            />
          ),
        }}
        slotProps={{
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: { show: true },
              settingsButton: { show: true },
            },
            slotProps: {
              right: { gap: 0 },
            },
            extraContent: {
              left: (
                <>
                  <AlertsTabGroup
                    value="GENERAL"
                    onChange={onChangeSelectedTab}
                  />
                  <DateTimeRangePicker
                    disableFuture
                    closeOnSelect
                    value={selectedDateRange}
                    onChange={([start, end]) => {
                      if (start !== null && end !== null) {
                        setSelectedDateRange([start, end])
                      }
                    }}
                  />
                </>
              ),
              middle: (
                <IconButton onClick={() => setIsFiltersDrawerOpen(true)}>
                  <Badge
                    color="primary"
                    badgeContent={numberOfFilters}
                  >
                    <FilterListOutlinedIcon />
                  </Badge>
                </IconButton>
              ),
              right: (
                <Stack direction="row">
                  <Tooltip title={ctIntl.formatMessage({ id: 'Refresh' })}>
                    <span>
                      <IconButton
                        disabled={alertsFeedData.isFetching}
                        onClick={() => {
                          setSelectedDateRange(alertsDefaultDateRange)
                          alertsFeedData.refetch()
                        }}
                      >
                        <RefreshOutlinedIcon />
                      </IconButton>
                    </span>
                  </Tooltip>

                  <Tooltip title={ctIntl.formatMessage({ id: 'Download' })}>
                    <IconButton
                      onClick={() => {
                        apiRef.current?.exportDataAsCsv({
                          fileName: `${ctIntl.formatMessage({
                            id: 'Alerts',
                          })} - ${selectedDateRange[0].toFormat(
                            'D',
                          )} - ${selectedDateRange[1].toFormat('D')}`,
                          utf8WithBom: true,
                        })
                      }}
                    >
                      <DownloadOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              ),
              bottom: hasFilters && (
                <Stack
                  direction="row"
                  alignItems="center"
                  flexWrap="wrap"
                  gap={1}
                >
                  <Typography
                    variant="caption"
                    color="text.secondary"
                  >
                    {ctIntl.formatMessage({
                      id: 'Filters selected',
                    })}
                    :
                  </Typography>

                  {filters.selectedAlertType.value !== 'all' && (
                    <Chip
                      size="small"
                      variant="filled"
                      label={
                        <Stack direction="row">
                          <FilterChipLabel>
                            {ctIntl.formatMessage({
                              id: 'Alert Type',
                            })}
                          </FilterChipLabel>
                          {': '}
                          {filters.selectedAlertType.name}
                        </Stack>
                      }
                      onDelete={() => {
                        setFilters((prev) => ({
                          ...prev,
                          selectedAlertType: allOption,
                        }))
                      }}
                    />
                  )}

                  {filters.selectedContactType.value !== 'all' && (
                    <Chip
                      size="small"
                      variant="filled"
                      label={
                        <Stack direction="row">
                          <FilterChipLabel>
                            {ctIntl.formatMessage({
                              id: 'Contact Type',
                            })}
                          </FilterChipLabel>
                          {': '}
                          {filters.selectedContactType.name}
                        </Stack>
                      }
                      onDelete={() => {
                        setFilters((prev) => ({
                          ...prev,
                          selectedContactType: allOption,
                        }))
                      }}
                    />
                  )}
                  {filters.selectedVehicles[0].value !== 'all' &&
                    filters.selectedVehicles.map((vehicle) => (
                      <Chip
                        key={vehicle.label}
                        size="small"
                        variant="filled"
                        label={
                          <Stack direction="row">
                            <FilterChipLabel>
                              {ctIntl.formatMessage({
                                id: 'Vehicle',
                              })}
                            </FilterChipLabel>
                            {': '}
                            {vehicle.label}
                          </Stack>
                        }
                        onDelete={() => {
                          if (filters.selectedVehicles.length === 1) {
                            setFilters((prev) => ({
                              ...prev,
                              selectedVehicles: [allOption],
                            }))
                          } else {
                            const newArray = filters.selectedVehicles.filter(
                              (v) => v.value !== vehicle.value,
                            )
                            setFilters((prev) => ({
                              ...prev,
                              selectedVehicles: newArray,
                            }))
                          }
                        }}
                      />
                    ))}

                  <Button
                    variant="text"
                    size="small"
                    onClick={clearFilters}
                  >
                    {ctIntl.formatMessage({ id: 'Clear All' })}
                  </Button>
                </Stack>
              ),
            },
          }),
          pagination: { showFirstButton: true, showLastButton: true },
        }}
        sx={{
          '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': {
            py: 1,
          },
          '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': {
            py: 2,
          },
          '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': {
            py: 2.5,
          },
        }}
      />
      {isFiltersDrawerOpen && (
        <AlertsGeneralFeedFiltersDrawer
          vehicleFilterOptions={vehicleFilterOptions}
          onClose={() => setIsFiltersDrawerOpen(false)}
        />
      )}
      <Menu
        id="action-setup-menu"
        anchorEl={anchorSetupEl}
        open={openSetupMenu}
        onClose={handleCloseSetupMenu}
        MenuListProps={{
          'aria-labelledby': 'action-setup-button',
        }}
      >
        <MenuItem onClick={() => handleClickOnSetupButton(selectedRow)}>
          {ctIntl.formatMessage({ id: 'View alert setup' })}
        </MenuItem>
      </Menu>
    </>
  )
}

export default AlertsGeneralFeed

const MAX_MESSAGE_LENGTH = 200
const ExpandableMessageCell = ({ message }: { message: string }) => {
  const [expanded, setExpanded] = useState(false)
  const isMessageLong = message.length > MAX_MESSAGE_LENGTH

  return (
    <div>
      <Typography
        variant="body2"
        sx={(theme) => ({ color: theme.palette.text.secondary })}
      >
        {expanded
          ? message
          : `${message.slice(0, MAX_MESSAGE_LENGTH)}${isMessageLong ? '...' : ''}`}
      </Typography>
      {isMessageLong && (
        <NativeLink
          type="button"
          component="button"
          sx={{ fontSize: 'inherit' }}
          onClick={() => setExpanded((prev) => !prev)}
        >
          {ctIntl.formatMessage({ id: expanded ? 'Show less' : 'View more' })}
        </NativeLink>
      )}
    </div>
  )
}

const FilterChipLabel = styled('span')(({ theme }) => ({
  color: theme.palette.text.secondary,
}))
