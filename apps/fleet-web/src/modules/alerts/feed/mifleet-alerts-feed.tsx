import { memo, useEffect, useMemo, useState } from 'react'
import { debounce } from 'lodash'
import {
  Box,
  Button,
  Checkbox,
  DataGrid,
  DateRangePicker,
  GridToolbarStandardOld,
  LinearProgress,
  MenuItem,
  styled,
  TextField,
  Tooltip,
  useCallbackBranded,
  useDataGridDateColumns,
  useSearchTextField,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import FileDownloadIcon from '@mui/icons-material/FileDownload'
import RefreshIcon from '@mui/icons-material/Refresh'
import VisibilityIcon from '@mui/icons-material/Visibility'
import type { History } from 'history'
import { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router'

import {
  actions as costsActiveAlertsActions,
  selectors as costsActiveAlertsSelectors,
} from 'duxs/mifleet/alerts/active-alerts'
import { actions, selectors } from 'duxs/mifleet/alerts/alerts'
import { getSettings } from 'duxs/user'
import { useModal } from 'src/hooks'
import { COSTS } from 'src/modules/app/components/routes/costs'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import type { FixMeAny } from 'src/types'
import type { CostsAlert } from 'src/types/api/mifleet/alerts'
import { ctIntl } from 'src/util-components/ctIntl'
import { DeleteModal } from 'src/util-components/delete-modal'
import { jsonCSVify } from 'src/util-functions/export-utils'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import { getAlertsSetupPath } from '../setup'

const { fetchCostsAlerts, updateCostsAlert, dismissCostsAlert } = actions
const style = {
  textField: {
    width: '180px',
    input: {
      paddingTop: '3.5px',
      paddingBottom: '3.5px',
    },
    '@media (max-width: 1370px)': {
      width: '130px',
    },
  },
}

const alertOptions = [
  { name: ctIntl.formatMessage({ id: 'All' }), value: 'all' },
  { name: ctIntl.formatMessage({ id: 'Active' }), value: 'active' },
  { name: ctIntl.formatMessage({ id: 'Dismissed' }), value: 'dismissed' },
  { name: ctIntl.formatMessage({ id: 'Unread' }), value: 'unread' },
]

const {
  getCostsAlerts,
  isCostsAlertsLoading,
  getCostsAlertsVehicles,
  getCostsAlertsDrivers,
} = selectors
const { fetchCostsAlertTypes } = costsActiveAlertsActions
const { getCostsAlertTypesOptions } = costsActiveAlertsSelectors

type Props = ReduxProps & DispatchProp & RouteComponentProps & { history: History }

function MiFleetAlertsList({
  // Store
  alerts,
  costsAlertTypesOptions,
  isLoading,
  vehicles,
  drivers,
  hasCosts,
  history,
  // Redux
  dispatch,
}: Props) {
  const { createDateTimeColumn } = useDataGridDateColumns({
    filterMode: 'client',
  })
  const searchProps = useSearchTextField('')
  const [dateFilter, setDateFilter] = useState<DateRange<FixMeAny>>([
    DateTime.local().minus({ days: 1 }),
    DateTime.local(),
  ])
  const [alertOption, setAlertOption] = useState<string>('active')
  const [isDeleteModalOpen, { close, open, data: alertIdToDismiss }] =
    useModal<string>()

  useEffect(() => {
    dispatch(
      fetchCostsAlerts({
        startDate: dateFilter[0]?.toFormat('D'),
        endDate: dateFilter[1]?.toFormat('D'),
      }),
    )
  }, [dateFilter, dispatch])

  useEffect(() => {
    dispatch(fetchCostsAlertTypes())
  }, [dispatch])

  const debouncedFetchCostsAlerts = debounce(
    () =>
      dispatch(
        fetchCostsAlerts({
          startDate: dateFilter[0]?.toFormat('D'),
          endDate: dateFilter[1]?.toFormat('D'),
        }),
      ),
    500,
  )

  const handleModalDeleteClick = () => {
    if (alertIdToDismiss) {
      dispatch(dismissCostsAlert(alertIdToDismiss))
    }

    close()
  }

  const handleRefresh = () => debouncedFetchCostsAlerts()

  const handleDownload = () => {
    const data = filteredAlerts.map((alert: CostsAlert) => {
      const vehicle = vehicles.find((v: FixMeAny) => v.value === alert.vehicleId)

      return {
        date: alert.warningDate.date
          ? DateTime.fromSQL(alert.warningDate.date).toFormat('D')
          : '',
        type: alert.warningDescription,
        vehicle: vehicle ? vehicle.name : '',
        message: alert.message,
      }
    })

    jsonCSVify(data)
  }

  const vehicleFilterOptions = useMemo(
    () =>
      vehicles.map((vehicle) => ({
        label: vehicle.label,
        value: vehicle.label,
      })),
    [vehicles],
  )
  // Table
  const tableColumns = useMemo((): Array<GridColDef<ReduxProps['alerts'][0]>> => {
    const markAsRead = (costsAlert: CostsAlert) =>
      dispatch(
        updateCostsAlert({
          ...costsAlert,
          isRead: !costsAlert.isRead,
        }),
      )

    return [
      createDateTimeColumn({
        headerName: ctIntl.formatMessage({ id: 'Date/Time' }),
        field: 'warningDate',
        flex: 1,
        minWidth: 150,
        valueGetter: (_, row) =>
          row.warningDate ? new Date(row.warningDate.date) : null,
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Type' }),
        field: 'warningType',
        flex: 1,
        type: 'singleSelect',
        valueOptions: costsAlertTypesOptions,
        valueGetter: (_, row) => {
          const costs = costsAlertTypesOptions.find(
            (c: { id: number }) => c.id === Number(row.warningTypeId),
          )
          if (costs) {
            return costs.label
          }
          return ''
        },
        minWidth: 150,
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'vehicleId',
        flex: 1,
        type: 'singleSelect',
        valueOptions: vehicleFilterOptions,
        valueGetter: (_, row) => {
          const vehicle = vehicles.find((v: FixMeAny) => v.value === row.vehicleId)
          if (vehicle) {
            return vehicle.name
          }

          return ''
        },
        minWidth: 150,
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Driver' }),
        field: 'driverId',
        flex: 1,
        valueGetter: (_, row) => {
          const driver = drivers.find((v: FixMeAny) => v.value === row.driverId)
          return driver ? driver.name : ''
        },
        minWidth: 150,
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Message' }),
        field: 'message',
        flex: 1,
        valueGetter: (_, row) => row.message,
        minWidth: 200,
      },
      {
        field: 'Mark_read',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Mark as Read' }),
        width: 110,
        valueGetter: (_, row) => row.isRead,
        renderCell: ({ row }) => (
          <Checkbox
            onChange={() => {
              markAsRead(row)
            }}
            checked={row.isRead}
          />
        ),
      },
      {
        field: 'Dismiss',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Dismiss' }),
        width: 100,
        valueGetter: (_, row) => row.alertId,
        renderCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage({
              id: row.isDismissed ? 'Alert already dismissed' : 'Dismiss Alert',
            })}
          >
            <StyledButton
              disabled={row.isDismissed}
              onClick={(_) => open(row.alertId)}
              startIcon={<CloseIcon />}
              color="inherit"
            />
          </Tooltip>
        ),
      },
      {
        headerName: ctIntl.formatMessage({ id: 'View Document' }),
        field: 'View_Document',
        cellClassName: 'Regulatory-centeredItem',
        width: 140,
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.documentId),
        renderCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage({
              id: row.documentId
                ? 'View Document'
                : 'No document available for this alert',
            })}
          >
            <StyledButton
              startIcon={<VisibilityIcon />}
              color="inherit"
              size="small"
              onClick={() =>
                history.push({
                  pathname: `${COSTS.DOCUMENTS.path}/${row.documentId}`,
                })
              }
              disabled={!row.documentId}
            />
          </Tooltip>
        ),
      },
    ]
  }, [
    costsAlertTypesOptions,
    createDateTimeColumn,
    dispatch,
    drivers,
    history,
    open,
    vehicleFilterOptions,
    vehicles,
  ])

  const filteredAlerts = useMemo(() => {
    let items: Array<FixMeAny> = alerts
    const searchFilters: Filters<ReduxProps['alerts'][0]> = {
      search: [
        (u) => u.message?.toString(),
        (u) =>
          costsAlertTypesOptions.find(
            (c: { id: number }) => c.id === Number(u.warningTypeId),
          )?.label,
        (u) => vehicles.find((v: FixMeAny) => v.value === u.vehicleId)?.name,
        (u) => drivers.find((d: FixMeAny) => d.value === u.driverId)?.name,
      ],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    items = items.filter((category) =>
      itemMatchesWithTextAndFilters(category, searchFilters),
    )
    items = items.filter(
      (alert: CostsAlert) =>
        alertOption === 'all' ||
        (alertOption === 'active' && !alert.isDismissed) ||
        (alertOption === 'dismissed' && alert.isDismissed) ||
        (alertOption === 'unread' && !alert.isRead),
    )
    return items
  }, [
    alertOption,
    alerts,
    costsAlertTypesOptions,
    drivers,
    searchProps.value,
    vehicles,
  ])

  const tableData = filteredAlerts

  const onKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    e.preventDefault()
  }
  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        dataGridId="mifleetAlertsFeed"
        disableRowSelectionOnClick
        loading={isLoading}
        pagination
        rows={tableData}
        getRowId={useCallbackBranded((row: ReduxProps['alerts'][0]) => row.alertId, [])}
        columns={tableColumns}
        autoPageSize
        getRowClassName={({ row }) => (!row.isRead ? 'util-textBold' : '')}
        slots={{
          toolbar: GridToolbarStandardOld,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: GridToolbarStandardOld.createProps({
            SearchTextFieldProps: searchProps,
            gridToolbarLeftContent: (
              <>
                <DateRangePicker
                  value={dateFilter}
                  onChange={(newValue: FixMeAny) => {
                    setDateFilter(newValue)
                  }}
                  slotProps={{
                    textField: {
                      onKeyDown: onKeyDown,
                      sx: style.textField,
                    },
                  }}
                />
                <TextField
                  select
                  label={ctIntl.formatMessage({
                    id: 'Alerts',
                  })}
                  size="small"
                  value={alertOption}
                  onChange={(e) => setAlertOption(e.target.value)}
                  slotProps={{
                    input: {
                      sx: {
                        '.MuiSelect-select': {
                          paddingTop: '3.5px',
                          paddingBottom: '3.5px',
                        },
                      },
                    },
                  }}
                >
                  {alertOptions.map((item) => (
                    <MenuItem
                      value={item.value}
                      key={item.value}
                    >
                      {item.name}
                    </MenuItem>
                  ))}
                </TextField>
              </>
            ),
            gridToolbarRightContent: (
              <RightToolbar>
                <Button
                  variant="outlined"
                  onClick={() => handleRefresh()}
                  color="inherit"
                  size="small"
                  sx={{ minWidth: 'auto' }}
                >
                  <RefreshIcon />
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => handleDownload()}
                  color="inherit"
                  size="small"
                  sx={{ minWidth: 'auto' }}
                >
                  <FileDownloadIcon />
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={() =>
                    history.push({ pathname: getAlertsSetupPath('MIFLEET') })
                  }
                  size="small"
                  disabled={!hasCosts}
                >
                  {ctIntl.formatMessage({
                    id: 'Add Alert',
                  })}
                </Button>
              </RightToolbar>
            ),
          }),
        }}
      />
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={close}
        onDelete={handleModalDeleteClick}
        warningMessageId="Warning! You're about to dismiss an alert."
        deleteLabel="Dismiss"
      />
    </>
  )
}

const mapStateToProps = (state: AppState) => {
  const { costs } = getSettings(state)

  return {
    alerts: getCostsAlerts(state),
    costsAlertTypesOptions: [
      ...(getCostsAlertTypesOptions(state) as Array<{ name: string; value: string }>)
        .map((f) => ({
          label: ctIntl.formatMessage({ id: f.name }),
          value: ctIntl.formatMessage({ id: f.name }),
          id: Number(f.value),
        }))
        .sort((a, b) => a.label.localeCompare(b.label)),
    ],
    isLoading: isCostsAlertsLoading(state),
    hasCosts: costs as boolean,
    vehicles: getCostsAlertsVehicles(state),
    drivers: getCostsAlertsDrivers(state),
  }
}

type ReduxProps = ReturnType<typeof mapStateToProps>

export default memo(connect(mapStateToProps)(withRouter(MiFleetAlertsList)))

const StyledButton = styled(Button)({
  '&:hover': {
    backgroundColor: 'transparent',
  },
})
const RightToolbar = styled(Box)({
  display: 'flex',
  gap: '10px',
})
