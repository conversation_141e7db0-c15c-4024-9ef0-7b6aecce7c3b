import { useCallback, useMemo, useState } from 'react'
import { capitalize, isNil, size } from 'lodash'
import {
  Autocomplete,
  Chip,
  DataGrid,
  Divider,
  GridToolbarExport,
  LinearProgress,
  OverflowTypography,
  Skeleton,
  Stack,
  TextField,
  Tooltip,
  Typography,
  useCallbackBranded,
  useDataGridColumnHelper,
  type GridColDef,
} from '@karoo-ui/core'
import { DateTime, Duration, type DurationObjectUnits } from 'luxon'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { GPSFixType } from 'api/types'
import { getGeofences } from 'duxs/geofences-selector'
import { getSettings } from 'duxs/user'
import {
  getDefaultVehiclesTableColumns,
  getStylePositionUnreliableTypeSetting,
  getUserPositionAddressStateGetter,
  getVehicleDetailsShowTerminalSerial,
  getVehiclesViewStatus,
} from 'duxs/user-sensitive-selectors'
import {
  getVehicleGroups,
  getVehicles,
  getVehiclesQueryIsFetching,
  type ReduxVehicles,
} from 'duxs/vehicles'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { UserFormattedLengthInKmOrMiles } from 'src/modules/components/connected/UserFormattedLengthInKmOrMiles'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import type { EventStatusClassName } from 'src/modules/map-view/map/types'
import { useTypedSelector } from 'src/redux-hooks'
import { variables } from 'src/shared/components/styled/global-styles'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'
import Lookup from 'src/util-components/lookup'

import { FormattedDistance, StarRating, Stats } from 'cartrack-ui-kit'
import { useVehiclesCurrentGeofencesQuery } from '../useVehiclesCurrentGeofences'

const enum VehicleFilterOptions {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

const enum VehicleStatusFilterOptions {
  NO_SIGNAL = 'no-signal',
  NO_SIGNAL_WITH_TIME = 'no-signal ns-with-time',
  IGNITION_OFF = 'ignition-off',
  IDLING = 'idling',
  DRIVING = 'driving',
  STATIONARY = 'stationary',
  LAST_UPDATE = 'last-update',
  UNAVAILABLE = 'unavailable',
}
const VEHICLES_STATUS_MAP_LABEL: Record<VehicleStatusFilterOptions, string> = {
  [VehicleStatusFilterOptions.NO_SIGNAL]: 'No Signal',
  [VehicleStatusFilterOptions.NO_SIGNAL_WITH_TIME]: 'Last Seen',
  [VehicleStatusFilterOptions.IGNITION_OFF]: 'Ignition Off',
  [VehicleStatusFilterOptions.IDLING]: 'Idling',
  [VehicleStatusFilterOptions.DRIVING]: 'Driving',
  [VehicleStatusFilterOptions.STATIONARY]: 'Stationary',
  [VehicleStatusFilterOptions.LAST_UPDATE]: 'Last Update',
  [VehicleStatusFilterOptions.UNAVAILABLE]: 'Status Unavailable',
}

const VehicleStatusFilterOptionsMap = {
  'no-signal': VehicleStatusFilterOptions.NO_SIGNAL,
  'no-signal ns-with-time': VehicleStatusFilterOptions.NO_SIGNAL_WITH_TIME,
  'ignition-off': VehicleStatusFilterOptions.IGNITION_OFF,
  idling: VehicleStatusFilterOptions.IDLING,
  driving: VehicleStatusFilterOptions.DRIVING,
  stationary: VehicleStatusFilterOptions.STATIONARY,
  'last-update': VehicleStatusFilterOptions.LAST_UPDATE,
  unavailable: VehicleStatusFilterOptions.UNAVAILABLE,
} as const satisfies Partial<
  Record<
    EventStatusClassName | 'unavailable' | 'last-update',
    VehicleStatusFilterOptions
  >
>

const vehiclesStatusArray = Object.entries(VEHICLES_STATUS_MAP_LABEL).map(
  ([value, label]) => ({
    value: value as VehicleStatusFilterOptions,
    label,
  }),
)

const STATUS_LABEL_MAP: Record<
  VehicleStatusFilterOptions,
  {
    statusLabel: string
    color: string
    textColor: string
    chip: boolean
  }
> = {
  [VehicleStatusFilterOptions.NO_SIGNAL]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.NO_SIGNAL],
    color: 'white',
    textColor: 'black',
    chip: true,
  },
  [VehicleStatusFilterOptions.NO_SIGNAL_WITH_TIME]: {
    statusLabel:
      VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.NO_SIGNAL_WITH_TIME],
    color: '',
    textColor: '',
    chip: false,
  },
  [VehicleStatusFilterOptions.IGNITION_OFF]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.IGNITION_OFF],
    color: 'white',
    textColor: 'black',
    chip: true,
  },
  [VehicleStatusFilterOptions.IDLING]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.IDLING],
    color: '#FF9800',
    textColor: 'white',
    chip: true,
  },
  [VehicleStatusFilterOptions.DRIVING]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.DRIVING],
    color: '#4CAF50',
    textColor: 'white',
    chip: true,
  },
  [VehicleStatusFilterOptions.STATIONARY]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.STATIONARY],
    color: '#4CAF50',
    textColor: 'white',
    chip: true,
  },
  [VehicleStatusFilterOptions.LAST_UPDATE]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.LAST_UPDATE],
    color: '',
    textColor: '',
    chip: false,
  },
  [VehicleStatusFilterOptions.UNAVAILABLE]: {
    statusLabel: VEHICLES_STATUS_MAP_LABEL[VehicleStatusFilterOptions.UNAVAILABLE],
    color: '',
    textColor: '',
    chip: false,
  },
}
function getVehicleStatusValue(
  statusName: string | undefined,
): VehicleStatusFilterOptions {
  return (
    VehicleStatusFilterOptionsMap[
      statusName as keyof typeof VehicleStatusFilterOptionsMap
    ] || VehicleStatusFilterOptions.UNAVAILABLE
  )
}
const getStatusLabel = (statusName: string | undefined) =>
  STATUS_LABEL_MAP[statusName as VehicleStatusFilterOptions] ||
  STATUS_LABEL_MAP[VehicleStatusFilterOptions.UNAVAILABLE]

export function VehiclesTable() {
  const history = useHistory()
  const columnHelper = useDataGridColumnHelper<ReduxVehicles[number]>({
    filterMode: 'client',
  })

  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehiclesQueryIsFetching = useTypedSelector(getVehiclesQueryIsFetching)
  const { vehicleOpenVehicle, vehicleBasicInfo } = useTypedSelector(getSettings)
  const vehiclesViewStatus = useTypedSelector(getVehiclesViewStatus)
  const stylePositionUnreliableType = useTypedSelector(
    getStylePositionUnreliableTypeSetting,
  )
  const getUserPositionAddressState = useTypedSelector(
    getUserPositionAddressStateGetter,
  )
  const defaultVehiclesTableColumns = useTypedSelector(getDefaultVehiclesTableColumns)
  const vehicleDetailsShowTerminalSerial = useTypedSelector(
    getVehicleDetailsShowTerminalSerial,
  )
  const vehicles = useTypedSelector(getVehicles)
  const geofencesList = useTypedSelector(getGeofences)
  const vehicleIds = useMemo(() => vehicles.map((v) => v.id), [vehicles])

  const vehiclesCurrentGeofences = useVehiclesCurrentGeofencesQuery({
    vehicleIds,
  })

  const vehicleFilterOptions = useMemo(
    () => [
      {
        label: ctIntl.formatMessage({ id: 'All Vehicles' }),
        id: VehicleFilterOptions.ALL,
      },
      {
        label: ctIntl.formatMessage({ id: 'Active Vehicles' }),
        id: VehicleFilterOptions.ACTIVE,
      },
      {
        label: ctIntl.formatMessage({ id: 'Decommissioned Vehicles' }),
        id: VehicleFilterOptions.INACTIVE,
      },
    ],
    [],
  )

  const [vehicleStatusFilter, setVehicleStatusFilter] = useState<
    (typeof vehicleFilterOptions)[number]
  >(vehicleFilterOptions[0])

  const filteredVehicles = useMemo(
    () =>
      match(vehicleStatusFilter.id)
        .with(VehicleFilterOptions.ALL, () => vehicles)
        .with(VehicleFilterOptions.ACTIVE, () => vehicles.filter((v) => v.active))
        .with(VehicleFilterOptions.INACTIVE, () => vehicles.filter((v) => !v.active))
        .exhaustive(),
    [vehicleStatusFilter, vehicles],
  )

  const statusDurationToHuman = ({
    start,
    end,
  }: {
    start: DateTime
    end: DateTime
  }): string => {
    const duration = end.diff(start).shiftTo('days', 'hours', 'minutes').toObject()

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    if ('days' in duration && duration.days! >= 20) {
      duration.minutes = 0
      duration.days = 0
    }

    if ('minutes' in duration) {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      duration.minutes = Math.round(duration.minutes!)
    }

    const cleanedDuration = Object.fromEntries(
      Object.entries(duration)
        .filter(([_key, value]) => value !== 0)
        .map(([key, value]) => [key, Math.abs(value)]),
    ) as DurationObjectUnits

    if (Object.keys(cleanedDuration).length === 0) {
      cleanedDuration.seconds = 0
    }

    return Duration.fromObject(cleanedDuration)
      .toHuman({ unitDisplay: 'short' })
      .replaceAll(/,\s/g, ' ')
  }

  const clockDurationToHuman = ({ rawClock }: { rawClock: number }): string => {
    const clockDuration = Duration.fromObject({
      seconds: rawClock,
    })
      .shiftTo('hours', 'minutes', 'seconds')
      .toObject()

    const cleanedDuration = Object.fromEntries(
      Object.entries(clockDuration)
        .filter(([_key, value]) => value !== 0)
        .map(([key, value]) => [key, Math.abs(value)]),
    ) as DurationObjectUnits

    if (Object.keys(cleanedDuration).length === 0) {
      cleanedDuration.seconds = 0
    }

    return Duration.fromObject(cleanedDuration)
      .toHuman({ unitDisplay: 'short' })
      .replaceAll(/,\s/g, ' ')
  }

  const showGpsUnreliableWarning = useCallback(
    (gpsFixType?: GPSFixType) => {
      if (gpsFixType === undefined) {
        return false
      }
      return (
        stylePositionUnreliableType === 'visible-warning' &&
        (isNil(gpsFixType) || gpsFixType < 3)
      )
    },
    [stylePositionUnreliableType],
  )

  const vehicleColumns = useMemo((): ReadonlyArray<
    GridColDef<ReduxVehicles[number]>
  > => {
    const columns = [
      {
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        field: 'name',
        valueGetter: (_, row) => row.name,
        minWidth: 200,
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={0.5}
            sx={{ minWidth: 0 }}
          >
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              {row.name}
            </OverflowTypography>
            {vehicleStatusFilter.id === VehicleFilterOptions.ALL && !row.active && (
              <Typography
                variant="inherit"
                color="text.secondary"
              >
                {ctIntl.formatMessage({ id: '(Discommisioned)' })}
              </Typography>
            )}
          </Stack>
        ),
      },
      columnHelper.string((_, row) => row.registration, {
        headerName: ctIntl.formatMessage({ id: 'Registration' }),
        field: 'registration',
        minWidth: 200,
      }),
      columnHelper.singleSelect(
        (_, row) => {
          const { statusClassName, lastIgnition } = row

          const isStatusNever = statusClassName === 'ignition-off' && lastIgnition === 0

          return getVehicleStatusValue(isStatusNever ? undefined : statusClassName)
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Status' }),
          field: 'statusClassName',
          valueOptions: vehiclesStatusArray,
          renderCell: ({ row }) => {
            const {
              statusClassName,
              currentStatusSinceDate,
              gpsFixType,
              lastTimestamp,
            } = row

            const isStatusNever =
              statusClassName === 'ignition-off' && currentStatusSinceDate === null

            const labelObject = getStatusLabel(
              isStatusNever ? undefined : statusClassName,
            )

            const dateHumanAbstraction = currentStatusSinceDate
              ? statusDurationToHuman({
                  start: DateTime.fromJSDate(currentStatusSinceDate),
                  end: DateTime.now(),
                })
              : null

            return (
              <Tooltip
                title={
                  <Stack>
                    <Typography sx={{ fontSize: '10px' }}>
                      {dateHumanAbstraction
                        ? ctIntl.formatMessage(
                            { id: 'vehicles.table.status.tooltip' },
                            {
                              values: {
                                status: ctIntl.formatMessage({
                                  id: labelObject.statusLabel,
                                }),
                                dateFor: dateHumanAbstraction,
                              },
                            },
                          )
                        : null}
                    </Typography>
                    <Typography sx={{ fontSize: '10px' }}>
                      {`${ctIntl.formatMessage({
                        id: 'Last Updated',
                      })}: ${DateTime.fromMillis(lastTimestamp).toLocaleString(
                        DateTime.DATETIME_SHORT,
                      )}`}
                    </Typography>
                    {showGpsUnreliableWarning(gpsFixType) && (
                      <Typography sx={{ fontSize: '10px', fontWeight: 'bold' }}>
                        {ctIntl.formatMessage({ id: 'Location may be unreliable' })}
                      </Typography>
                    )}
                  </Stack>
                }
              >
                {labelObject.chip ? (
                  <Stack
                    direction="row"
                    gap={0.8}
                  >
                    <Chip
                      size="small"
                      sx={{
                        backgroundColor: labelObject.color,
                        border: `1px solid #BDBDBD`,
                        color: labelObject.textColor,
                      }}
                      label={ctIntl.formatMessage({ id: labelObject.statusLabel })}
                    />
                    {dateHumanAbstraction && (
                      <>
                        <Typography
                          variant="inherit"
                          color="text.secondary"
                        >
                          {ctIntl.formatMessage({ id: 'for' })}
                        </Typography>
                        <Typography
                          variant="inherit"
                          color="text.primary"
                        >
                          {dateHumanAbstraction}
                        </Typography>
                      </>
                    )}
                  </Stack>
                ) : (
                  <Stack
                    direction="row"
                    gap={0.5}
                  >
                    <Typography
                      variant="inherit"
                      color="text.secondary"
                    >
                      {ctIntl.formatMessage({ id: labelObject.statusLabel })}
                    </Typography>
                    {!isStatusNever && (
                      <Typography
                        variant="inherit"
                        color="text.primary"
                      >
                        {dateHumanAbstraction}
                      </Typography>
                    )}
                    {!isStatusNever && (
                      <Typography
                        variant="inherit"
                        color="text.secondary"
                      >
                        {ctIntl.formatMessage({ id: 'ago' })}
                      </Typography>
                    )}
                  </Stack>
                )}
              </Tooltip>
            )
          },
          minWidth: 200,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driverName = row.driverName
          if (driverName.status === 'UNDISCLOSED') {
            return ctIntl.formatMessage({
              id: 'vehicle.driverName.undisclosed',
            })
          }

          return isNil(driverName.name) || !driverName.name
            ? ctIntl.formatMessage({ id: 'Unassigned' })
            : driverName.name
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
          field: 'driverName',
          renderCell: ({ row }) => {
            const driverName = row.driverName

            if (driverName.status === 'UNDISCLOSED') {
              return ctIntl.formatMessage({
                id: 'vehicle.driverName.undisclosed',
              })
            }

            const isInvalidDriverName = isNil(driverName.name) || !driverName.name

            return (
              <OverflowTypography
                typographyProps={{
                  variant: 'inherit',
                  color: isInvalidDriverName ? 'text.secondary' : 'text.primary',
                }}
              >
                {isInvalidDriverName
                  ? ctIntl.formatMessage({ id: 'Unassigned' })
                  : driverName.name}
              </OverflowTypography>
            )
          },
          maxWidth: 300,
          minWidth: 150,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        minWidth: 150,
      }),
      columnHelper.string((_, row) => row.description1, {
        headerName: ctIntl.formatMessage({ id: 'Description 2' }),
        field: 'description1',
        minWidth: 150,
      }),
      columnHelper.string((_, row) => row.description2, {
        headerName: ctIntl.formatMessage({ id: 'Description 3' }),
        field: 'description2',
        minWidth: 150,
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Score' }),
        field: 'rating',
        type: 'number',
        valueGetter: (_, row) => row.rating,
        renderCell: ({ row }) => (
          <StarRating
            color={'#FFB400'}
            rating={row.rating}
          />
        ),
        width: 80,
        minWidth: 80,
        headerAlign: 'left',
        align: 'left',
      },
      {
        headerName: ctIntl.formatMessage({ id: 'Speed' }),
        field: 'speed',
        valueGetter: (_, row) => row.speed,
        renderCell: ({ row }) => (
          <OverflowTypography typographyProps={{ variant: 'inherit' }}>
            <FormattedDistance
              value={row.speed}
              perTime
              round
            />
          </OverflowTypography>
        ),
        maxWidth: 100,
        minWidth: 100,
      },
      columnHelper.valueGetter(
        (_, row) => {
          const possibleGeofence = geofencesList.find(
            (geofence) => geofence.id === row.homeGeofence,
          )
          if (possibleGeofence) {
            return possibleGeofence.name
          }
          return null
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Home Geofence' }),
          field: 'homeGeofence',
          renderCell: ({ row }) => (
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              <Lookup
                type="geofence"
                property="name"
                id={row.homeGeofence}
              />
            </OverflowTypography>
          ),
          minWidth: 150,
        },
      ),
      columnHelper.valueGetter(
        (_, row) =>
          match(vehiclesCurrentGeofences)
            .with({ status: 'pending' }, () => null)
            .with({ status: 'error' }, () => null)
            .with({ status: 'success' }, ({ data }) => data[row.id])
            .exhaustive(),
        {
          headerName: ctIntl.formatMessage({ id: 'Current Geofence' }),
          field: 'currentGeofence',
          renderCell: ({ row }) =>
            match(vehiclesCurrentGeofences)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="text"
                  sx={{ width: '100%', fontSize: '1rem' }}
                />
              ))
              .with({ status: 'error' }, () => null)
              .with({ status: 'success' }, ({ data }) => data[row.id])
              .exhaustive(),
          flex: 1,
          minWidth: 200,
        },
      ),
      {
        headerName: ctIntl.formatMessage({ id: 'Location' }),
        field: 'positionDescription',
        valueGetter: (_, row) =>
          match(
            getUserPositionAddressState({
              address: row.positionDescription,
              gpsFixType: row.gpsFixType,
            }),
          )
            .with('EMPTY', () => '')
            .with({ visibility: 'PRIVATE' }, () =>
              ctIntl.formatMessage({ id: 'Privacy Enabled' }),
            )
            .with(
              { visibility: 'PUBLIC' },
              ({ processedDescriptionText }) => processedDescriptionText,
            )
            .exhaustive(),
        renderCell: ({ row }) => (
          <UserFormattedPositionAddress
            address={row.positionDescription}
            gpsFixType={row.gpsFixType}
            statesRenderer={{
              publicAddress: ({ processedJSX, hasWarning }) => (
                <OverflowTypography
                  typographyProps={{
                    variant: 'body2',
                    sx: hasWarning
                      ? {
                          '& .MuiTypography-root': {
                            color: variables.red, // for consistency with warning color in UserFormattedPositionAddress
                          },
                          color: variables.red,
                        }
                      : {},
                  }}
                >
                  {processedJSX}
                </OverflowTypography>
              ),
            }}
          />
        ),
        flex: 1,
        minWidth: 300,
      },
      columnHelper.string((_, row) => row.VIN, {
        headerName: ctIntl.formatMessage({ id: 'VIN' }),
        field: 'VIN',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Odometer' }),
        field: 'odometer',
        type: 'number',
        valueGetter: (_, row) => {
          if (typeof row.odometer === 'number') {
            return Math.round(row.odometer)
          }
          return row.odometer
        },
        renderCell: ({ row }) => (
          <OverflowTypography typographyProps={{ variant: 'inherit' }}>
            {row.odometer ? (
              <UserFormattedLengthInKmOrMiles
                valueInKm={row.odometer}
                transformValueBeforeFormatting={Math.round}
              />
            ) : (
              <span>--</span>
            )}
          </OverflowTypography>
        ),
      },
      columnHelper.valueGetter(
        (_, row) =>
          isNil(row.unitRawClock)
            ? null
            : clockDurationToHuman({ rawClock: Number(row.unitRawClock) }),
        {
          headerName: ctIntl.formatMessage({ id: 'Clock' }),
          field: 'unitRawClock',
          renderCell: ({ row }) => (
            <OverflowTypography typographyProps={{ variant: 'inherit' }}>
              {row.unitRawClock ? (
                clockDurationToHuman({ rawClock: Number(row.unitRawClock) })
              ) : (
                <span>--</span>
              )}
            </OverflowTypography>
          ),
        },
      ),
      ...(vehicleDetailsShowTerminalSerial
        ? [
            columnHelper.string((_, row) => row.terminalSerial, {
              headerName: ctIntl.formatMessage({ id: 'Terminal Serial' }),
              field: 'terminalSerial',
            }),
          ]
        : []),
      {
        headerName: ctIntl.formatMessage({ id: 'Defects' }),
        field: 'newDefectsCount',
        valueGetter: (_, row) => row.newDefectsCount,
        renderCell: ({ row }) =>
          row.newDefectsCount ? (
            <span>
              {ctIntl.formatMessage(
                { id: 'list.vehicles.table.defects.count' },
                { values: { count: row.newDefectsCount } },
              )}
            </span>
          ) : (
            <span>--</span>
          ),
      },
      // as const and satisfies are used to make sure we preserve the column `field` types
    ] as const satisfies ReadonlyArray<GridColDef<ReduxVehicles[number]>>

    const basicInfoVehicleColumns = new Set<(typeof columns)[number]['field']>([
      'registration',
      'statusClassName',
      'VIN',
      'odometer',
      'unitRawClock',
    ])

    const vehiclesViewStatusColumns = new Set<(typeof columns)[number]['field']>([
      'statusClassName',
      'speed',
      'homeGeofence',
      'currentGeofence',
      'positionDescription',
      'odometer',
    ])

    if (vehicleBasicInfo) {
      return columns.filter((c) => basicInfoVehicleColumns.has(c.field))
    } else if (!vehiclesViewStatus) {
      return columns.filter((c) => !vehiclesViewStatusColumns.has(c.field))
    } else {
      return columns
    }
  }, [
    columnHelper,
    geofencesList,
    getUserPositionAddressState,
    showGpsUnreliableWarning,
    vehicleBasicInfo,
    vehicleDetailsShowTerminalSerial,
    vehicleStatusFilter.id,
    vehiclesCurrentGeofences,
    vehiclesViewStatus,
  ])

  const columnVisibilityModel: Record<
    GridColDef<ReduxVehicles[number]>['field'],
    boolean
  > = useMemo(() => {
    if (defaultVehiclesTableColumns) {
      return vehicleColumns.reduce(
        (acc, c) => {
          if (!defaultVehiclesTableColumns.includes(c.field)) {
            acc[c.field] = false
          }
          return acc
        },
        {} as Record<GridColDef<ReduxVehicles[number]>['field'], boolean>,
      )
    } else {
      return {
        defaultDriver: false,
        description: false,
        description1: false,
        description2: false,
        homeGeofence: false,
        VIN: false,
        odometer: false,
        unitRawClock: false,
        terminalSerial: false,
        newDefectsCount: false,
      } satisfies Record<GridColDef<ReduxVehicles[number]>['field'], boolean>
    }
  }, [defaultVehiclesTableColumns, vehicleColumns])

  return (
    <UserDataGridWithSavedSettingsOnIDB
      Component={DataGrid}
      dataGridId="sc-vehicles-list"
      data-testid="sc-vehicles-list"
      rows={filteredVehicles}
      getRowId={useCallbackBranded(
        (row: (typeof filteredVehicles)[number]) => row.id,
        [],
      )}
      columns={vehicleColumns}
      pagination
      pageSizeOptions={[25, 50, 100]}
      loading={vehiclesQueryIsFetching}
      initialState={{
        columns: { columnVisibilityModel },
        pagination: {
          paginationModel: { pageSize: 25, page: 0 },
        },
      }}
      disableRowSelectionOnClick
      onRowClick={({ row }) => {
        if (vehicleOpenVehicle) {
          history.push(getVehicleDetailsModalMainPath(history.location, row.id))
        }
      }}
      slots={{
        toolbar: KarooToolbar,
        loadingOverlay: LinearProgress,
        noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
      }}
      slotProps={{
        toolbar: KarooToolbar.createProps({
          slots: {
            searchFilter: { show: true },
            settingsButton: { show: true },
            filterButton: { show: true },
          },
          extraContent: {
            left: (
              <Autocomplete
                size="small"
                sx={{ width: '220px' }}
                options={vehicleFilterOptions}
                value={vehicleStatusFilter}
                getOptionLabel={(option) => option.label}
                disableClearable
                onChange={(_, value) => setVehicleStatusFilter(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({ id: 'Status Filter' })}
                  />
                )}
              />
            ),
            middle: (
              <Stack
                direction="row"
                gap={2}
              >
                <Stats
                  reversed
                  data={[
                    {
                      key: capitalize(vehicleStatusFilter.label),
                      value: size(filteredVehicles),
                    },
                  ]}
                />
                <Divider
                  orientation="vertical"
                  variant="middle"
                  flexItem
                />
                <Stats
                  reversed
                  data={[
                    {
                      key: `Total groups`,
                      value: size(vehicleGroups),
                    },
                  ]}
                />
              </Stack>
            ),
            right: (
              <GridToolbarExport
                csvOptions={{
                  fileName: ctIntl.formatMessage({ id: 'Vehicle List' }),
                }}
                excelOptions={{
                  fileName: ctIntl.formatMessage({ id: 'Vehicle List' }),
                }}
                // TODO: need to enable it when print export stable
                printOptions={{ disableToolbarButton: true }}
              />
            ),
          },
        }),
        pagination: { showFirstButton: true, showLastButton: true },
      }}
      sx={{
        '& .MuiDataGrid-row': {
          cursor: 'pointer',
        },
      }}
    />
  )
}
