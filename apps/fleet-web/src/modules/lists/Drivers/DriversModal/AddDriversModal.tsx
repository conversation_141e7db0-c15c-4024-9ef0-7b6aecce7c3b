import { useMemo, useState } from 'react'
import { But<PERSON>, <PERSON>ack, Switch, Typography } from '@karoo-ui/core'
import { useControlledForm } from '@karoo-ui/core-rhf'
import { FormattedMessage } from 'react-intl'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'
import { z } from 'zod/v4'

import { useCreateDriverMutation } from 'api/drivers/useCreateDriverMutation'
import type { DriverId, VehicleId } from 'api/types'
import {
  getAuthenticatedUserAsAccountUser,
  getIsDeliveryEnabled,
  getIsDeliveryOrFieldServiceEnabled,
  getIsFieldServiceEnabled,
  getISO3166Alpha2CountryCode,
} from 'duxs/user'
import { getVehiclesOptions } from 'duxs/vehicles'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails'
import { getDeliveryDriverInfoDialogMainPath } from 'src/modules/deliveryRevamp/components/DriverLoginInfoDialog'
import DriverAppPinForm from 'src/modules/forms/drivers/DriverAppPinForm'
import DriverGeneralInfoForm, {
  driverGeneralInfoSchema,
  type DriverGeneralInfoSchema,
} from 'src/modules/forms/drivers/GeneralInfoForm'
import AddVehiclePermissions, {
  driverVehiclePermissionsSchema,
  type DriverVehiclePermissionsForm,
} from 'src/modules/forms/drivers/VehiclePermissions'
import {
  Actions,
  ActionsContainer,
} from 'src/modules/map-view/FleetMapView/shared/utils'
import { useTypedSelector } from 'src/redux-hooks'
import type { FixMeAny } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'

import { DialogWrap } from '../utils'

const deliveryDriverInfoSchema = z.object({
  isDeliveryDriver: z.boolean(),
  loginUsername: z.string(),
  loginPassword: z.string(),
})

type DeliveryDriverInfoSchema = z.infer<typeof deliveryDriverInfoSchema>

export type AddDriverSchema = DriverGeneralInfoSchema &
  DriverVehiclePermissionsForm &
  DeliveryDriverInfoSchema

const generateAddDriverSchema = (isDeliveryEnabled: boolean) => {
  const generalInfoSchema = driverGeneralInfoSchema({
    phoneNumberRequired: isDeliveryEnabled,
  })
  const vehiclePermissionSchema = driverVehiclePermissionsSchema
  return isDeliveryEnabled
    ? z.intersection(
        generalInfoSchema,
        z.intersection(deliveryDriverInfoSchema, vehiclePermissionSchema),
      )
    : z.intersection(generalInfoSchema, vehiclePermissionSchema)
}

type Props = {
  withDeliveryDetails: boolean
}

const AddDriversModal = ({ withDeliveryDetails }: Props) => {
  const history = useHistory()
  const defaultCountryCode = useTypedSelector(getISO3166Alpha2CountryCode)
  const allVehicles = useTypedSelector(getVehiclesOptions)
  const isDeliveryOrFieldServiceEnabled = useTypedSelector(
    getIsDeliveryOrFieldServiceEnabled,
  )

  const user = useTypedSelector(getAuthenticatedUserAsAccountUser)

  const isDeliveryEnabled = useTypedSelector(getIsDeliveryEnabled)

  const isFieldServiceEnabled = useTypedSelector(getIsFieldServiceEnabled)

  const activeService = match({ isDeliveryEnabled, isFieldServiceEnabled })
    .with({ isDeliveryEnabled: true }, () => 'Delivery')
    .with({ isFieldServiceEnabled: true }, () => 'Field Service')
    .otherwise(() => '')

  const [isDeliverySwitchEnabled, setIsDeliverySwitchEnabled] =
    useState(withDeliveryDetails)

  const closeDialog = () => {
    history.push({
      pathname: history.location.pathname,
      search: '',
    })
  }

  const addDriverSchema = useMemo(
    () => generateAddDriverSchema(isDeliverySwitchEnabled),
    [isDeliverySwitchEnabled],
  )

  const form = useControlledForm<AddDriverSchema>({
    resolver: zodResolverV4(addDriverSchema),
    mode: 'all',
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneInfo: {
        countryCode: defaultCountryCode,
        number: null,
      },
      passport: '',
      gender: 'male',
      loginUsername: '',
      loginPassword: '',
      driverAppPin: null,
      confirmDriverAppPin: null,
      isDeliveryDriver: withDeliveryDetails,
      vehiclesAndGroupsAllowed: [],
      vehiclePermissionOption: 'all' as const,
    },
  })

  const createDriverMutation = useCreateDriverMutation()

  const submitForm = form.handleSubmit((values) => {
    const isAllCurrentVehiclesSelected = // without future vehicles
      values.vehiclesAndGroupsAllowed.length === 1 &&
      values.vehiclesAndGroupsAllowed[0].type === 'all'

    const formattedValues = {
      ...values,
      vehiclesAndGroupsAllowed: isAllCurrentVehiclesSelected
        ? allVehicles
            .filter((v) => v.value !== 'all')
            .map((v) => ({
              label: v.name,
              value: v.value as VehicleId,
              type: 'vehicle' as const,
            }))
        : values.vehiclesAndGroupsAllowed,
    }

    createDriverMutation.mutate(formattedValues, {
      onSuccess(newDriverId: DriverId) {
        closeDialog()

        if (withDeliveryDetails) {
          history.push(
            getDeliveryDriverInfoDialogMainPath(history.location, {
              driversInfo: {
                country: defaultCountryCode as string,
                account: user.account,
                username: values.loginUsername,
                password: values.loginPassword,
                fleetDriverid: newDriverId,
              },
            }),
          )
        } else {
          history.push(getDriverDetailsModalMainPath(history.location, newDriverId))
        }
      },
    })
  })

  return (
    <DialogWrap
      title={ctIntl.formatMessage({ id: 'Add New Driver' })}
      closeDialog={closeDialog}
      paperSx={{
        '& .MuiDialog-paper': {
          maxWidth: '800px',
          height: 'fit-content',
          overflow: 'auto',
          minHeight: 0,
        },
      }}
      contentSx={{
        overflow: 'visible',
      }}
    >
      <Stack
        spacing={2}
        sx={{ px: 2, pt: 0, pb: 3 }}
      >
        <DriverGeneralInfoForm
          form={form as FixMeAny}
          isPhoneNumberRequired={isDeliverySwitchEnabled}
          isEditing
        />
        <AddVehiclePermissions form={form as FixMeAny} />
        {isDeliveryOrFieldServiceEnabled && (
          <Stack gap={1}>
            <Stack
              direction="row"
              alignItems="center"
            >
              <Switch
                disabled={withDeliveryDetails}
                checked={isDeliverySwitchEnabled}
                onChange={(_, checked) => {
                  if (!checked) {
                    const phoneNumber = form.getValues('phoneInfo.number')
                    if (!phoneNumber) {
                      form.clearErrors('phoneInfo')
                    }
                  }
                  form.setValue('isDeliveryDriver', checked)
                  setIsDeliverySwitchEnabled(checked)
                }}
              />
              <Typography variant="subtitle2">
                {ctIntl.formatMessage({ id: activeService })}
              </Typography>
            </Stack>

            {isDeliverySwitchEnabled ? (
              <>
                <Typography
                  variant="caption"
                  color="text.secondary"
                >
                  <FormattedMessage
                    id="delivery.driverAppLogin.driverAppLoginNotice"
                    values={{ b: (chunks) => <>{chunks}</> }}
                  />
                </Typography>
                <DriverAppPinForm
                  form={form as FixMeAny}
                  isEditing
                />
              </>
            ) : (
              <Typography
                variant="caption"
                color="text.secondary"
              >
                {ctIntl.formatMessage({ id: 'Enable Delivery for this driver' })}
              </Typography>
            )}
          </Stack>
        )}
      </Stack>
      <ActionsContainer>
        <Actions>
          <Button
            variant="outlined"
            size="small"
            color="secondary"
            onClick={closeDialog}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            data-testid="AddGroupModal-SaveButton"
            variant="contained"
            type="submit"
            size="small"
            onClick={submitForm}
            loading={createDriverMutation.isPending}
          >
            {ctIntl.formatMessage({ id: 'Add Driver' })}
          </Button>
        </Actions>
      </ActionsContainer>
    </DialogWrap>
  )
}

export default AddDriversModal
