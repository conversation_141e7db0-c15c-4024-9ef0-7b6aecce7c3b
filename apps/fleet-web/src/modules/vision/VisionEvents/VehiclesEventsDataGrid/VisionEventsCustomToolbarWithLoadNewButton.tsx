import { useMemo, useState, type Dispatch, type SetStateAction } from 'react'
import { debounce } from 'lodash'
import {
  Badge,
  BaseGridToolbarContainer,
  BaseGridToolbarContainerWithItems,
  Button,
  Checkbox,
  CircularProgressDelayedCentered,
  DateRangePicker,
  FormControlLabel,
  GridToolbarFilterButton,
  InputAdornment,
  Menu,
  SingleInputDateRangeField,
  Stack,
  styled,
  TextField,
  useGridApiContext,
  type ButtonProps,
  type DateRange,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp'
import SearchIcon from '@mui/icons-material/Search'
import type { DateTime } from 'luxon'
import {
  Controller,
  useForm,
  useWatch,
  type ControllerRenderProps,
} from 'react-hook-form'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'
import type { ReadonlyObjectDeep } from 'type-fest/source/readonly-deep'
import { z } from 'zod/v4'

import { terminalEventTypeIdSchema } from 'api/types'
import { zodResolverV4 } from 'src/_maintained-lib-forks/zodResolverV4'
import type { parseEventTypesAsDedupedByGeneratedLabel } from 'src/modules/coaching/utils'
import KarooToolbarSettingsButton from 'src/shared/data-grid/KarooToolbar/KarooToolbarSettingsButton'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import type { FetchVisionVehicleEventsFilterModelSchemaSelf } from '../api/types'
import { useFetchVehicleVisionEventsOverviewQuery } from '../api/vehiclesQueries'

export type DataGridFilterModel = FetchVisionVehicleEventsFilterModelSchemaSelf

type FilterModelItem = DataGridFilterModel['items'][number]

type OuterFilterItem = {
  field: keyof DataGridOuterFilterModel
}

const formSchema = z.object({
  eventTypes: z.set(terminalEventTypeIdSchema),
  manualRequest: z.object({
    checked: z.boolean(),
    comment: z.string(),
  }),
})

type FormType = z.infer<typeof formSchema>

export type DataGridOuterFilterModel = FormType & {
  dateRange: { value: DateRange<DateTime> }
}

type FilterEventType = ReturnType<
  typeof parseEventTypesAsDedupedByGeneratedLabel
>['dedupedOrderedEventTypes'][number]

export type DataGridInnerFilterModel = Except<DataGridFilterModel, 'items'> & {
  // eslint-disable-next-line @typescript-eslint/no-restricted-types
  items: Array<Exclude<FilterModelItem, OuterFilterItem>>
}

export type VisionEventsCustomToolbarWithLoadNewButtonProps = {
  dateRangePicker: {
    shortcuts: Array<PickersShortcutsItem<DateRange<DateTime>>>
  }
  setOuterFilterModel: Dispatch<SetStateAction<DataGridOuterFilterModel>>
  outerFilterModel: DataGridOuterFilterModel
  loadNewRowsButton: false | { props: ButtonProps }
  middleStats: React.ReactNode
}

export function VisionEventsCustomToolbarWithLoadNewButton({
  dateRangePicker: { shortcuts },
  setOuterFilterModel,
  outerFilterModel,
  loadNewRowsButton,
  middleStats,
}: VisionEventsCustomToolbarWithLoadNewButtonProps) {
  const gridApiContext = useGridApiContext()

  const visionEventsOverviewQuery = useFetchVehicleVisionEventsOverviewQuery()

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const { control, handleSubmit } = useForm<FormType>({
    resolver: zodResolverV4(formSchema),
    mode: 'onChange',
    defaultValues: outerFilterModel,
  })

  const isManualRequestChecked = useWatch({ control, name: 'manualRequest.checked' })

  const onSubmit = (data: FormType) => {
    setOuterFilterModel((curr) => ({
      ...curr,
      ...data,
    }))
    setAnchorEl(null)
  }

  const onClickEventTypeFilter = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget)

  const onCloseEventTypeFilter = () => setAnchorEl(null)

  const dateRangeFilterValue = outerFilterModel.dateRange.value

  const handleSearchFilterChange = useMemo(
    () =>
      debounce((e) => {
        gridApiContext.current.setQuickFilterValues([e.target.value])
      }, 500), // 500ms debounce because it's server side filtering
    [gridApiContext],
  )

  const selectedFilterCount = useMemo(() => {
    let count = 0
    if (outerFilterModel.manualRequest.checked) {
      count += 1

      // only count the comment when manual request is checked
      if (outerFilterModel.manualRequest.comment) {
        count += 1
      }
    }
    count += outerFilterModel.eventTypes.size

    return count
  }, [outerFilterModel])

  return (
    <BaseGridToolbarContainer sx={{ display: 'grid', alignItems: 'center' }}>
      <BaseGridToolbarContainerWithItems sx={{ justifyContent: 'space-between' }}>
        <Stack
          direction="row"
          spacing={1}
        >
          <DateRangePicker
            value={dateRangeFilterValue}
            onAccept={(value) => {
              const startTime = value[0]
              const endTime = value[1]
              if (startTime && endTime) {
                setOuterFilterModel((curr) => ({
                  ...curr,
                  dateRange: { value: [startTime, endTime.endOf('day')] },
                }))
              } else {
                setOuterFilterModel((curr) => ({
                  ...curr,
                  dateRange: { value: [null, null] },
                }))
              }
            }}
            label={ctIntl.formatMessage({ id: 'Date Range' })}
            slots={{ field: SingleInputDateRangeField }}
            slotProps={{
              shortcuts: { items: shortcuts },
              textField: { sx: { input: { cursor: 'pointer' }, minWidth: '210px' } },
            }}
          />
          {match(visionEventsOverviewQuery)
            .with({ status: 'pending' }, () => (
              <CircularProgressDelayedCentered
                circularProgressDelayedProps={{ size: 20 }}
              />
            ))
            .with({ status: 'error' }, () => null)
            .with({ status: 'success' }, ({ data }) => (
              <Stack>
                <TextField
                  placeholder={ctIntl.formatMessage({
                    id: 'vision.filters.eventType.title',
                  })}
                  slotProps={{
                    input: {
                      readOnly: true,
                      endAdornment: (
                        <InputAdornment position="end">
                          {(() => {
                            if (selectedFilterCount > 0) {
                              return (
                                <Badge
                                  color="primary"
                                  badgeContent={selectedFilterCount}
                                  sx={{
                                    width: '24px',
                                    height: '24px',
                                    '.MuiBadge-badge': { transform: 'none' },
                                  }}
                                />
                              )
                            } else if (open) {
                              return <ArrowDropUpIcon />
                            } else {
                              return <ArrowDropDownIcon />
                            }
                          })()}
                        </InputAdornment>
                      ),
                    },
                  }}
                  onClick={onClickEventTypeFilter}
                  sx={{
                    '.MuiInputBase-root': { cursor: 'pointer' },
                    input: { cursor: 'pointer' },
                  }}
                />
                <Menu
                  id="vision-events-filter-events"
                  anchorEl={anchorEl}
                  open={open}
                  onClose={onCloseEventTypeFilter}
                  sx={{
                    '.MuiPaper-root': { p: 2, pb: 0 },
                    '.MuiList-root': { p: 0, gap: 0.5 },
                    minHeight: '420px',
                    maxHeight: '85vh',
                  }}
                >
                  <form
                    onSubmit={handleSubmit(onSubmit)}
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                    }}
                  >
                    <FilterMenuItem sx={{ gap: 0.5 }}>
                      <IntlTypography
                        msgProps={{ id: 'vision.filters.eventType.title' }}
                        variant="subtitle2"
                      />
                      <IntlTypography
                        msgProps={{ id: 'vision.filters.eventType.subtitle1' }}
                        sx={{ color: 'text.secondary' }}
                      />
                      <Controller
                        name="manualRequest.checked"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={<Checkbox sx={{ py: 0.8 }} />}
                            label={ctIntl.formatMessage({
                              id: 'vision.filters.eventType.manualRequest',
                            })}
                            checked={field.value}
                            onChange={(_e, checked) => {
                              // NOTE: We keep the comment even when unchecked so that we can use it when re-enabling the filter.
                              // And it unchecked, comment would not be sent to server. Better DX
                              field.onChange(checked)
                            }}
                          />
                        )}
                      />
                      <TextFieldControlled
                        ControllerProps={{ name: 'manualRequest.comment', control }}
                        label={ctIntl.formatMessage({
                          id: 'vision.filters.eventType.comment',
                        })}
                        disabled={!isManualRequestChecked}
                        sx={{ width: '100%' }}
                        //NOTE: Necessary to prevent bug of focus on menu when typing 'f' first
                        onKeyDown={(e) => {
                          e.stopPropagation()
                        }}
                      />
                    </FilterMenuItem>
                    <FilterMenuItem sx={{ flex: 1, minHeight: '100%', gap: 1 }}>
                      <Controller
                        name="eventTypes"
                        control={control}
                        render={({ field }) => (
                          <Stack sx={{ flex: 1 }}>
                            <IntlTypography
                              msgProps={{ id: 'vision.filters.eventType.visionEvents' }}
                              sx={{ color: 'text.secondary', py: 0.5 }}
                            />
                            {data.dedupedOrderedCategorizedEventTypes.visionEvents.map(
                              (eventType) => (
                                <EventTypeFilterItem
                                  key={eventType.id}
                                  field={field}
                                  eventType={eventType}
                                />
                              ),
                            )}
                            <IntlTypography
                              msgProps={{ id: 'vision.filters.eventType.otherEvents' }}
                              sx={{ color: 'text.secondary', py: 0.5 }}
                            />
                            {data.dedupedOrderedCategorizedEventTypes.otherEvents.map(
                              (eventType) => (
                                <EventTypeFilterItem
                                  key={eventType.id}
                                  field={field}
                                  eventType={eventType}
                                />
                              ),
                            )}
                          </Stack>
                        )}
                      />
                    </FilterMenuItem>
                    <Stack
                      sx={({ palette }) => ({
                        position: 'sticky',
                        bottom: 0,
                        alignSelf: 'flex-end',
                        width: '100%',
                        display: 'flex',
                        alignItems: 'flex-end',
                        backgroundColor: palette.common.white,
                        mt: 0,
                        py: 1,
                        borderTop: `1px solid ${palette.divider}`,
                      })}
                    >
                      <Button
                        type="submit"
                        variant="contained"
                        size="small"
                      >
                        {ctIntl.formatMessage({ id: 'Apply' })}
                      </Button>
                    </Stack>
                  </form>
                </Menu>
              </Stack>
            ))
            .exhaustive()}
          <TextField
            label={ctIntl.formatMessage({ id: 'Search' })}
            sx={{ minWidth: '150px' }}
            variant="outlined"
            onChange={handleSearchFilterChange}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
          />
          <GridToolbarFilterButton
            slotProps={{
              button: {
                sx: {
                  color: 'action.active',
                  minWidth: 'unset',
                  fontSize: 0,
                  '.MuiButton-startIcon': { margin: 0 },
                },
              },
            }}
          />
        </Stack>
        {middleStats}

        <KarooToolbarSettingsButton gridApiContext={gridApiContext} />
      </BaseGridToolbarContainerWithItems>
      {loadNewRowsButton ? (
        <Button
          variant="outlined"
          sx={{ justifySelf: 'center' }}
          {...loadNewRowsButton.props}
        >
          {ctIntl.formatMessage({ id: 'vision.events.loadNewEvents' })}
        </Button>
      ) : null}
    </BaseGridToolbarContainer>
  )
}

const EventTypeFilterItem = ({
  field,
  eventType,
}: {
  field: ControllerRenderProps<FormType, 'eventTypes'>
  eventType: ReadonlyObjectDeep<FilterEventType>
}) => {
  const checked = field.value.has(eventType.id)

  return (
    <FormControlLabel
      control={<Checkbox sx={{ py: 0.4 }} />}
      label={eventType.generatedLabel}
      checked={checked}
      onChange={(_e, newChecked) => {
        const newEventTypes = new Set(field.value)
        if (newChecked) {
          newEventTypes.add(eventType.id)
        } else {
          newEventTypes.delete(eventType.id)
        }
        field.onChange(newEventTypes)
      }}
    />
  )
}

const FilterMenuItem = styled(Stack)(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    '&:hover': {
      background: 'unset',
    },
    cursor: 'auto',
    gap: 0.2,
    width: '100%',
  }),
)
