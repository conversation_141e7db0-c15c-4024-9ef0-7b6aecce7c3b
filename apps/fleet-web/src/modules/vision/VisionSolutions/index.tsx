import { useCallback, useEffect, useState } from 'react'
import { <PERSON>, Button, Divider, <PERSON>ack, Typography, useMediaQuery } from '@karoo-ui/core'
import { FormattedMessage } from 'react-intl'
import { getCountryCallingCode, type Country } from 'react-phone-number-input'

import { getLocale } from 'duxs/user'
import { ContentContainerScrollable } from 'src/modules/app/components/content-containers'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import { ctIntl } from 'src/util-components/ctIntl'

import VerifyContactModal from '../components/VerifyContactModal'
import VisionCarousel from '../VisionCarousel'
import useCaptureVisionLeadMutation from '../VisionLandingPage/api/useCaptureVisionLeadMutation'
import { GetAFreeQuoteAndDemoButton } from '../VisionLandingPage/GetAFreeQuoteAndDemoButton'
import { isPLDomainOrLocale } from '../VisionLandingPage/isPLDomainOrLocale'

const HEADER_IMG_PADDING_LEFT_IN_PIXELS = 50

const slides = [
  {
    title: 'vision.landingPage.carousel.slide1.header',
    description: 'vision.landingPage.carousel.slide1.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-1.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide2.header',
    description: 'vision.landingPage.carousel.slide2.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-2.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide3.header',
    description: 'vision.landingPage.carousel.slide3.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-3.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide4.header',
    description: 'vision.landingPage.carousel.slide4.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-4.webp',
    note: 'vision.landingPage.carousel.slide4.note',
  },
  {
    title: 'vision.landingPage.carousel.slide5.header',
    description: 'vision.landingPage.carousel.slide5.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-5.webp',
    note: null,
  },
]

const plSlides = [
  {
    title: 'vision.landingPage.carousel.slide1.header',
    description: 'vision.landingPage.carousel.slide1.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-1-pl.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide2.header',
    description: 'vision.landingPage.carousel.slide2.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-2.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide3.header',
    description: 'vision.landingPage.carousel.slide3.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-3.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide4-pl.header',
    description: 'vision.landingPage.carousel.slide4-pl.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-4-pl.webp',
    note: null,
  },
  {
    title: 'vision.landingPage.carousel.slide5.header',
    description: 'vision.landingPage.carousel.slide5.description',
    imageSrc:
      'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-slide-5-pl.webp',
    note: null,
  },
]
const VisionSolutions = () => {
  const isMediumOrSmallerScreen = useMediaQuery('(max-width:1024px)')
  const captureVisionLeadMutation = useCaptureVisionLeadMutation()
  const locale = useTypedSelector(getLocale)
  const isPL = isPLDomainOrLocale(locale || '')
  const [verifyContactModal, setVerifyContactModal] = useState<{
    requireMoreInfo: boolean
  } | null>(null)

  const ga4Event = useCallback((action: string) => {
    GA4.event({
      category: 'Vision solutions',
      action,
    })
  }, [])

  useEffect(() => {
    ga4Event('Navigated to Vision solutions')
  }, [ga4Event])

  return (
    <>
      <ContentContainerScrollable
        sx={{
          pt: 3,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            position: 'relative',
            mb: 11,
            px: 3,
          }}
        >
          <img
            style={{
              width: '100%',
              height: 'auto',
              borderRadius: '15px',
            }}
            src="https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-header-img-1.webp"
          />
          <img
            style={{
              position: 'absolute',
              left: `${HEADER_IMG_PADDING_LEFT_IN_PIXELS}px`,
              bottom: '-60px',
              width: '40%',
            }}
            src={
              isPL
                ? 'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-header-img-2-pl.webp'
                : 'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/landing-header-img-2.webp'
            }
          />

          <Stack
            sx={{
              position: 'absolute',
              left: `${HEADER_IMG_PADDING_LEFT_IN_PIXELS}px`,
              top: '40px',
              width: '55%',
              maxWidth: '600px',
            }}
            gap={4}
          >
            <Typography
              variant={isMediumOrSmallerScreen ? 'h5' : 'h4'} // make sure UI don't break on smaller screens
              color="common.white"
              sx={{ pointerEvents: 'none' }}
            >
              {ctIntl.formatMessage({
                id: 'vision.visionSolutions.bannerHeader',
              })}
            </Typography>

            <Button
              variant="contained"
              sx={{
                width: '75%',
              }}
              onClick={() => {
                ga4Event(
                  'Button click - "Call me for more Vision solutions" - within image',
                )
                setVerifyContactModal({
                  requireMoreInfo: false,
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'vision.visionSolutions.actions.callMe' })}
            </Button>
          </Stack>
        </Box>

        {/* Body */}
        <Box
          sx={{ textAlign: 'center' }}
          mb={5}
        >
          <Typography variant="h6">
            {ctIntl.formatMessage({
              id: 'vision.visionSolutions.carousel.Header',
            })}
          </Typography>
        </Box>

        <Box
          mb={10}
          px={3}
        >
          <VisionCarousel slides={isPL ? plSlides : slides} />
        </Box>

        <Box
          textAlign="center"
          mb={4}
        >
          <Typography
            variant="h6"
            mb={3}
          >
            {ctIntl.formatMessage({
              id: 'vision.landingPage.otherFeaturesHeader',
            })}
          </Typography>

          <Stack
            gap={2}
            divider={
              <Divider
                sx={(theme) => ({
                  width: '200px',
                  alignSelf: 'center',
                  bgcolor: theme.palette.primary.main,
                })}
              />
            }
          >
            <Typography variant="body2">
              <FormattedMessage
                id="vision.landingPage.otherFeatures.feature1"
                values={{
                  b: (chunks) => <strong>{chunks}</strong>,
                }}
              />
            </Typography>

            <Typography variant="body2">
              <FormattedMessage
                id="vision.landingPage.otherFeatures.feature2"
                values={{
                  b: (chunks) => <strong>{chunks}</strong>,
                }}
              />
            </Typography>

            <Typography variant="body2">
              <FormattedMessage
                id="vision.landingPage.otherFeatures.feature3"
                values={{
                  b: (chunks) => <strong>{chunks}</strong>,
                }}
              />
            </Typography>
          </Stack>
        </Box>

        {/* Footer */}
        <Stack
          gap={1.5}
          alignItems="center"
          py={2}
          sx={({ palette }) => ({
            position: 'sticky',
            bottom: 0,
            backgroundColor: 'common.white',
            borderTop: `1px solid ${palette.grey[300]}`,
            zIndex: 100,
          })}
        >
          <Typography>
            {ctIntl.formatMessage({
              id: 'vision.visionSolutions.footer.header',
            })}
          </Typography>

          <Button
            variant="contained"
            sx={{
              width: '380px',
            }}
            onClick={() => {
              ga4Event('Button click - "Call me for more Vision solutions" - footer')
              setVerifyContactModal({
                requireMoreInfo: false,
              })
            }}
          >
            {ctIntl.formatMessage({
              id: 'vision.visionSolutions.actions.callMe',
            })}
          </Button>

          <GetAFreeQuoteAndDemoButton
            onClick={() => {
              setVerifyContactModal({
                requireMoreInfo: true,
              })
            }}
            logScopedEvent={ga4Event}
          />
        </Stack>
      </ContentContainerScrollable>

      {verifyContactModal && (
        <VerifyContactModal
          onClose={() => setVerifyContactModal(null)}
          onSubmit={(values) => {
            if (values.phoneNumber.number && values.phoneNumber.countryCode) {
              const countryCode = getCountryCallingCode(
                values.phoneNumber.countryCode as Country,
              )

              captureVisionLeadMutation.mutate(
                {
                  requireMoreInfo: verifyContactModal.requireMoreInfo,
                  name: values.name,
                  email: values.email,
                  phoneNumber: `+${countryCode}${values.phoneNumber.number}`,
                  jobTitle: values.jobTitle,
                },
                {
                  onSuccess: () => {
                    ga4Event(
                      `Lead captured${
                        verifyContactModal.requireMoreInfo
                          ? ' (more info is required)'
                          : ''
                      }`,
                    )
                  },
                  onSettled: () => {
                    setVerifyContactModal(null)
                  },
                },
              )
            }
          }}
        />
      )}
    </>
  )
}

export default VisionSolutions
