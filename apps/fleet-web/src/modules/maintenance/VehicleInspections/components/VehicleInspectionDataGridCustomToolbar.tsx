import { useCallback, useMemo, useState, type ReactNode } from 'react'
import { debounce } from 'lodash'
import {
  Box,
  Button,
  ButtonGroup,
  CircularProgress,
  DateRangePicker,
  FormControl,
  GridToolbarContainer,
  GridToolbarFilterButton,
  InputAdornment,
  InputLabel,
  Menu,
  MenuItem,
  NativeLink,
  Select,
  SingleInputDateRangeField,
  Stack,
  styled,
  TextField,
  Tooltip,
  Typography,
  useGridApiContext,
  type DateRange,
  type GridApi,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import LaunchOutlinedIcon from '@mui/icons-material/LaunchOutlined'
import SearchIcon from '@mui/icons-material/Search'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { FormattedMessage } from 'react-intl'
import { useHistory } from 'react-router'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { getCustomFormDetailsModalMainPath } from 'src/modules/maintenance/InspectionFormSetup/components/CustomFormModal'
import KarooToolbarSettings from 'src/shared/data-grid/KarooToolbar/KarooToolbarSettingsButton'
import { downloadBasicTableDataAsSheetFile } from 'src/shared/data-grid/utils'
import type { ExtractStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import {
  fetchMaintenanceVehicleInspections,
  type UseMaintenanceVehicleInspectionsQueryData,
} from '../api/queries'
import type {
  FetchMaintenanceVehicleInspectionsFilterModelSchemaSelf,
  VehicleInspectionFormStatus,
} from '../api/types'

const FORMNAME_OPTION_ALL = {
  label: 'All Forms',
  value: 'all',
} as const

const STATUS_OPTION_ALL = {
  label: 'All Statuses',
  value: 'all',
  color: 'success',
} as const

export const getInspectionFormStatusMsgId = (
  status: VehicleInspectionFormStatus,
): string =>
  match(status)
    .with('submitted', () => 'inspectionForm.status.submitted')
    .with('pending', () => 'inspectionForm.status.pending_submission')
    .with('not_submitted', () => 'inspectionForm.status.not_submitted')
    .exhaustive()

export const statusSingleSelectColumns = [
  {
    label: getInspectionFormStatusMsgId('submitted'),
    value: 'submitted',
  },
  {
    label: getInspectionFormStatusMsgId('pending'),
    value: 'pending',
  },
  {
    label: getInspectionFormStatusMsgId('not_submitted'),
    value: 'not_submitted',
  },
] as const satisfies ReadonlyArray<{
  label: string
  value: VehicleInspectionFormStatus
}>

export type DataGridFilterModel =
  FetchMaintenanceVehicleInspectionsFilterModelSchemaSelf

type FilterModelItem = DataGridFilterModel['items'][number]
export type DataGridOuterFilterModel = {
  date: ExtractStrict<FilterModelItem, { field: 'date' }>
  required: ExtractStrict<FilterModelItem, { field: 'required' }>
  formName: ExtractStrict<FilterModelItem, { field: 'formName' }>
  status: ExtractStrict<FilterModelItem, { field: 'status' }>
}

type OuterFilterItem = {
  field: keyof DataGridOuterFilterModel
}

export type DataGridInnerFilterModel = Except<DataGridFilterModel, 'items'> & {
  // eslint-disable-next-line @typescript-eslint/no-restricted-types
  items: Array<Exclude<FilterModelItem, OuterFilterItem>>
}

export type VehicleInspectionDataGridCustomToolbarProps = {
  dateRangePicker: {
    shortcuts: Array<PickersShortcutsItem<DateRange<DateTime>>>
  }
  rowCount: number
  filterModels: {
    value: DataGridOuterFilterModel
    setter: (filter: DataGridOuterFilterModel) => void
  }
  forms: UseMaintenanceVehicleInspectionsQueryData['serverModelPageInfo']['forms']
  queryParams: Parameters<typeof fetchMaintenanceVehicleInspections>[0]
  gridApiRef: React.MutableRefObject<GridApi>
}

const STATUS_SINGLE_SELECT_OPTIONS = [
  STATUS_OPTION_ALL,
  ...statusSingleSelectColumns,
] as const

export const VehicleInspectionDataGridCustomToolbar = ({
  forms,
  filterModels: { value: filterModel, setter: setFilterModel },
  rowCount,
  dateRangePicker: { shortcuts },
  queryParams,
  gridApiRef,
}: VehicleInspectionDataGridCustomToolbarProps) => {
  const history = useHistory()
  const gridApiContext = useGridApiContext()

  const [searchExpanded, setSearchExpanded] = useState(false)
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null)
  const [exportQueryFetchStatus, setExportQueryFetchStatus] = useState<
    'fetching' | 'idle'
  >('idle')
  const isExportMenuOpen = Boolean(exportMenuAnchor)

  const handleSearchFilterChange = debounce((e) => {
    gridApiContext.current.setQuickFilterValues([e.target.value])
  }, 500)

  const dateRangeFilterValue = useMemo((): DateRange<DateTime> => {
    const value = filterModel.date.value
    if (value instanceof DateTime) {
      return [value, null]
    }
    return value ?? [null, null]
  }, [filterModel.date])

  const requiredValue = filterModel.required.value

  const formNameOptions = useMemo(
    () =>
      [
        FORMNAME_OPTION_ALL,
        ...forms
          .filter((form) => form.name)
          .map((form) => ({ label: form.name, value: form.name })),
      ] as const,
    [forms],
  )

  const onExportMenuItemClick = useCallback(
    async (type: 'xlsx' | 'csv') => {
      setExportMenuAnchor(null)
      setExportQueryFetchStatus('fetching')
      try {
        const maxExportableRows = 2000
        const eventsToExport = await fetchMaintenanceVehicleInspections({
          serverModel: {
            filter: queryParams.serverModel.filter,
            sort: queryParams.serverModel.sort,
            pagination: {
              cursor: 'start',
              pageSize: Math.min(rowCount, maxExportableRows),
            },
          },
        })

        const rowsToExport = eventsToExport.rows

        await downloadBasicTableDataAsSheetFile({
          fileName: ctIntl.formatMessage({
            id: 'maintenance.vehicleInspections.title',
          }),
          fileExtension: type,
          gridApiRef,
          rowsToExport,
        })
      } catch {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'Something went wrong, please try again' }),
          { variant: 'error' },
        )
      } finally {
        setExportQueryFetchStatus('idle')
      }
    },
    [
      gridApiRef,
      queryParams.serverModel.filter,
      queryParams.serverModel.sort,
      rowCount,
    ],
  )

  return (
    <Stack sx={{ p: 2, gap: 2 }}>
      <GridToolbarContainer
        sx={{
          p: 0,
          display: 'grid',
          gridTemplateColumns: '1fr 180px',
        }}
      >
        <Stack
          direction="row"
          gap={1}
          alignItems="center"
          sx={{ overflowX: 'auto', pt: 1 }}
        >
          <DateRangePicker
            value={dateRangeFilterValue}
            onAccept={(value) => {
              setFilterModel({
                ...filterModel,
                date: { ...filterModel.date, value },
              })
            }}
            label={ctIntl.formatMessage({ id: 'Date Range' })}
            slots={{ field: SingleInputDateRangeField }}
            slotProps={{
              shortcuts: { items: shortcuts },
              textField: { sx: { input: { cursor: 'pointer' }, minWidth: '200px' } },
            }}
          />
          <ButtonGroup
            sx={({ palette }) => ({
              border: `1px solid ${rgba(
                palette.text.primary,
                palette.action.hoverOpacity,
              )}`,
            })}
          >
            <ButtonTab
              selected={R.isNullish(requiredValue) || requiredValue === 'any'}
              onClick={() =>
                setFilterModel({
                  ...filterModel,
                  required: { ...filterModel.required, value: undefined },
                })
              }
            >
              {ctIntl.formatMessage({ id: 'All' })}
            </ButtonTab>
            <ButtonTab
              selected={requiredValue === true}
              onClick={() => {
                setFilterModel({
                  ...filterModel,
                  required: { ...filterModel.required, value: true },
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'Required' })}
            </ButtonTab>
            <ButtonTab
              selected={requiredValue === false}
              onClick={() => {
                setFilterModel({
                  ...filterModel,
                  required: { ...filterModel.required, value: false },
                })
              }}
            >
              {ctIntl.formatMessage({ id: 'Optional' })}
            </ButtonTab>
          </ButtonGroup>
          <FormControl
            fullWidth
            sx={{ minWidth: '180px' }}
          >
            <InputLabel>{ctIntl.formatMessage({ id: 'Form Name Filter' })}</InputLabel>
            <Select
              size="small"
              value={filterModel.formName.value ?? FORMNAME_OPTION_ALL.value}
              label={ctIntl.formatMessage({
                id: ctIntl.formatMessage({ id: 'Form Name Filter' }),
              })}
              onChange={(e) => {
                const value = e.target
                  .value as (typeof formNameOptions)[number]['value']

                setFilterModel({
                  ...filterModel,
                  formName: {
                    ...filterModel.formName,
                    value: value === 'all' ? undefined : value,
                  },
                })
              }}
            >
              {formNameOptions.map((option) => (
                <MenuItem
                  key={option.value}
                  value={option.value}
                >
                  {option.label ?? ''}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl
            fullWidth
            sx={{ minWidth: '180px' }}
          >
            <InputLabel>{ctIntl.formatMessage({ id: 'Status' })}</InputLabel>
            <Select
              size="small"
              value={filterModel.status.value ?? STATUS_OPTION_ALL.value}
              label={ctIntl.formatMessage({
                id: ctIntl.formatMessage({ id: 'Status' }),
              })}
              onChange={(e) => {
                const value = e.target
                  .value as (typeof STATUS_SINGLE_SELECT_OPTIONS)[number]['value']

                setFilterModel({
                  ...filterModel,
                  status: {
                    ...filterModel.status,
                    value: value === 'all' ? undefined : value,
                  },
                })
              }}
            >
              {STATUS_SINGLE_SELECT_OPTIONS.map((option) => (
                <MenuItem
                  key={option.value}
                  value={option.value}
                >
                  {ctIntl.formatMessage({ id: option.label })}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {searchExpanded ? (
            <TextField
              label={ctIntl.formatMessage({ id: 'Search' })}
              sx={{ minWidth: '150px' }}
              variant="outlined"
              onChange={handleSearchFilterChange}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                },
              }}
            />
          ) : (
            <SearchIcon
              sx={{ color: 'text.secondary', cursor: 'pointer' }}
              onClick={() => setSearchExpanded(true)}
            />
          )}
          <GridToolbarFilterButton
            slotProps={{
              button: {
                sx: {
                  color: 'action.active',
                  minWidth: 'unset',
                  fontSize: 0,
                  '.MuiButton-startIcon': { margin: 0 },
                },
              },
            }}
          />
        </Stack>
        <Stack
          direction="row"
          alignItems="center"
        >
          <Stack>
            <Button
              size="small"
              color="secondary"
              variant="outlined"
              startIcon={
                exportQueryFetchStatus === 'fetching' ? (
                  <CircularProgress size={18} />
                ) : (
                  <UploadFileIcon />
                )
              }
              aria-haspopup="true"
              aria-controls={isExportMenuOpen ? 'export-menu' : undefined}
              aria-expanded={isExportMenuOpen ? 'true' : undefined}
              onClick={(event) => setExportMenuAnchor(event.currentTarget)}
            >
              {`${ctIntl.formatMessage({ id: 'Export' })} (${rowCount})`}
            </Button>
            <Menu
              anchorEl={exportMenuAnchor}
              open={isExportMenuOpen}
              onClose={() => setExportMenuAnchor(null)}
              MenuListProps={{ 'aria-labelledby': 'export-button' }}
            >
              <MenuItem
                key="1"
                onClick={() => onExportMenuItemClick('xlsx')}
              >
                {ctIntl.formatMessage({ id: 'global.download.as.excel' })}
              </MenuItem>
              ,
              <MenuItem
                key="2"
                onClick={() => onExportMenuItemClick('csv')}
              >
                {ctIntl.formatMessage({ id: 'global.download.as.csv' })}
              </MenuItem>
            </Menu>
          </Stack>
          <Stack>
            <KarooToolbarSettings gridApiContext={gridApiContext} />
          </Stack>
        </Stack>
      </GridToolbarContainer>
      <Stack
        sx={({ palette }) => ({
          background: palette.grey['100'],
          py: 1,
          borderRadius: '4px',
          overflowX: 'auto',
          width: '100%',
        })}
        direction="row"
      >
        <FormNameCard sx={{ width: '100px' }}>
          <IntlTypography
            variant="caption"
            msgProps={{ id: 'maintenance.vehicleInspections.toolbar.summary.header' }}
          />
        </FormNameCard>
        {forms
          .filter((form) => form.name)
          .map((form, idx) => {
            const displayCheckMark =
              R.isNonNullish(form.submittedInVehicle) &&
              form.submittedInVehicle === form.totalInVehicle

            return (
              <Tooltip
                key={form.name}
                title={
                  <Stack
                    direction="column"
                    gap={0.5}
                  >
                    <FormattedMessage
                      id="maintenance.vehicleInspections.toolbar.tooltip.submittedVehicles"
                      values={{
                        count: R.isNonNullish(form.submittedInVehicle)
                          ? form.submittedInVehicle
                          : form.total,
                        a: (chunks) => (
                          <TooltipLink
                            onClick={() => {
                              setFilterModel({
                                ...filterModel,
                                formName: {
                                  ...filterModel.formName,
                                  operator: 'contains',
                                  value: form.name,
                                },
                                status: {
                                  ...filterModel.status,
                                  operator: 'is',
                                  value: 'submitted',
                                },
                              })
                            }}
                          >
                            {chunks}
                          </TooltipLink>
                        ),
                      }}
                    />
                    {R.isNonNullish(form.submittedInVehicle) && (
                      <>
                        <FormattedMessage
                          id="maintenance.vehicleInspections.toolbar.tooltip.notSubmittedVehicles"
                          values={{
                            count: form.totalInVehicle - form.submittedInVehicle,
                            a: (chunks) => (
                              <TooltipLink
                                onClick={() => {
                                  setFilterModel({
                                    ...filterModel,
                                    formName: {
                                      ...filterModel.formName,
                                      operator: 'contains',
                                      value: form.name,
                                    },
                                    status: {
                                      ...filterModel.status,
                                      operator: 'is',
                                      value: 'non_submitted',
                                    },
                                  })
                                }}
                              >
                                {chunks}
                              </TooltipLink>
                            ),
                          }}
                        />
                        <FormattedMessage
                          id="maintenance.vehicleInspections.toolbar.tooltip.activeVehicles"
                          values={{
                            count: form.totalInVehicle,
                            a: (chunks) => (
                              <TooltipLink
                                onClick={() => {
                                  setFilterModel({
                                    ...filterModel,
                                    formName: {
                                      ...filterModel.formName,
                                      operator: 'contains',
                                      value: form.name,
                                    },
                                  })
                                }}
                              >
                                {chunks}
                              </TooltipLink>
                            ),
                          }}
                        />
                      </>
                    )}
                    <FormattedMessage
                      id="maintenance.vehicleInspections.toolbar.tooltip.totalVehicles"
                      values={{
                        count: form.total,
                        a: (chunks) => <TooltipLink>{chunks}</TooltipLink>,
                      }}
                    />
                    {R.isNonNullish(form.submittedInVehicle) && (
                      <>
                        <br />
                        <FormattedMessage
                          id="maintenance.vehicleInspections.toolbar.tooltip.setupForm"
                          values={{
                            link: 'shortcut',
                            a: (chunks) => (
                              <TooltipLink
                                onClick={() => {
                                  history.push(
                                    getCustomFormDetailsModalMainPath(
                                      history.location,
                                      { form: 'add' },
                                    ),
                                  )
                                }}
                              >
                                {chunks}
                                <LaunchOutlinedIcon
                                  sx={{ fontSize: 18, verticalAlign: 'middle' }}
                                />
                              </TooltipLink>
                            ),
                          }}
                        />
                      </>
                    )}
                  </Stack>
                }
                placement="bottom"
                arrow={false}
              >
                <FormNameCard
                  hideBorder={idx === forms.length - 1}
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textWrap: 'nowrap',
                  }}
                >
                  <Stack
                    direction="row"
                    alignItems="center"
                    sx={({ typography }) => ({
                      color: 'text.primary',
                      fontWeight: typography.fontWeightBold,
                      gap: '2px',
                    })}
                  >
                    {R.isNonNullish(form.submittedInVehicle)
                      ? `${form.submittedInVehicle} / ${form.totalInVehicle}`
                      : form.total}
                    {displayCheckMark && (
                      <CheckIcon
                        color="success"
                        sx={{ fontSize: 18 }}
                      />
                    )}
                  </Stack>
                  <Typography variant="caption">{form.name}</Typography>
                </FormNameCard>
              </Tooltip>
            )
          })}
      </Stack>
    </Stack>
  )
}

const ButtonTab = styled(Button)<{ selected: boolean }>(
  ({ selected, theme: { palette } }) => ({
    border: `1px solid ${rgba(palette.text.primary, palette.action.hoverOpacity)}`,
    color: selected ? palette.primary.main : palette.text.secondary,
    backgroundColor: selected
      ? rgba(palette.primary.main, palette.action.selectedOpacity)
      : 'transparent',

    '&.MuiButtonGroup-firstButton:hover, &.MuiButtonGroup-middleButton:hover': {
      borderRightColor: 'transparent',
    },
  }),
)

const FormNameCard = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'hideBorder',
})<{ hideBorder?: boolean }>(({ hideBorder = false, theme: { palette } }) => ({
  paddingLeft: '16px',
  paddingRight: '16px',
  color: palette.text.secondary,
  cursor: 'pointer',
  ...(hideBorder ? {} : { borderRight: `1px solid ${palette.grey['300']}` }),
}))

const TooltipLink = ({
  children,
  onClick,
}: {
  children: ReactNode
  onClick?: () => void
}) => (
  <NativeLink
    sx={{ color: 'info.main', cursor: 'pointer' }}
    underline="none"
    onClick={onClick}
  >
    {children}
  </NativeLink>
)
