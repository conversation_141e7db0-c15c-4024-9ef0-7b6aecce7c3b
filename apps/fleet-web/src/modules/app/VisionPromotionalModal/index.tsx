import { useEffect, useState } from 'react'
import { Box, Button, <PERSON>alog, IconButton, Stack, Typography } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import MonitorIcon from '@mui/icons-material/DesktopMacOutlined'
import LiveTvIcon from '@mui/icons-material/LiveTv'
import NotificationImportantIcon from '@mui/icons-material/NotificationImportantOutlined'
import { DateTime } from 'luxon'
import { useHistory } from 'react-router-dom'

import {
  getEnableVisionLanding,
  getHasVisionPromotionalModalBeenSeen,
  getLocale,
  getVisionSetting,
} from 'duxs/user'
import { VISION } from 'src/modules/app/components/routes/vision'
import { useUserAvailableCameraTerminalTypes } from 'src/modules/vision/api/queries'
import { isPLDomainOrLocale } from 'src/modules/vision/VisionLandingPage/isPLDomainOrLocale'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import { ctIntl } from 'src/util-components/ctIntl'

import { useSetVisionPromotionalModalSeenMutation } from './mutations'

const REMIND_LATER_DATE_STORAGE_KEY = 'visionPromotionalModalRemindLater'
const REMIND_LATER_INTERVAL_IN_HOURS = 24

export default function VisionPromotionalModal() {
  const history = useHistory()

  const visionSetting = useTypedSelector(getVisionSetting)
  const hasVisionPromotionalModalBeenSeen = useTypedSelector(
    getHasVisionPromotionalModalBeenSeen,
  )
  const enableVisionLanding = useTypedSelector(getEnableVisionLanding)
  const locale = useTypedSelector(getLocale)
  const isPL = isPLDomainOrLocale(locale || '')
  const userAvailableCameraTerminalTypes = useUserAvailableCameraTerminalTypes()
  const setVisionPromotionalModalSeenMutation =
    useSetVisionPromotionalModalSeenMutation()

  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    const currentReminderDate = localStorage.getItem(REMIND_LATER_DATE_STORAGE_KEY)
    if (
      visionSetting &&
      enableVisionLanding &&
      !hasVisionPromotionalModalBeenSeen &&
      (!currentReminderDate ||
        (DateTime.fromISO(currentReminderDate).isValid &&
          DateTime.fromISO(currentReminderDate) <= DateTime.local()))
    ) {
      setIsModalOpen(true)
    } else {
      setIsModalOpen(false)
    }
  }, [enableVisionLanding, hasVisionPromotionalModalBeenSeen, visionSetting])

  const setModalSeen = () => {
    setVisionPromotionalModalSeenMutation.mutate()
  }

  const handleRemindMeLater = () => {
    GA4.event({
      category: 'Vision promotional modal',
      action: 'Clicked "Remind me later"',
    })

    const reminderDate = DateTime.local().plus({
      hours: REMIND_LATER_INTERVAL_IN_HOURS,
    })

    setIsModalOpen(false)
    localStorage.setItem(REMIND_LATER_DATE_STORAGE_KEY, reminderDate.toString())
  }

  if (!isModalOpen || userAvailableCameraTerminalTypes.isLoading) return null

  const isVisionNotEnabled = userAvailableCameraTerminalTypes.data === 'NONE_AVAILABLE'

  return (
    <Dialog
      open
      onClose={(_, reason) => {
        if (reason === 'backdropClick') {
          return
        }
        setModalSeen()
      }}
      maxWidth="lg"
    >
      <Stack sx={{ maxWidth: '720px' }}>
        <Box
          sx={{
            position: 'relative',
          }}
        >
          <img
            src={
              isPL
                ? 'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/promotional-modal-img-with-camera-pl.webp'
                : 'https://fleetweb.ams3.cdn.digitaloceanspaces.com/vision/promotional-modal-img-with-camera.webp'
            }
            style={{
              width: '100%',
            }}
          />

          {/* Close modal button */}
          <IconButton
            sx={{
              position: 'absolute',
              right: '10px',
              top: '10px',
            }}
            onClick={setModalSeen}
          >
            <CloseIcon
              sx={{
                color: 'common.white',
              }}
            />
          </IconButton>

          <Stack
            sx={{
              position: 'absolute',
              width: '55%',
              height: '100%',
              left: 0,
              top: 0,
              pt: 3,
              pl: 4,
              pb: 6,
            }}
            justifyContent="space-between"
            gap={2}
          >
            <Typography
              variant="h5"
              color="common.white"
            >
              {ctIntl.formatMessage({
                id: isVisionNotEnabled
                  ? `vision.promotionalModal.header`
                  : 'vision.visionSolutions.bannerHeader',
              })}
            </Typography>

            <Stack gap={1.5}>
              <Stack
                direction="row"
                gap={1}
                alignItems="center"
              >
                <IconContainer>
                  <NotificationImportantIcon color="primary" />
                </IconContainer>
                <Typography
                  color="common.white"
                  variant="subtitle2"
                >
                  {ctIntl.formatMessage({
                    id: `vision.promotionalModal.feature1`,
                  })}
                </Typography>
              </Stack>

              <Stack
                direction="row"
                gap={1}
                alignItems="center"
              >
                <IconContainer>
                  <LiveTvIcon color="primary" />
                </IconContainer>
                <Typography
                  color="common.white"
                  variant="subtitle2"
                >
                  {ctIntl.formatMessage({
                    id: `vision.promotionalModal.feature2`,
                  })}
                </Typography>
              </Stack>

              <Stack
                direction="row"
                gap={1}
                alignItems="center"
              >
                <IconContainer>
                  <MonitorIcon color="primary" />
                </IconContainer>
                <Typography
                  color="common.white"
                  variant="subtitle2"
                >
                  {ctIntl.formatMessage({
                    id: `vision.promotionalModal.feature3`,
                  })}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
        </Box>

        {/* Footer */}
        <Stack
          direction="row"
          justifyContent="flex-end"
          gap={1}
          p={2}
        >
          <Button
            variant="outlined"
            color="secondary"
            size="small"
            onClick={handleRemindMeLater}
          >
            {ctIntl.formatMessage({
              id: `Remind me later`,
            })}
          </Button>

          <Button
            variant="contained"
            size="small"
            onClick={() => {
              GA4.event({
                category: 'Vision promotional modal',
                action: 'Navigated to Vision landing page',
              })
              setModalSeen()
              history.push(VISION.subMenusRoutes.VISION_SOLUTIONS.path)
            }}
          >
            {ctIntl.formatMessage({
              id: isVisionNotEnabled ? `Explore benefits` : 'Explore more benefits',
            })}
          </Button>
        </Stack>
      </Stack>
    </Dialog>
  )
}

const IconContainer = ({ children }: { children: React.ReactNode }) => (
  <Stack
    sx={{
      width: '40px',
      height: '40px',
      borderRadius: '50%',
      backgroundColor: 'secondary.main',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    {children}
  </Stack>
)
