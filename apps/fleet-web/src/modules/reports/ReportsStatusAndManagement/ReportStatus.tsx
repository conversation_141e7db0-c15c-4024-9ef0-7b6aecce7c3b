import { useMemo, useState } from 'react'
import {
  BaseGridToolbarContainer,
  BaseGridToolbarContainerWithItems,
  Box,
  Chip,
  DataGridAsTabItem,
  DateRangePicker,
  GridLogicOperator,
  LinearProgress,
  SingleInputDateRangeField,
  Stack,
  ToolbarStandardContent,
  Tooltip,
  useDataGridDateColumns,
  type DateRange,
  type FilterColumnsArgs,
  type GetColumnForNewFilterArgs,
  type GridColDef,
  type GridFilterModel,
  type GridRowParams,
  type GridSingleSelectColDef,
  type GridValidRowModel,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { useHistory } from 'react-router'
import * as R from 'remeda'
import type { Except } from 'type-fest'

import type { FetchReportsStatusV2Resolved } from 'api/reports'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { getLocale } from 'duxs/user'
import { getVehicleGroups, getVehiclesById } from 'duxs/vehicles'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import { createDataGridTextColumn } from 'src/shared/data-grid/utils'
import type { ExcludeStrict } from 'src/types/utils'
import { CellTextWithMore } from 'src/util-components/CellTextWithMore'
import { ctIntl } from 'src/util-components/ctIntl'
import { getTranslatedFormatRepeatInterval } from 'src/util-functions/format-utils'

import { useFetchReportsStatusQuery } from '../api/queries'
import {
  fetchReportStatusDataFilterModelSchema,
  type FetchReportStatusDataFilterModelSchemaSelf,
} from '../api/types'
import {
  EmailColumnTooltip,
  REPORT_STATUS_CHIP_PROPS_OBJ,
  reportFormSearchParamsSchema,
} from '../util'
import { parseVehicleRegistrationToGroupAndVehiclesNames } from './util'

type DataGridFilterModel = FetchReportStatusDataFilterModelSchemaSelf
type DataGridRow = Except<FetchReportsStatusV2Resolved[number], 'parameters'> & {
  parameters: Except<
    FetchReportsStatusV2Resolved[number]['parameters'],
    'registration'
  > & {
    registration: string | Array<string> | undefined
  }
}

type FilterableColumnId = (typeof filterableColumnIds)[keyof typeof filterableColumnIds]

const columnsIds = {
  sendingDate: 'sendingDate',
  reportName: 'reportName',
  status: 'status',
} as const

const filterableColumnIds = R.pick(columnsIds, ['reportName', 'sendingDate', 'status'])
const filterableColumnIdsArray = Object.values(filterableColumnIds) satisfies Array<
  DataGridFilterModel['items'][number]['field']
>

export default function ReportStatus({ basePath }: { basePath: string }) {
  const history = useHistory()
  const fetchReportStatusQuery = useFetchReportsStatusQuery()
  const vehicleGroups = useTypedSelector(getVehicleGroups)
  const vehiclesById = useTypedSelector(getVehiclesById)
  const locale = useTypedSelector(getLocale) as ExcludeStrict<
    ReturnType<typeof getLocale>,
    undefined
  >
  const { createDateColumn, getGridDateColumnOperators } = useDataGridDateColumns({
    filterMode: 'client',
  })
  const shortcuts = useDateRangeShortcutItems()
  const [filterModel, setFilterModel] = useState<DataGridFilterModel>(() => ({
    items: [
      {
        field: columnsIds.sendingDate,
        operator: 'range',
        value: shortcuts.last7Days.getValue(),
      },
    ],
    logicOperator: GridLogicOperator.Or,
  }))

  const shortcutsItems = useMemo(
    (): Array<PickersShortcutsItem<DateRange<DateTime>>> => [
      shortcuts.thisWeek,
      shortcuts.thisMonth,
      shortcuts.thisYear,
      { label: 'All Time', getValue: () => [null, null] },
    ],
    [shortcuts.thisMonth, shortcuts.thisWeek, shortcuts.thisYear],
  )

  const onFilterModelChange = async (newFilterModel: GridFilterModel) => {
    setFilterModel(newFilterModel as DataGridFilterModel)

    // We set the filter model anyways but we try to throw an error while in dev for developers to fix it
    if (ENV.NODE_ENV === 'development') {
      // Might throw an error if the filter model is invalid (which is what we want)
      fetchReportStatusDataFilterModelSchema.self.parse(newFilterModel)
      return
    }
    return
  }

  const rows = useMemo((): Array<DataGridRow> => {
    const reportStatusData = fetchReportStatusQuery.data
    if (!reportStatusData) {
      return []
    }
    return reportStatusData.map((report): DataGridRow => {
      const parameters = report.parameters
      let vehicleRegistration: string | Array<string> = ''

      // TODO: currently only handle the vehicle and vehicle group
      if (parameters?.registration) {
        vehicleRegistration = parseVehicleRegistrationToGroupAndVehiclesNames({
          registration: parameters.registration,
          vehicleGroups,
          vehiclesById,
        })
      }

      return {
        ...report,
        parameters: {
          ...report.parameters,
          registration: vehicleRegistration,
        },
      }
    })
  }, [fetchReportStatusQuery.data, vehicleGroups, vehiclesById])

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const base: Array<GridColDef<DataGridRow>> = [
      createDateColumn({
        field: columnsIds.sendingDate,
        headerName: ctIntl.formatMessage({ id: 'Send Date' }),
        valueGetter: (_, row) => row.lastRan,
        filterOperators: getGridDateColumnOperators({ showTime: false }).filter(
          (operator) => operator.value === 'range',
        ),
        flex: 1,
      }),
      createDataGridTextColumn({
        field: columnsIds.reportName,
        headerNameMsg: { id: 'Report Name' },
        valueGetter: (_, row) => row.reportName,
        flex: 1,
      }),
      {
        field: 'parameters',
        headerName: ctIntl.formatMessage({ id: 'Vehicles' }),
        valueGetter: (_, { parameters: { registration } }) =>
          R.isArray(registration) ? registration.join(', ') : registration ?? '',
        renderCell: ({ value }) =>
          value?.includes(',') ? <CellTextWithMore value={value} /> : value ?? '',
        flex: 1,
        minWidth: 250,
      } satisfies GridColDef<DataGridRow, string>,
      createDataGridTextColumn({
        field: 'interval',
        headerNameMsg: { id: 'Recurring Interval' },
        valueGetter: (_, row) => {
          const formattedInterval =
            getTranslatedFormatRepeatInterval(row.repeatInterval, locale) ||
            ctIntl.formatMessage({ id: 'One Time' })

          return formattedInterval
        },
        flex: 1,
      }),
      createDataGridTextColumn({
        field: 'recipients',
        headerNameMsg: { id: 'Recipients' },
        valueGetter: (_, row) =>
          row.emails === null
            ? ctIntl.formatMessage({ id: 'reports.noRecipients' })
            : row.emails.join(';'),
        renderCell: ({ row }) => {
          if (row.emails) {
            const emails = row.emails
            if (emails.length > 1) {
              return <EmailColumnTooltip emails={emails} />
            }
            return row.emails
          }
          return ctIntl.formatMessage({ id: 'reports.noRecipients' })
        },
        flex: 2,
      }),
      {
        type: 'singleSelect',
        field: columnsIds.status,
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        valueOptions: [
          {
            value: 'Completed',
            label: ctIntl.formatMessage({ id: 'reports.status.sent' }),
          },
          {
            value: 'Processing',
            label: ctIntl.formatMessage({ id: 'reports.status.processing' }),
          },
          {
            value: 'Failed',
            label: ctIntl.formatMessage({ id: 'reports.status.failed' }),
          },
          {
            value: 'Pending',
            label: ctIntl.formatMessage({ id: 'reports.status.upcoming' }),
          },
          {
            value: 'Downloaded',
            label: ctIntl.formatMessage({ id: 'Downloaded' }),
          },
        ],
        valueGetter: (_, row) => row.stateString,
        renderCell: ({ value: stateStringValue, row }) =>
          stateStringValue ? (
            <Tooltip
              title={ctIntl.formatMessage({
                id:
                  stateStringValue === 'Processing'
                    ? 'reports.status.tooltip.processing' // using general message for processing status
                    : row.translationId ?? '',
              })}
              arrow
            >
              <Chip
                size="medium"
                variant="filled"
                sx={{ width: '100px' }}
                {...{
                  ...REPORT_STATUS_CHIP_PROPS_OBJ[stateStringValue],
                  label: ctIntl.formatMessage({
                    id: REPORT_STATUS_CHIP_PROPS_OBJ[stateStringValue].label,
                  }),
                }}
              />
            </Tooltip>
          ) : null,
        flex: 1,
      } satisfies GridSingleSelectColDef<
        {
          label: string
          value: DataGridRow['stateString']
        },
        DataGridRow,
        DataGridRow['stateString']
      >,
    ]

    return base.map((column) => ({
      ...column,
      // sortable: sortableColumnIdsArray.includes(column.field as SortableColumnId),
      filterable: filterableColumnIdsArray.includes(column.field as FilterableColumnId),
    }))
  }, [createDateColumn, getGridDateColumnOperators, locale])

  // Logic based on https://mui.com/x/react-data-grid/filtering/multi-filters/#one-filter-per-column
  const filterColumns = ({
    field,
    columns,
    currentFilters,
  }: FilterColumnsArgs): Array<GridColDef<GridValidRowModel>['field']> => {
    const filteredFields = currentFilters?.map((item) => item.field)
    return columns
      .filter((colDef) => {
        if (!colDef.filterable) {
          return false
        }
        if (colDef.field === field) {
          return true
        }

        // Do not allow to filter twice by sending date since it does not make sense to have two ranges
        return colDef.field === columnsIds.sendingDate &&
          filteredFields.includes(columnsIds.sendingDate)
          ? false
          : true
      })
      .map((column) => column.field)
  }

  // Logic based on https://mui.com/x/react-data-grid/filtering/multi-filters/#one-filter-per-column
  const getColumnForNewFilter = ({
    currentFilters,
    columns,
  }: GetColumnForNewFilterArgs): GridColDef<GridValidRowModel>['field'] | null => {
    const filteredFields = currentFilters?.map(({ field }) => field)
    const columnForNewFilter = columns
      .filter((colDef) => {
        if (!colDef.filterable) {
          return false
        }

        // Do not allow to filter twice by sending date since it does not make sense to have two ranges
        if (colDef.field === columnsIds.sendingDate) {
          return !filteredFields.includes(columnsIds.sendingDate)
        }

        return true
      })
      .find((colDef) => colDef.filterOperators?.length)
    return columnForNewFilter?.field ?? null
  }

  return (
    <UserDataGridWithSavedSettingsOnIDB<DataGridRow>
      hideFooterSelectedRowCount
      data-testid="Reports-StatusTable"
      autoPageSize
      pagination
      Component={DataGridAsTabItem}
      dataGridId="ReportsStatusTableDataGrid"
      rows={rows}
      filterModel={filterModel}
      onFilterModelChange={onFilterModelChange}
      columns={columns}
      loading={fetchReportStatusQuery.isFetching}
      onRowClick={({ row: { id } }: GridRowParams<DataGridRow>) =>
        history.push(
          `${basePath}/exportReportStatus?${buildRouteQueryStringKeepingExistingSearchParams(
            {
              location: history.location,
              schema: reportFormSearchParamsSchema,
              searchParams: { type: 'view', id },
            },
          )}`,
        )
      }
      slots={{ toolbar: CustomDataGridToolbar, loadingOverlay: LinearProgress }}
      slotProps={{
        toolbar: {
          shortcutsItems,
          setFilterModel,
          filterModel,
        } satisfies CustomDataGridToolbarProps,
        pagination: { showFirstButton: true, showLastButton: true },
        filterPanel: {
          filterFormProps: { filterColumns },
          getColumnForNewFilter,
        },
      }}
    />
  )
}

export type CustomDataGridToolbarProps = {
  shortcutsItems: Array<PickersShortcutsItem<DateRange<DateTime>>>
  setFilterModel: (filterModel: DataGridFilterModel) => void
  filterModel: DataGridFilterModel
}

function CustomDataGridToolbar({
  setFilterModel,
  filterModel,
  shortcutsItems,
}: CustomDataGridToolbarProps) {
  const dateRangeFilterValue = useMemo((): DateRange<DateTime> => {
    for (const item of filterModel.items) {
      if (item.field === columnsIds.sendingDate) {
        const value = item.value as ExcludeStrict<typeof item.value, DateTime>
        return value ?? [null, null]
      }
    }

    return [null, null]
  }, [filterModel.items])

  return (
    <BaseGridToolbarContainer sx={{ display: 'grid', alignItems: 'center' }}>
      <BaseGridToolbarContainerWithItems sx={{ justifyContent: 'space-between' }}>
        <Stack
          sx={{ width: '255px' }}
          data-testid="Reports-StatusTable-DateRangeFilter"
        >
          <DateRangePicker
            slots={{ field: SingleInputDateRangeField }}
            slotProps={{ shortcuts: { items: shortcutsItems } }}
            label={ctIntl.formatMessage({ id: 'Send Date' })}
            value={dateRangeFilterValue}
            onAccept={(value) => {
              const hasDateFilterAlready = filterModel.items.find(
                (item) => item.field === columnsIds.sendingDate,
              )

              setFilterModel({
                ...filterModel,
                items: hasDateFilterAlready
                  ? filterModel.items.map((item) =>
                      item.field === columnsIds.sendingDate
                        ? ({
                            ...item,
                            value,
                          } satisfies typeof item)
                        : item,
                    )
                  : [
                      {
                        field: columnsIds.sendingDate,
                        operator: 'range',
                        value,
                      },
                      ...filterModel.items,
                    ],
              })
            }}
          />
        </Stack>
        <Box>
          <ToolbarStandardContent />
        </Box>
      </BaseGridToolbarContainerWithItems>
    </BaseGridToolbarContainer>
  )
}
