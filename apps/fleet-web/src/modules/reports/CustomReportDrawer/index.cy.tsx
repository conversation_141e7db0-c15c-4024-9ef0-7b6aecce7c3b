/* eslint-disable no-param-reassign */
/// <reference types="@testing-library/cypress" />
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { createMemoryHistory } from 'history'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import type {
  CustomizedReportFormFilterWithoutType,
  CustomReportFilterKeyType,
} from 'api/reports/types'
import { CUSTOM_REPORT_FILTERS } from 'api/reports/util'
import type { ReportId } from 'api/types'
import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { endpointsMocks } from 'src/cypress-ct/mocks/endpoints/'
import { geofenceEndpointMocks } from 'src/cypress-ct/mocks/endpoints/geofence'
import { timezoneToParseDates } from 'src/cypress-ct/mocks/endpoints/report'
import {
  exhaustiveEndpointCallCheck,
  matchWithMethod,
  mountWithProviders,
  runTestsInOrderWithLoggingAndSetup,
} from 'src/cypress-ct/utils'

import Report from '../index'

const basePath = '/reports/all-reports'

const customizedReports =
  endpointsMocks.ct_fleet_get_customized_reports().body.result
    .ct_fleet_get_customized_reports

const globalHistory = createMemoryHistory({
  initialEntries: [basePath],
})

const mountCustomReportDrawerMain = () => {
  mountWithProviders(<Report />, {
    history: globalHistory,
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user({ defaultTimezone: timezoneToParseDates }).mockState,
        reports: duxsMocks.reports,
      },
    },
  })
}

const handleApiCalls = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_prelogin_data' }, () =>
      req.reply({ delay: 50, body: { id: 10, result: {} } }),
    )
    .with(matchWithMethod(req, 'ct_fleet_get_report_options_v2'), () => {
      req.reply(endpointsMocks.ct_fleet_get_report_options_v2())
    })
    .with({ method: 'ct_fleet_get_favourite_reports' }, () => {
      req.alias = 'getFavouriteReports'
      req.reply(endpointsMocks.ct_fleet_get_favourite_reports())
    })
    .with({ method: 'ct_fleet_get_report_preview' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_report_preview()),
    )
    .with({ method: 'ct_fleet_get_customized_reports' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_customized_reports()),
    )
    .with({ method: 'ct_fleet_get_customized_report_configuration' }, () => {
      req.alias = 'getCustomizedReportConfig'
      req.reply(
        endpointsMocks.ct_fleet_get_customized_report_configuration(
          customizedReports[0].report_user_customized_id as ReportId,
        ),
      )
    })
    .with({ method: 'ct_fleet_get_custom_reports' }, () => {
      req.alias = 'getCustomReports'
      req.reply(endpointsMocks.ct_fleet_get_custom_reports())
    })
    .with({ method: 'ct_fleet_get_user' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_user()),
    )
    .with({ method: 'ct_fleet_get_vehiclelist_v3' }, () =>
      req.reply(endpointsMocks.ct_fleet_get_vehiclelist_v3()),
    )
    .with({ method: 'ct_fleet_get_custom_report_resources' }, () => {
      req.alias = 'getCustomReportResources'
      req.reply(endpointsMocks.ct_fleet_get_custom_report_resources())
    })
    .with({ method: 'ct_fleet_get_geofence_v2' }, () =>
      req.reply(geofenceEndpointMocks.ct_fleet_get_geofence_v2()),
    )
    .with(matchWithMethod(req, 'ct_fleet_get_report_profile_data'), () => {
      req.reply(endpointsMocks.ct_fleet_get_report_profile_data())
    })
    .otherwise(exhaustiveEndpointCallCheck)

describe('Custom Report List', () => {
  const setup = () => {
    mountCustomReportDrawerMain()
    cy.wait('@getFavouriteReports')
  }

  runTestsInOrderWithLoggingAndSetup('create/edit/delete the reports', {
    setupIfFirstTest: setup,
    tests: [
      {
        name: 'show the custom report list and preview the first item',
        fn: ({ setupIfFirstTest }) => {
          cy.intercept('POST', '/jsonrpc/public/index.php', handleApiCalls)

          // NOTE: for the first case, wait to load the multiple requests
          setupIfFirstTest()

          // click the custom report tab
          cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()

          //first item of report should be selected
          cy.findByTestId('Reports-AllReports-Content-Name').should(
            'contain.text',
            customizedReports[0].report_name,
          )

          // delete button
          return cy.findByTestId('Report-AllReports-Content-DelIcon').should('exist')
        },
      },
      {
        name: 'click button create custom will show the add custom report drawer',
        fn: ({ setupIfFirstTest }) => {
          const firstCustomReport =
            endpointsMocks.ct_fleet_get_custom_reports().body.result
              .ct_fleet_get_custom_reports[0]
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with({ method: 'ct_fleet_submit_custom_report' }, () => {
                req.alias = 'submitCustomizedReport'
                req.reply({ delay: 50, body: { id: 10, result: true } })
              })
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          // click the custom report tab
          cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()

          cy.findByTestId('Reports-AllReports-Sidebar-CreateCustomButton').click()

          // wait the custom report api
          cy.wait('@getCustomReports')

          // drawer should be displayed
          cy.findByTestId('Reports-Export-Drawer').should('exist')

          // Step 1
          cy.findByTestId('CustomReport-Category')
            .find('input')
            .type(`${firstCustomReport.category}{downArrow}{enter}`)

          // report name is required
          cy.findByTestId('CustomReport-ReportName')
            .find('input')
            .type('{selectAll}{del}')
          cy.findByText('This field is required').should('exist')

          cy.findByTestId('CustomReport-ReportName')
            .find('input')
            .type('Detail Position Report')
          cy.findByText('This field is required').should('not.exist')
          cy.findByTestId('CustomReport-Next').click()

          // Step 2
          cy.findAllByTestId('CustomReport-SelectFieldButton').first().click()
          cy.findAllByTestId('CustomReport-SelectFieldButton').eq(1).click()
          cy.findAllByTestId('CustomReport-SelectFieldButton').eq(2).click()

          cy.findAllByTestId('CustomReport-RemoveFieldButton').should('have.length', 3)

          cy.findAllByTestId('CustomReport-RemoveFieldButton').first().click()
          cy.findAllByTestId('CustomReport-RemoveFieldButton').should('have.length', 2)
          cy.findAllByTestId('CustomReport-Next').click()

          // Step 3
          cy.findByTestId('CustomReport-Filters').children().should('have.length', 2)

          cy.findByTestId('CustomReport-Next').click()

          // Step 4
          // Should render date range widget
          cy.findByTestId('ReportForm-DateRange').should('exist')
          cy.findByTestId('ReportForm-Registration').should('exist')

          cy.findByTestId('ReportForm-DateRange')
            .find('input')
            .first()
            .type('2023/11/01')
          cy.findByTestId('ReportForm-DateRange').find('input').eq(1).type('2023/11/09')

          cy.findByTestId('CustomReport-Submit').click()
          cy.wait('@submitCustomizedReport')

          return cy.findByTestId('Reports-Export-Drawer').should('not.exist')
        },
      },
      {
        name: 'click button edit template in preview will show the edit custom report drawer',
        fn: ({ setupIfFirstTest }) => {
          // TODO: check the data, string and number type fields and their filters
          const customReport =
            endpointsMocks.ct_fleet_get_customized_report_configuration(
              customizedReports[0].report_user_customized_id as ReportId,
            ).body.result

          const seletedTemplate = endpointsMocks
            .ct_fleet_get_custom_reports()
            .body.result.ct_fleet_get_custom_reports.find(
              (r) => r.report_id === customReport.report_id,
            )

          // TODO: test fields in the form and submission
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with({ method: 'ct_fleet_submit_custom_report' }, () => {
                req.alias = 'submitCustomizedReport'
                req.reply({ delay: 50, body: { id: 10, result: true } })
              })
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          // click the custom report tab
          cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()

          cy.findByTestId('Report-AllReports-Content-EditButton').click()

          // drawer should be displayed
          cy.findByTestId('Reports-Export-Drawer').should('exist')

          // Step 1
          cy.findByTestId('CustomReport-Category')
            .find('input')
            .should('contain.value', seletedTemplate?.category)
          cy.findByTestId('CustomReport-ReportName')
            .find('input')
            .should('contain.value', customReport.report_name)
          cy.findByTestId('CustomReport-Next').click()
          // TODO: test the supress header and footer

          // Step 2
          const totalColumns = seletedTemplate?.report_fields.split(';').length ?? 0
          const totalField = customReport.report_data.customFields.split(';').length
          cy.findAllByTestId('CustomReport-SelectFieldButton').should(
            'have.length',
            totalColumns - totalField,
          )
          cy.findAllByTestId('CustomReport-RemoveFieldButton').should(
            'have.length',
            totalField,
          )
          cy.findByTestId('CustomReport-Next').click()
          // TODO test drag and drop

          // Step 3
          const allFields = seletedTemplate?.report_fields
            .split(';')
            .map((field) => field.split('='))

          const getColumnField = (key: string) =>
            allFields?.find((field) => field[0] === key)

          const customReportFilters = customReport.report_data.filters as Array<
            CustomizedReportFormFilterWithoutType & { key: string }
          >
          const getFiltersByKey = (key: string) =>
            customReportFilters?.filter((filter) => filter.key === key)

          cy.findAllByTestId('CustomReport-FilterForColumn').should(
            'have.length',
            totalField,
          )

          const fieldFilters = customReportFilters?.sort()

          // test filter title, value and operators
          for (const filter of fieldFilters) {
            // check the label
            cy.findByTestId(`CustomReport-FilterForColumn-Title-${filter.key}`).should(
              'have.text',
              getColumnField(filter.key)?.[1],
            )

            // get the type
            const type = getColumnField(filter.key)?.[2] as CustomReportFilterKeyType

            if (type) {
              // same key may have multiple items, so iterate as well
              const filters = getFiltersByKey(filter.key)
              for (const [index, f] of filters.entries()) {
                if (f.operator) {
                  const operatorLabel = CUSTOM_REPORT_FILTERS[type].find(
                    (op) => op.value === f.operator,
                  )?.name

                  // operator
                  cy.findByTestId(`CustomReport-FilterBy-${filter.key}_${index}`)
                    .find('input')
                    .should('have.value', f.operator)
                  cy.findByTestId(`CustomReport-FilterBy-${filter.key}_${index}`)
                    .find('[role="combobox"]')
                    .should('have.text', operatorLabel)
                }

                // value
                cy.findByTestId(`CustomReport-FilterBy-Input-${filter.key}_${index}`)
                  .find('input')
                  .should(
                    'have.value',
                    type === 'DATE_COMP'
                      ? DateTime.fromFormat(f.value, 'yyyy-MM-dd HH:mm:ss').toFormat(
                          'yyyy/MM/dd',
                        )
                      : f.value,
                  )
              }
            }
          }

          // TODO update the value

          cy.findByTestId('CustomReport-Next').click()

          // Step 4

          cy.findByTestId('CustomReport-Submit').click()
          cy.wait('@submitCustomizedReport')

          return cy.findByTestId('Reports-Export-Drawer').should('not.exist')
        },
      },
      {
        name: 'click button export in preview will show the export custom report drawer',
        fn: ({ setupIfFirstTest }) => {
          cy.intercept('POST', '/jsonrpc/public/index.php', handleApiCalls)

          setupIfFirstTest()

          // click the custom report tab
          cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()

          cy.findByTestId('Report-AllReports-Content-ExportButton').click()

          cy.wait('@getCustomizedReportConfig')

          // drawer should be displayed
          cy.findByTestId('Reports-Export-Drawer').should('exist')

          // Should jump to step 4 and have button submit and button to edit template the custom report
          cy.findByTestId('CustomReport-Submit').should('exist')
          cy.findByTestId('CustomReport-Edit-Template').should('exist')
          cy.findByTestId('CustomReport-Previous').should('not.exist')

          cy.findByTestId('CustomReport-Cancel').click()

          return cy.findByTestId('Reports-Export-Drawer').should('not.exist')
        },
      },
      {
        name: 'click delete button will delete the report with confirmation',
        fn: ({ setupIfFirstTest }) => {
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with({ method: 'ct_fleet_delete_customized_report' }, () => {
                req.reply({
                  delay: 50,
                  body: { id: 10, result: { ct_fleet_get_customized_reports: [] } },
                })
              })
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          // click the custom report tab
          cy.findByTestId('Reports-AllReports-Sidebar-CusTab').click()

          // click the delete button of first row
          cy.findByTestId('Report-AllReports-Content-DelIcon').click()
          // delete confirmation modal
          cy.findByTestId('Report-AllReports-Content-DeleteModal').should('exist')

          // click the confirm button
          cy.findByTestId('ConfirmationModal-confirm-button').click()

          // close the confirm modal
          return cy
            .findByTestId('Report-AllReports-Content-DeleteModal')
            .should('not.exist')
        },
      },
    ],
  })
})
