import { forwardRef, useMemo, useState } from 'react'
import { Stack, TimePicker, type TimePickerProps } from '@karoo-ui/core'
import { DateTime } from 'luxon'
import type { Except } from 'type-fest'

import type { ExcludeStrict } from 'src/types/utils'

export type ScheduleTimeInUserTZ = {
  hour: number
  minute: number
  second?: number | undefined
}

type ScheduleTimeFieldProps = {
  value: ScheduleTimeInUserTZ | null | undefined
  onChange: (value: ScheduleTimeInUserTZ | null) => void
  dataTestId: string
} & Except<TimePickerProps<DateTime>, 'value' | 'onChange'>

export const parseScheduleTimeInUserTZToDateTime = ({
  hour,
  minute,
}: ScheduleTimeInUserTZ) => {
  // IMPORTANT: We create a DateTime.local instance directly instead of Date to make sure there isn't an implicit timezone conversion afterwards.
  // It's usually not a problem, but since we are creating a date from hour and minute (no timezone information), it could be a problem if we did so.
  // This is specially relevant if the luxon timezone was something like (India: UTC +5:30). The time could actually be completely altered when converting DateTime <-> Date.
  const date = DateTime.local(
    2000 /* irrelevant */,
    1 /* irrelevant */,
    1 /* irrelevant */,
    Number(hour),
    Number(minute),
    0,
  )

  return date
}

/**
 * This component abstracts the conversion to/from a DateTime instance so we can just use CameraEventScheduleTimeType since it's a simpler and less error prone concept to work with.
 */
const ScheduleTimePicker = forwardRef<HTMLDivElement, ScheduleTimeFieldProps>(
  ({ onChange, value, dataTestId, slotProps, ...rest }, ref) => {
    const [isTimePickerOpen, setIsTimePickerOpen] = useState(false)

    const valueAsDateTime = useMemo(
      () => (value ? parseScheduleTimeInUserTZToDateTime(value) : null),
      // eslint-disable-next-line react-hooks/react-compiler
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [value?.hour, value?.minute, value?.second], // Do not use value as dependency because the object reference might change (even though the value didn't)
    )

    const onChangeAsDateTime: ExcludeStrict<
      TimePickerProps<DateTime>['onChange'],
      undefined
    > = (valueInUserTz) => {
      if (!valueInUserTz) {
        return onChange(null)
      }
      const { hour, minute, second } = valueInUserTz

      onChange({ hour, minute, second })
    }

    return (
      <Stack data-testid={dataTestId}>
        <TimePicker
          {...rest}
          open={isTimePickerOpen}
          onOpen={() => setIsTimePickerOpen(true)}
          onClose={() => setIsTimePickerOpen(false)}
          ref={ref}
          value={valueAsDateTime}
          onChange={onChangeAsDateTime}
          slotProps={{
            ...slotProps,
            textField: {
              ...slotProps?.textField,
              onClick: () => setIsTimePickerOpen(true),
            },
          }}
        />
      </Stack>
    )
  },
)

export default ScheduleTimePicker
