import { forwardRef, useEffect, useState } from 'react'
import { FormHelperText, Stack, type TimePickerProps } from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import type { Except } from 'type-fest'

import { ctIntl } from './ctIntl'
import ScheduleTimePicker, {
  parseScheduleTimeInUserTZToDateTime,
  type ScheduleTimeInUserTZ,
} from './schedule-time-picker'

export type ScheduleTimeRange = { from: ScheduleTimeInUserTZ; to: ScheduleTimeInUserTZ }

type ScheduleTimeRangeProps = {
  value: ScheduleTimeRange | null
  onSelect: (value: ScheduleTimeRange | null) => void
  dataTestId: string
} & Except<TimePickerProps<DateTime>, 'value' | 'onChange'>

const ScheduleTimeRangePicker = forwardRef<HTMLDivElement, ScheduleTimeRangeProps>(
  ({ onSelect, value, dataTestId, ...rest }, ref) => {
    const [timeRange, setTimeRange] = useState<{
      from?: ScheduleTimeInUserTZ | null
      to?: ScheduleTimeInUserTZ | null
    } | null>(value)
    const [error, setError] = useState<string | null>(null)

    // update internal state when value changes
    useEffect(() => {
      setTimeRange(value)
    }, [value])

    const onSelectAsDateTime = (
      valueInUserTz: ScheduleTimeInUserTZ | null,
      type: 'from' | 'to',
    ) => {
      setError(null)

      // initial state
      if (!timeRange) {
        setTimeRange(type === 'from' ? { from: valueInUserTz } : { to: valueInUserTz })
        return
      }

      if (!valueInUserTz) {
        setTimeRange((prev) => ({ ...prev, [type]: null }))
        onSelect(null)
        return
      }

      // end time should not be earlier than start time
      const valueInDateTime = parseScheduleTimeInUserTZToDateTime(valueInUserTz)
      const timeRangeFromInDateTime = timeRange.from
        ? parseScheduleTimeInUserTZToDateTime(timeRange.from)
        : null
      const timeRangeToInDateTime = timeRange.to
        ? parseScheduleTimeInUserTZToDateTime(timeRange.to)
        : null
      if (
        valueInUserTz &&
        ((type === 'to' &&
          timeRangeFromInDateTime &&
          valueInDateTime < timeRangeFromInDateTime) ||
          (type === 'from' &&
            timeRangeToInDateTime &&
            valueInDateTime > timeRangeToInDateTime))
      ) {
        setError('Start time cannot be later than end time')
        return
      }

      const updatedTimeRange = { ...timeRange, [type]: valueInUserTz }
      setTimeRange(updatedTimeRange)

      if (updatedTimeRange.from && updatedTimeRange.to) {
        onSelect({
          from: updatedTimeRange.from,
          to: updatedTimeRange.to,
        })
      }
    }

    return (
      <Stack
        data-testid={dataTestId}
        ref={ref}
      >
        <Stack
          direction="row"
          gap={1}
        >
          <ScheduleTimePicker
            {...rest}
            dataTestId={`${dataTestId}_from`}
            label={ctIntl.formatMessage({ id: 'Start Time' })}
            value={timeRange?.from}
            onChange={(newValue) => {
              onSelectAsDateTime(newValue, 'from')
            }}
          />
          <ScheduleTimePicker
            {...rest}
            dataTestId={`${dataTestId}_to`}
            label={ctIntl.formatMessage({ id: 'End Time' })}
            value={timeRange?.to}
            onChange={(newValue) => {
              onSelectAsDateTime(newValue, 'to')
            }}
          />
        </Stack>
        {error && (
          <FormHelperText error>{ctIntl.formatMessage({ id: error })}</FormHelperText>
        )}
      </Stack>
    )
  },
)

export default ScheduleTimeRangePicker
