{"name": "cartrack", "version": "0.0.0", "cartrack_meta": {"version_with_metadata": ""}, "license": "MIT", "scripts": {"start": "corepack enable pnpm && pnpm install --frozen-lockfile --lockfile-only && nx serve", "build": "nx build", "test": "nx test", "lint": "nx lint", "e2e": "nx e2e", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "clean-start": "npx rimraf node_modules && pnpm start", "tsc-check": "nx tsc-check", "update-locales": "nx update-locales", "fleet-web-storybook": "cross-env NODE_OPTIONS=--max-old-space-size=4096 nx storybook", "workspace-generator": "nx workspace-generator", "karoo-ui-core-watch": "nx run karoo-ui-core:build:watch", "karoo-ui-core-storybook": "nx run karoo-ui-core:storybook", "fleet-web-component-test-run": "nx run fleet-web:component-test", "upgrade-nx": "nx migrate latest", "postinstall": "husky install"}, "private": true, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"scope-case": [2, "always", ["camel-case", "pascal-case", "kebab-case"]]}}, "config": {"commitizen": {"path": "@commitlint/prompt"}}, "dependencies": {"@ahooksjs/use-url-state": "3.5.0", "@ai-sdk/react": "1.2.8", "@ai-sdk/ui-utils": "1.2.7", "@changey/react-leaflet-markercluster": "4.0.0-rc1", "@dnd-kit/core": "6.1.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@formatjs/intl-durationformat": "0.7.1", "@formatjs/intl-locale": "4.2.4", "@fortawesome/fontawesome-svg-core": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/fontawesome-svg-core", "@fortawesome/free-regular-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/free-regular-svg-icons", "@fortawesome/free-solid-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/free-solid-svg-icons", "@fortawesome/pro-light-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-light-svg-icons", "@fortawesome/pro-regular-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-regular-svg-icons", "@fortawesome/pro-solid-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-solid-svg-icons", "@fortawesome/react-fontawesome": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/react-fontawesome", "@geoman-io/leaflet-geoman-free": "2.15.0", "@googlemaps/js-api-loader": "1.16.6", "@hookform/resolvers": "3.1.0", "@microsoft/signalr": "8.0.7", "@mobily/ts-belt": "3.13.1", "@mui/icons-material": "7.0.1", "@mui/lab": "7.0.0-beta.10", "@mui/material": "7.0.1", "@mui/system": "7.0.1", "@mui/types": "7.4.0", "@mui/x-data-grid": "7.28.3", "@mui/x-data-grid-generator": "7.28.3", "@mui/x-data-grid-premium": "7.28.3", "@mui/x-data-grid-pro": "7.28.3", "@mui/x-date-pickers": "7.28.3", "@mui/x-date-pickers-pro": "7.28.3", "@mui/x-license": "7.28.0", "@mui/x-tree-view": "7.28.1", "@popperjs/core": "2.11.8", "@react-dnd/shallowequal": "2.0.0", "@react-hook/resize-observer": "1.2.6", "@react-pdf/renderer": "3.4.4", "@react-spring/web": "9.7.2", "@reduxjs/toolkit": "1.3.1", "@sentry/browser": "7.118.0", "@tanstack/react-form": "1.11.2", "@tanstack/react-query": "5.76.1", "@tanstack/react-query-devtools": "5.76.1", "@tippyjs/react": "4.0.0", "@toast-ui/calendar": "2.1.3", "@toast-ui/react-calendar": "2.1.3", "@trainiac/html2canvas": "1.0.0", "@turf/boolean-point-in-polygon": "6.5.0", "@turf/helpers": "6.5.0", "@vis.gl/react-google-maps": "1.5.0", "@visx/axis": "3.12.0", "@visx/brush": "3.12.0", "@visx/curve": "3.12.0", "@visx/event": "3.12.0", "@visx/glyph": "3.12.0", "@visx/gradient": "3.12.0", "@visx/grid": "3.12.0", "@visx/group": "3.12.0", "@visx/responsive": "3.12.0", "@visx/scale": "3.12.0", "@visx/shape": "3.12.0", "@visx/tooltip": "3.12.0", "@visx/vendor": "3.12.0", "@visx/xychart": "3.12.0", "ai": "4.3.6", "bignumber.js": "9.0.0", "broadcast-channel": "4.2.0", "classnames": "2.3.1", "connected-react-router": "6.9.3", "core-js": "~3.37.0", "country-flag-icons": "1.5.7", "cropperjs": "1.5.13", "date-fns": "2.29.3", "dequal": "2.0.3", "dompurify": "2.4.0", "downloadjs": "1.4.7", "downshift": "3.2.10", "esri-leaflet": "3.0.12", "esri-leaflet-vector": "4.2.3", "exceljs": "4.4.0", "file-saver": "2.0.5", "flexboxgrid": "6.3.1", "formik": "2.2.9", "google-map-react": "2.2.1", "history": "4.10.1", "hookform-resolvers3": "npm:@hookform/resolvers@3.1.0", "html-react-parser": "5.2.3", "humanize-duration": "3.27.1", "idb-keyval": "6.1.0", "imask": "7.5.0", "immer": "6.0.9", "in-view": "0.6.1", "input-format": "0.3.8", "jsbarcode": "3.11.5", "json-stable-stringify": "1.1.1", "jspdf": "2.3.1", "jszip": "3.5.0", "jwt-decode": "4.0.0", "konva": "8.4.2", "leaflet": "1.9.4", "leaflet-editable": "1.2.0", "leaflet-path-drag": "1.9.5", "leaflet.markercluster": "1.5.3", "libphonenumber-js": "1.10.49", "lodash": "~4.17.21", "luxon": "3.5.0", "match-sorter": "2.3.0", "material-ui-popup-state": "4.1.0", "memoize-one": "5.1.1", "moment": "2.29.4", "moment-timezone": "0.5.43", "neverthrow": "8.2.0", "notistack": "3.0.1", "pdf-lib": "1.17.1", "pdfjs-dist": "3.11.174", "points-cluster": "0.1.4", "polished": "4.1.0", "posthog-js": "~1.249.3", "process": "0.11.10", "prop-types": "15.8.1", "qrcode": "1.5.3", "qrcode.react": "3.1.0", "query-string": "6.14.1", "rc-slider": "9.7.1", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-click-outside": "2.3.1", "react-compiler-runtime": "19.1.0-rc.1", "react-content-loader": "7.0.0", "react-cropper": "2.1.8", "react-datepicker": "4.2.1", "react-day-picker": "6.2.1", "react-device-detect": "1.17.0", "react-dnd": "14.0.5", "react-dnd-html5-backend": "14.1.0", "react-dom": "18.3.1", "react-dropzone": "14.2.3", "react-hook-form": "7.52.1", "react-imask": "7.5.0", "react-intl": "6.8.7", "react-is": "18.3.1", "react-konva": "18.2.4", "react-leaflet": "4.2.1", "react-markdown": "10.1.0", "react-modal": "3.12.1", "react-onclickoutside": "6.8.0", "react-otp-input": "3.0.2", "react-phone-number-input": "3.2.9", "react-pro-sidebar": "1.1.0-alpha.1", "react-redux": "7.2.5", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-scroll": "1.9.3", "react-select": "3.0.8", "react-slick": "0.30.2", "react-split": "2.0.14", "react-spring": "9.2.4", "react-switch": "6.0.0", "react-table": "6.8.6", "react-tiny-virtual-list": "2.2.0", "react-toastify": "10.0.5", "react-virtualized": "9.22.5", "react-virtualized-auto-sizer": "1.0.24", "react-window": "1.8.7", "recharts": "1.5.0", "redux-form": "8.3.6", "redux-persist": "6.0.0", "redux-saga": "1.1.3", "remark-gfm": "4.0.1", "remeda": "2.16.0", "reselect": "5.1.0", "screenfull": "4.2.0", "slick-carousel": "1.8.1", "stream-browserify": "3.0.0", "styled-components": "5.3.5", "tippy.js": "6.3.1", "ts-pattern": "5.7.1", "tslib": "^2.4.0", "type-fest": "4.26.0", "typed-redux-saga": "1.3.1", "use-effect-reducer": "0.7.0", "use-places-autocomplete": "4.0.0", "uuid": "9.0.1", "virtua": "0.33.4", "vm-browserify": "1.1.2", "xlsx": "0.16.0", "yup": "0.27.0", "zenscroll": "4.0.2", "zod": "3.25.48"}, "devDependencies": {"@4tw/cypress-drag-drop": "2.3.0", "@babel/core": "7.25.8", "@babel/plugin-syntax-jsx": "7.24.7", "@babel/plugin-syntax-typescript": "7.25.7", "@babel/preset-typescript": "7.24.7", "@commitlint/cli": "9.1.2", "@commitlint/config-conventional": "9.1.2", "@commitlint/prompt": "9.1.2", "@eslint/eslintrc": "3.3.0", "@eslint/js": "9.22.0", "@fsouza/prettierd": "0.25.3", "@ianvs/prettier-plugin-sort-imports": "4.2.1", "@jambit/eslint-plugin-typed-redux-saga": "0.4.0", "@nx/cypress": "21.2.1", "@nx/eslint": "21.2.1", "@nx/eslint-plugin": "21.2.1", "@nx/js": "21.2.1", "@nx/react": "21.2.1", "@nx/rollup": "21.2.1", "@nx/storybook": "21.2.1", "@nx/vite": "21.2.1", "@nx/web": "21.2.1", "@nx/webpack": "21.2.1", "@rspack/cli": "1.4.4", "@rspack/core": "1.4.4", "@rspack/plugin-react-refresh": "1.4.3", "@storybook/addon-essentials": "8.1.11", "@storybook/addon-interactions": "8.1.11", "@storybook/components": "8.1.11", "@storybook/core-events": "8.1.11", "@storybook/core-server": "8.1.11", "@storybook/manager-api": "8.1.11", "@storybook/react": "8.1.11", "@storybook/react-webpack5": "8.1.11", "@storybook/test": "8.1.11", "@storybook/theming": "8.1.11", "@storybook/types": "8.1.11", "@testing-library/cypress": "10.0.2", "@testing-library/react": "16.2.0", "@types/dompurify": "2.3.4", "@types/downloadjs": "1.4.1", "@types/esri-leaflet": "3.0.3", "@types/file-saver": "2.0.5", "@types/geojson": "7946.0.7", "@types/google-map-react": "2.1.10", "@types/google.maps": "3.58.1", "@types/history": "4.7.9", "@types/humanize-duration": "3.27.1", "@types/json-stable-stringify": "1.0.36", "@types/leaflet": "1.9.12", "@types/leaflet.markercluster": "1.5.4", "@types/lodash": "4.14.182", "@types/luxon": "3.4.2", "@types/match-sorter": "2.3.0", "@types/node": "18.19.14", "@types/prop-types": "15.7.13", "@types/qrcode": "1.5.0", "@types/react": "18.3.13", "@types/react-beautiful-dnd": "13.1.7", "@types/react-datepicker": "4.1.7", "@types/react-dom": "18.3.1", "@types/react-is": "18.3.0", "@types/react-redux": "~7.1.24", "@types/react-router": "5.1.18", "@types/react-router-dom": "5.3.3", "@types/react-scroll": "1.8.10", "@types/react-select": "3.0.19", "@types/react-slick": "0.23.13", "@types/react-table": "6.8.5", "@types/react-virtualized": "9.21.30", "@types/react-window": "1.8.5", "@types/recharts": "1.8.18", "@types/redux-form": "8.3.0", "@types/styled-components": "5.1.26", "@types/uuid": "9.0.8", "@types/yup": "0.26.37", "@types/zenscroll": "4.0.0", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "3.7.2", "@vitest/browser": "2.1.8", "@vitest/coverage-v8": "2.1.8", "@vitest/eslint-plugin": "1.1.38", "@vitest/ui": "2.1.8", "babel-loader": "9.2.1", "babel-plugin-react-compiler": "19.1.0-rc.1", "caniuse-lite": "1.0.30001706", "chromatic": "11.16.1", "commitizen": "2.10.1", "cross-env": "5.2.0", "csstype": "3.1.3", "cypress": "14.5.1", "cypress-multi-reporters": "1.6.4", "cypress-parallel": "0.14.0", "cypress-rspack-dev-server": "1.1.0", "dotenv": "14.3.2", "env-cmd": "10.1.0", "esbuild-plugin-react-virtualized": "1.0.4", "eslint": "9.8.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-cypress": "4.2.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-no-only-tests": "3.1.0", "eslint-plugin-no-unsanitized": "4.1.2", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "0.0.0-experimental-72135096-20250421", "eslint-plugin-risxss": "2.1.0", "eslint-plugin-sonarjs": "2.0.3", "eslint-plugin-storybook": "0.10.1", "eslint-plugin-xss": "0.1.12", "husky": "7.0.2", "jiti": "2.4.2", "jsdom": "24.0.0", "lint-staged": "10.5.2", "npm-run-all": "4.1.5", "nx": "21.2.1", "oxlint": "0.16.10", "playwright": "1.49.1", "postcss": "8.4.38", "postcss-focus": "5.0.1", "postcss-loader": "7.3.3", "postcss-preset-env": "7.8.3", "prettier": "3.1.1", "react-refresh": "0.14.2", "react-scan": "0.3.3", "rimraf": "5.0.7", "sass": "1.44.0", "sass-loader": "16.0.4", "storybook": "8.1.11", "svg-inline-loader": "0.8.2", "typescript": "5.8.3", "typescript-eslint": "8.35.1", "vite": "6.0.0", "vite-plugin-commonjs": "0.10.4", "vite-plugin-dts": "4.5.3", "vite-tsconfig-paths": "5.1.4", "vitest": "2.1.8", "vitest-browser-react": "0.0.4", "webpack": "5.98.0"}, "pnpm": {"patchedDependencies": {"redux-persist@6.0.0": "patches/<EMAIL>", "formik@2.2.9": "patches/<EMAIL>", "google-map-react@2.2.1": "patches/<EMAIL>", "react-hook-form@7.52.1": "patches/<EMAIL>", "eslint-plugin-react-hooks": "patches/eslint-plugin-react-hooks.patch", "@turf/boolean-point-in-polygon": "patches/@turf__boolean-point-in-polygon.patch", "@turf/helpers": "patches/@turf__helpers.patch", "cypress": "patches/cypress.patch", "broadcast-channel": "patches/broadcast-channel.patch", "react-imask": "patches/react-imask.patch", "@toast-ui/react-calendar": "patches/@toast-ui__react-calendar.patch", "@toast-ui/calendar": "patches/@toast-ui__calendar.patch"}, "overrides": {"eslint-plugin-react-hooks": "$eslint-plugin-react-hooks"}, "onlyBuiltDependencies": ["@fortawesome/pro-light-svg-icons", "@fortawesome/pro-regular-svg-icons", "@fortawesome/pro-solid-svg-icons", "@swc/core", "cypress", "esbuild", "msw", "nx", "core-js", "core-js-pure"], "ignoredBuiltDependencies": ["@fortawesome/fontawesome-common-types", "@parcel/watcher", "@react-spring/core", "@scarf/scarf", "canvas", "commitizen", "spawn-sync", "styled-components"]}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}